# 爱巢小记 - 智能记账与旅行规划小程序

<div align="center">
  <img src="https://img.shields.io/badge/微信小程序-原生开发-green" alt="微信小程序">
  <img src="https://img.shields.io/badge/云开发-微信云开发-blue" alt="云开发">
  <img src="https://img.shields.io/badge/UI框架-Vant Weapp-orange" alt="Vant Weapp">
  <img src="https://img.shields.io/badge/语音识别-腾讯云ASR-red" alt="语音识别">
</div>

## 📱 项目简介

**爱巢小记**是一款集智能记账和旅行规划于一体的微信小程序，采用现代化设计理念，为用户提供简洁美观的生活记录体验。

### ✨ 核心功能

- **💰 智能记账**
  - 语音识别记账，自动分类和金额识别
  - 财务分析和预算管理
  - 消费趋势可视化图表

- **✈️ 旅行规划**
  - 旅行预算管理和行程记录
  - 计划创建和协作管理
  - 花费统计和预算控制

- **👤 用户系统**
  - 支持游客模式和正式用户
  - 数据云端同步
  - 头像和个性化设置

- **🎨 主题系统**
  - 浅色/深色/跟随系统三种主题模式
  - 毛玻璃风格设计
  - 现代化UI交互

- **📊 数据分析**
  - 消费趋势分析
  - 预算使用情况
  - 可视化图表展示

- **🔔 通知系统**
  - 预算提醒
  - 计划通知
  - 系统消息推送

## 🏗️ 技术架构

### 前端技术栈
- **框架**: 微信小程序原生开发
- **UI组件库**: Vant Weapp + 自定义组件
- **图标系统**: SVG图标文件 + 渐变色彩设计
- **数据可视化**: 微信小程序ECharts插件
- **状态管理**: 本地存储 + 云端同步

### 后端技术栈
- **云服务**: 微信云开发
- **数据库**: 云数据库
- **存储**: 云存储
- **函数**: 云函数
- **语音识别**: 腾讯云语音识别API

### 设计规范
- **配色方案**: 粉色系主色调 + 渐变背景
- **交互规范**: 圆角设计 + 毛玻璃效果
- **动画**: 0.3s ease过渡动画
- **字体**: 响应式字体系统

## 📁 项目结构

```
爱巢小记/
├── pages/                 # 主包页面
│   ├── index/             # 首页
│   ├── login/             # 登录页
│   └── profile/           # 个人中心
├── subpackages/           # 分包
│   ├── account/           # 记账管理
│   ├── travel-planning/   # 旅行规划
│   ├── social/            # 社交功能
│   └── settings/          # 设置中心
├── components/            # 自定义组件
│   ├── icon/              # SVG图标组件
│   ├── voice-recorder-tencent/ # 腾讯语音录音组件
│   └── vant-icon-fix/     # Vant图标修复组件
├── images/                # SVG图标资源
├── cloud/                 # 云开发
│   └── functions/         # 云函数
├── styles/                # 样式文件
└── utils/                 # 工具函数
```

## 🚀 快速开始

### 环境要求
- 微信开发者工具 (最新版本)
- Node.js (推荐 16.x 或更高版本)
- 微信小程序账号

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/你的用户名/ai-chao-xiao-ji.git
cd ai-chao-xiao-ji
```

2. **安装依赖**
```bash
npm install
```

3. **微信开发者工具配置**
   - 打开微信开发者工具
   - 导入项目，选择项目根目录
   - 配置AppID（需要有自己的小程序账号）

4. **云开发配置**
   - 在微信开发者工具中开通云开发
   - 创建云环境
   - 部署云函数到云端

5. **语音识别配置**
   - 申请腾讯云语音识别服务
   - 在云函数中配置相关密钥

### 开发指南

详细的开发指南请参考：[DEVELOPMENT_GUIDE.md](./DEVELOPMENT_GUIDE.md)

## 🎨 设计特色

### 配色方案
```css
/* 主色调 - 渐变背景 */
background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);

/* 品牌色彩 */
主色调: #FFB6C1 (粉色系)
辅助色: #4ECDC4 (青绿色)
功能色: #45B7D1 (蓝色)
装饰色: #6c5ce7 (紫色)
```

### 交互规范
- **圆角**: 卡片32rpx，按钮16rpx，小元素8rpx
- **间距**: 基于8rpx倍数的间距系统
- **动画**: 0.3s ease过渡动画
- **阴影**: 毛玻璃效果 + backdrop-filter: blur(20rpx)

## 📝 功能特性

### 智能记账
- 语音识别记账，支持多种方言
- 自动分类和金额识别
- 预算管理和提醒
- 消费趋势分析

### 旅行规划
- 旅行计划创建和管理
- 预算控制和花费统计
- 行程记录和分享
- 协作功能

### 用户体验
- 三种主题模式切换
- 毛玻璃风格设计
- 流畅的动画效果
- 响应式布局

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 ISC 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目主页: [GitHub](https://github.com/你的用户名/ai-chao-xiao-ji)
- 问题反馈: [Issues](https://github.com/你的用户名/ai-chao-xiao-ji/issues)

## 🙏 致谢

感谢以下开源项目的支持：
- [Vant Weapp](https://github.com/youzan/vant-weapp) - UI组件库
- [ECharts](https://echarts.apache.org/) - 数据可视化
- [微信小程序](https://developers.weixin.qq.com/miniprogram/dev/) - 开发框架

---

<div align="center">
  <p>如果这个项目对你有帮助，请给个 ⭐️ 支持一下！</p>
</div> 