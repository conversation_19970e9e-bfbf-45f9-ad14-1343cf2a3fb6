# 贡献指南

感谢您对爱巢小记项目的关注！我们欢迎所有形式的贡献。

## 🤝 如何贡献

### 报告 Bug

如果您发现了一个 bug，请通过以下步骤报告：

1. 检查是否已经有相关的 issue
2. 创建一个新的 issue
3. 使用 bug 模板，并包含以下信息：
   - 详细的 bug 描述
   - 重现步骤
   - 预期行为
   - 实际行为
   - 环境信息（操作系统、微信版本等）

### 功能请求

如果您有新的功能想法，请：

1. 检查是否已经有相关的 issue
2. 创建一个新的 issue
3. 使用 feature request 模板
4. 详细描述功能需求和用例

### 代码贡献

#### 开发环境设置

1. Fork 本仓库
2. 克隆您的 fork：
   ```bash
   git clone https://github.com/您的用户名/ai-chao-xiao-ji.git
   cd ai-chao-xiao-ji
   ```
3. 安装依赖：
   ```bash
   npm install
   ```
4. 创建功能分支：
   ```bash
   git checkout -b feature/your-feature-name
   ```

#### 开发规范

1. **代码风格**
   - 使用 2 个空格缩进
   - 使用单引号
   - 行尾不要有分号
   - 使用 ES6+ 语法

2. **提交信息**
   - 使用中文描述
   - 格式：`类型: 简短描述`
   - 类型包括：feat, fix, docs, style, refactor, test, chore

3. **测试**
   - 确保代码在微信开发者工具中正常运行
   - 测试相关功能是否正常工作

#### 提交 Pull Request

1. 确保您的代码符合项目规范
2. 提交您的更改：
   ```bash
   git add .
   git commit -m "feat: 添加新功能"
   git push origin feature/your-feature-name
   ```
3. 在 GitHub 上创建 Pull Request
4. 填写 PR 模板，详细描述您的更改

## 📋 开发指南

### 项目结构

- `pages/` - 主包页面
- `subpackages/` - 分包页面
- `components/` - 自定义组件
- `utils/` - 工具函数
- `styles/` - 样式文件
- `cloud/` - 云开发相关

### 代码规范

#### JavaScript/TypeScript
```javascript
// 使用 const 和 let，避免 var
const user = {
  name: '张三',
  age: 25
};

// 使用箭头函数
const getUserInfo = () => {
  return user;
};

// 使用模板字符串
const message = `欢迎 ${user.name}！`;
```

#### WXML
```xml
<!-- 使用语义化标签 -->
<view class="container">
  <text class="title">{{title}}</text>
  <button class="btn" bindtap="handleClick">点击</button>
</view>
```

#### WXSS
```css
/* 使用 BEM 命名规范 */
.container {
  padding: 32rpx;
}

.container__title {
  font-size: 40rpx;
  color: #333;
}

.container__btn {
  margin-top: 24rpx;
}
```

### 组件开发

1. **组件结构**
   ```
   components/
   └── my-component/
       ├── index.js
       ├── index.json
       ├── index.wxml
       └── index.wxss
   ```

2. **组件规范**
   - 使用 props 传递数据
   - 使用 events 传递事件
   - 添加适当的注释

### 云函数开发

1. **函数结构**
   ```
   cloud/functions/
   └── my-function/
       ├── index.js
       ├── package.json
       └── config.json
   ```

2. **开发规范**
   - 使用 async/await
   - 添加错误处理
   - 返回标准格式的响应

## 🎯 贡献类型

### 代码贡献
- 修复 bug
- 添加新功能
- 优化性能
- 改进代码质量

### 文档贡献
- 更新 README
- 编写开发文档
- 翻译文档
- 添加注释

### 设计贡献
- UI/UX 改进
- 图标设计
- 动画效果
- 主题优化

### 测试贡献
- 编写测试用例
- 进行功能测试
- 性能测试
- 兼容性测试

## 📞 联系我们

如果您有任何问题或建议，请通过以下方式联系我们：

- 创建 GitHub Issue
- 发送邮件到项目维护者
- 加入项目讨论群

## 🙏 致谢

感谢所有为项目做出贡献的开发者！

---

**注意**: 请确保您的贡献符合项目的许可证要求。 