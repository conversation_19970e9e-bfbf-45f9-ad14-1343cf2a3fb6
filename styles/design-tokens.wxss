/**
 * 爱巢小记统一设计令牌系统 v2.0
 * 基于深度分析的完整设计体系
 */
:root {
  /* === 品牌色彩系统（基于实际使用的#FF6B6B） === */
  --brand-primary: #FF6B6B;
  --brand-primary-light: #FF8E8E;
  --brand-primary-dark: #E55A5A;
  --brand-primary-alpha-10: rgba(255, 107, 107, 0.1);
  --brand-primary-alpha-20: rgba(255, 107, 107, 0.2);
  --brand-primary-alpha-30: rgba(255, 107, 107, 0.3);
  
  --brand-secondary: #4ECDC4;
  --brand-secondary-light: #6DD5CD;
  --brand-secondary-dark: #3CBDB4;
  --brand-secondary-alpha-10: rgba(78, 205, 196, 0.1);
  --brand-secondary-alpha-20: rgba(78, 205, 196, 0.2);
  
  --brand-accent: #45B7D1;
  --brand-accent-light: #67C3D6;
  --brand-accent-dark: #3AA7C1;
  --brand-accent-alpha-10: rgba(69, 183, 209, 0.1);
  --brand-accent-alpha-20: rgba(69, 183, 209, 0.2);
  
  /* === 渐变系统 === */
  --gradient-primary: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  --gradient-primary-reverse: linear-gradient(315deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  --gradient-card: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(78, 205, 196, 0.1) 100%);
  --gradient-button: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 50%, #FFB6C1 100%);
  
  /* === 功能色彩（确保WCAG AA对比度） === */
  --color-success: #52c41a;
  --color-success-light: #73d13d;
  --color-success-dark: #389e0d;
  --color-warning: #faad14;
  --color-warning-light: #ffc53d;
  --color-warning-dark: #d48806;
  --color-error: #ff4d4f;
  --color-error-light: #ff7875;
  --color-error-dark: #cf1322;
  --color-info: #1890ff;
  --color-info-light: #40a9ff;
  --color-info-dark: #096dd9;
  
  /* === 中性色彩（确保WCAG AA对比度） === */
  --text-primary: rgba(0, 0, 0, 0.87);     /* 对比度 > 4.5:1 */
  --text-secondary: rgba(0, 0, 0, 0.65);   /* 对比度 > 3:1 */
  --text-tertiary: rgba(0, 0, 0, 0.45);
  --text-disabled: rgba(0, 0, 0, 0.25);
  --text-white: #FFFFFF;
  --text-white-secondary: rgba(255, 255, 255, 0.85);
  --text-white-tertiary: rgba(255, 255, 255, 0.65);
  --text-white-disabled: rgba(255, 255, 255, 0.45);
  
  /* === 背景色彩 === */
  --bg-primary: #FFFFFF;
  --bg-secondary: #FAFBFC;
  --bg-tertiary: #F5F6FA;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  --bg-overlay-light: rgba(0, 0, 0, 0.3);
  --bg-overlay-heavy: rgba(0, 0, 0, 0.7);
  
  /* === 统一间距系统（8rpx递增） === */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 48rpx;
  --spacing-xxl: 64rpx;
  --spacing-xxxl: 96rpx;
  
  /* === 统一圆角系统（4rpx递增） === */
  --radius-xs: 8rpx;
  --radius-sm: 12rpx;
  --radius-md: 16rpx;
  --radius-lg: 24rpx;
  --radius-xl: 32rpx;
  --radius-xxl: 48rpx;
  --radius-round: 50%;
  
  /* === 统一字体系统 === */
  --font-xs: 20rpx;
  --font-sm: 24rpx;
  --font-md: 28rpx;
  --font-lg: 32rpx;
  --font-xl: 36rpx;
  --font-xxl: 40rpx;
  --font-xxxl: 48rpx;
  
  /* === 字重系统 === */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* === 行高系统 === */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  --line-height-loose: 1.8;
  
  /* === 统一阴影系统 === */
  --shadow-xs: 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
  --shadow-sm: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  --shadow-md: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 16rpx 48rpx rgba(0, 0, 0, 0.16);
  --shadow-xl: 0 24rpx 64rpx rgba(0, 0, 0, 0.20);
  --shadow-2xl: 0 32rpx 96rpx rgba(0, 0, 0, 0.25);
  
  /* === 品牌阴影系统 === */
  --shadow-primary: 0 12rpx 40rpx var(--brand-primary-alpha-20);
  --shadow-secondary: 0 12rpx 40rpx var(--brand-secondary-alpha-20);
  --shadow-accent: 0 12rpx 40rpx var(--brand-accent-alpha-20);
  
  /* === 动画系统 === */
  --duration-instant: 0.1s;
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  --duration-slower: 0.8s;
  
  /* === 缓动函数系统 === */
  --easing-linear: linear;
  --easing-standard: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-decelerate: cubic-bezier(0, 0, 0.2, 1);
  --easing-accelerate: cubic-bezier(0.4, 0, 1, 1);
  --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --easing-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  /* === Z-index系统 === */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}
