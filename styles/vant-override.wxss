/* Vant组件样式覆盖 - 适配爱巢小记设计风格 */

/* ===== 禁用在线字体加载 ===== */
@font-face {
  font-family: 'vant-icon';
  src: none !important;
}

/* ===== Vant组件全局样式适配 ===== */

/* Cell组件适配 - 使用统一毛玻璃效果 */
.van-cell-group {
  background: rgba(255, 255, 255, 0.15) !important;
  /* 移除重复的backdrop-filter，使用统一标准 */
  border-radius: 24rpx !important;
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
  box-shadow:
    0 16rpx 64rpx rgba(0, 0, 0, 0.1),
    0 2rpx 0 rgba(255, 255, 255, 0.3) inset !important;
  margin: 32rpx 24rpx !important;
  overflow: hidden;
}

/* 应用统一毛玻璃效果 */
.van-cell-group {
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
}

.van-cell {
  background: transparent !important;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1) !important;
  padding: 24rpx 32rpx !important;
}

.van-cell:last-child {
  border-bottom: none !important;
}

.van-cell__title {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500 !important;
}

.van-cell__label {
  color: rgba(255, 255, 255, 0.6) !important;
}

.van-cell__value {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Button组件现代化适配 */
.van-button {
  border-radius: 20rpx !important;
  backdrop-filter: blur(10rpx) !important;
  -webkit-backdrop-filter: blur(10rpx) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-weight: 600 !important;
  position: relative !important;
  overflow: hidden !important;
}

/* 按钮悬停光效 */
.van-button::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
  transition: left 0.6s ease !important;
  z-index: 1 !important;
}

.van-button:active::before {
  left: 100% !important;
}

.van-button--primary {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 50%, #FFB6C1 100%) !important;
  border: none !important;
  box-shadow:
    0 12rpx 40rpx rgba(255, 107, 107, 0.25),
    0 4rpx 16rpx rgba(255, 107, 107, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3) !important;
}

.van-button--primary:active {
  transform: translateY(2rpx) scale(0.98) !important;
  box-shadow:
    0 8rpx 24rpx rgba(255, 107, 107, 0.3),
    0 2rpx 8rpx rgba(255, 107, 107, 0.2) !important;
}

.van-button--info {
  background: linear-gradient(135deg, #4ECDC4 0%, #45B7D1 100%) !important;
  border: none !important;
  box-shadow:
    0 12rpx 40rpx rgba(78, 205, 196, 0.25),
    0 4rpx 16rpx rgba(78, 205, 196, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3) !important;
}

.van-button--info:active {
  transform: translateY(2rpx) scale(0.98) !important;
  box-shadow:
    0 8rpx 24rpx rgba(78, 205, 196, 0.3),
    0 2rpx 8rpx rgba(78, 205, 196, 0.2) !important;
}

.van-button--default {
  background: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  border: 2rpx solid rgba(255, 255, 255, 0.3) !important;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2) !important;
}

.van-button--default:active {
  transform: translateY(2rpx) scale(0.98) !important;
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
}

.van-button--danger {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%) !important;
  border: none !important;
  box-shadow:
    0 12rpx 40rpx rgba(255, 77, 79, 0.25),
    0 4rpx 16rpx rgba(255, 77, 79, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3) !important;
}

.van-button--danger:active {
  transform: translateY(2rpx) scale(0.98) !important;
  box-shadow:
    0 8rpx 24rpx rgba(255, 77, 79, 0.3),
    0 2rpx 8rpx rgba(255, 77, 79, 0.2) !important;
}

.van-button--success {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%) !important;
  border: none !important;
  box-shadow:
    0 12rpx 40rpx rgba(82, 196, 26, 0.25),
    0 4rpx 16rpx rgba(82, 196, 26, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3) !important;
}

.van-button--success:active {
  transform: translateY(2rpx) scale(0.98) !important;
  box-shadow:
    0 8rpx 24rpx rgba(82, 196, 26, 0.3),
    0 2rpx 8rpx rgba(82, 196, 26, 0.2) !important;
}

.van-button--warning {
  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%) !important;
  border: none !important;
  box-shadow:
    0 12rpx 40rpx rgba(250, 173, 20, 0.25),
    0 4rpx 16rpx rgba(250, 173, 20, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3) !important;
}

.van-button--warning:active {
  transform: translateY(2rpx) scale(0.98) !important;
  box-shadow:
    0 8rpx 24rpx rgba(250, 173, 20, 0.3),
    0 2rpx 8rpx rgba(250, 173, 20, 0.2) !important;
}

/* Grid组件适配 - 使用统一毛玻璃效果 */
.van-grid {
  background: rgba(255, 255, 255, 0.15) !important;
  /* 使用统一毛玻璃效果 */
  backdrop-filter: blur(24rpx) saturate(180%) !important;
  -webkit-backdrop-filter: blur(24rpx) saturate(180%) !important;
  border-radius: 24rpx !important;
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
  box-shadow:
    0 16rpx 64rpx rgba(0, 0, 0, 0.1),
    0 2rpx 0 rgba(255, 255, 255, 0.3) inset !important;
  margin: 32rpx 24rpx !important;
  overflow: hidden;
}

.van-grid-item {
  background: transparent !important;
}

.van-grid-item__content {
  background: transparent !important;
  padding: 32rpx !important;
}

.van-grid-item__text {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500 !important;
}

/* Card组件适配 - 使用统一毛玻璃效果 */
.van-card {
  background: rgba(255, 255, 255, 0.15) !important;
  /* 使用统一毛玻璃效果 */
  backdrop-filter: blur(24rpx) saturate(180%) !important;
  -webkit-backdrop-filter: blur(24rpx) saturate(180%) !important;
  border-radius: 24rpx !important;
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
  box-shadow:
    0 16rpx 64rpx rgba(0, 0, 0, 0.1),
    0 2rpx 0 rgba(255, 255, 255, 0.3) inset !important;
  margin: 32rpx 24rpx !important;
}

.van-card__header {
  background: transparent !important;
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 600 !important;
  padding: 32rpx 32rpx 16rpx !important;
}

.van-card__content {
  background: transparent !important;
  padding: 16rpx 32rpx 32rpx !important;
}

.van-card__title {
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 600 !important;
}

.van-card__desc {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* Progress组件适配 */
.van-progress {
  margin: 24rpx 0 !important;
}

.van-progress__portion {
  background: linear-gradient(135deg, #FF6B6B, #FF8E8E) !important;
  border-radius: 8rpx !important;
}

.van-progress__track {
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 8rpx !important;
}

/* Tag组件适配 */
.van-tag {
  backdrop-filter: blur(10rpx);
  border-radius: 8rpx !important;
}

.van-tag--primary {
  background: rgba(255, 107, 107, 0.8) !important;
  color: #fff !important;
}

.van-tag--success {
  background: rgba(82, 196, 26, 0.8) !important;
  color: #fff !important;
}

.van-tag--warning {
  background: rgba(250, 173, 20, 0.8) !important;
  color: #fff !important;
}

.van-tag--info {
  background: rgba(78, 205, 196, 0.8) !important;
  color: #fff !important;
}

/* Image组件适配 */
.van-image {
  border-radius: 12rpx !important;
  overflow: hidden;
}

.van-image--round {
  border-radius: 50% !important;
}

/* Loading组件适配 */
.van-loading {
  color: rgba(255, 255, 255, 0.8) !important;
}

.van-loading__spinner {
  color: inherit !important;
}

/* Toast组件适配 - 移除重复backdrop-filter */
.van-toast {
  background: rgba(0, 0, 0, 0.8) !important;
  border-radius: 16rpx !important;
  /* 使用统一毛玻璃效果 */
  backdrop-filter: blur(24rpx) saturate(180%) !important;
  -webkit-backdrop-filter: blur(24rpx) saturate(180%) !important;
}

.van-toast__text {
  color: #fff !important;
}

/* Skeleton组件适配 */
.van-skeleton {
  background: transparent !important;
}

.van-skeleton__row {
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 8rpx !important;
}

.van-skeleton__avatar {
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 50% !important;
}

.van-skeleton__title {
  background: rgba(255, 255, 255, 0.15) !important;
  border-radius: 8rpx !important;
}

/* Icon组件字体修复 */
.van-icon {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
}

/* 优化字体渲染，防止重影 */
page, view, text, button, input, textarea, picker, navigator, image {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
  /* 优化字体渲染，减少模糊 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 防止字体变形 */
  text-rendering: optimizeLegibility;
}

/* ===== Dialog 弹窗样式增强 - 统一毛玻璃效果 ===== */
.van-dialog {
  border-radius: 24rpx !important;
  overflow: hidden !important;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.3) !important;
  /* 使用统一毛玻璃效果 */
  backdrop-filter: blur(24rpx) saturate(180%) !important;
  -webkit-backdrop-filter: blur(24rpx) saturate(180%) !important;
}

.van-dialog__header {
  padding: 32rpx 32rpx 16rpx !important;
  background: #ffffff !important;
  border-bottom: 1rpx solid #f0f0f0 !important;
}

.van-dialog__title {
  font-size: 36rpx !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
}

.van-dialog__content {
  padding: 24rpx 32rpx 32rpx !important;
  background: #ffffff !important;
}

.van-dialog__message {
  font-size: 30rpx !important;
  line-height: 1.6 !important;
  color: #495057 !important;
}

.van-dialog__footer {
  background: #ffffff !important;
  border-top: 1rpx solid #f0f0f0 !important;
  display: flex !important;
}

.van-dialog__footer .van-button {
  flex: 1 !important;
  height: 96rpx !important;
  border-radius: 0 !important;
  font-size: 34rpx !important;
  font-weight: 700 !important;
  border: none !important;
  margin: 0 !important;
}

.van-dialog__footer .van-button--default {
  background: #f8f9fa !important;
  color: #495057 !important;
  border-right: 1rpx solid #f0f0f0 !important;
}

.van-dialog__footer .van-button--default:active {
  background: #e9ecef !important;
  color: #343a40 !important;
}

.van-dialog__footer .van-button:not(.van-button--default) {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%) !important;
  color: #ffffff !important;
  box-shadow: inset 0 1rpx 0 rgba(255, 255, 255, 0.2) !important;
}

.van-dialog__footer .van-button:not(.van-button--default):active {
  background: linear-gradient(135deg, #d9363e 0%, #ff595f 100%) !important;
  box-shadow: inset 0 1rpx 0 rgba(255, 255, 255, 0.1) !important;
}
