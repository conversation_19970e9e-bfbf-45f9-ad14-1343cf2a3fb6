/**
 * 统一毛玻璃效果系统 - 唯一标准实现
 * 解决重影问题：统一blur值为24rpx，移除重复定义
 */

/* === 基础毛玻璃效果 === */
.glass-base {
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  position: relative;
  overflow: hidden;
  /* 防止文字模糊 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* === 毛玻璃卡片系统 === */
.glass-card {
  background: rgba(255, 255, 255, 0.12);
  border: 1rpx solid rgba(255, 255, 255, 0.18);
  border-radius: var(--radius-lg);
  box-shadow: 
    var(--shadow-md),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.25);
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.4) 50%, 
    transparent 100%);
}

/* === 三种强度的毛玻璃效果 === */
.glass-subtle {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(16rpx) saturate(160%);
  -webkit-backdrop-filter: blur(16rpx) saturate(160%);
  border: 1rpx solid rgba(255, 255, 255, 0.12);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.glass-normal {
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.18);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.glass-strong {
  background: rgba(255, 255, 255, 0.18);
  backdrop-filter: blur(32rpx) saturate(200%);
  -webkit-backdrop-filter: blur(32rpx) saturate(200%);
  border: 1rpx solid rgba(255, 255, 255, 0.25);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

/* === 深色毛玻璃效果 === */
.glass-dark {
  background: rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

/* === 品牌色毛玻璃效果 === */
.glass-primary {
  background: var(--brand-primary-alpha-10);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid var(--brand-primary-alpha-20);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-primary);
  position: relative;
  overflow: hidden;
}

.glass-secondary {
  background: var(--brand-secondary-alpha-10);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid var(--brand-secondary-alpha-20);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-secondary);
  position: relative;
  overflow: hidden;
}

.glass-accent {
  background: var(--brand-accent-alpha-10);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid var(--brand-accent-alpha-20);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-accent);
  position: relative;
  overflow: hidden;
}

/* === 毛玻璃效果增强 === */
.glass-subtle::before,
.glass-normal::before,
.glass-strong::before,
.glass-dark::before,
.glass-primary::before,
.glass-secondary::before,
.glass-accent::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.4) 50%, 
    transparent 100%);
  z-index: 1;
}

/* === 毛玻璃内容区域 === */
.glass-content {
  position: relative;
  z-index: 2;
  padding: var(--spacing-lg);
}

/* === 毛玻璃标题区域 === */
.glass-header {
  position: relative;
  z-index: 2;
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

/* === 毛玻璃底部区域 === */
.glass-footer {
  position: relative;
  z-index: 2;
  padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
}

/* === 响应式毛玻璃效果 === */
@media (max-width: 750rpx) {
  .glass-content,
  .glass-header,
  .glass-footer {
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }
}

/* === 可读性优化毛玻璃系统 === */
.glass-readable-subtle {
  background: rgba(255, 255, 255, 0.32);
  backdrop-filter: blur(16rpx) saturate(160%);
  -webkit-backdrop-filter: blur(16rpx) saturate(160%);
  border: 1rpx solid rgba(255, 255, 255, 0.38);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm), inset 0 1rpx 0 rgba(255, 255, 255, 0.45);
  position: relative;
  overflow: hidden;
}

.glass-readable-normal {
  background: rgba(255, 255, 255, 0.42);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.48);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md), inset 0 1rpx 0 rgba(255, 255, 255, 0.55);
  position: relative;
  overflow: hidden;
}

.glass-readable-strong {
  background: rgba(255, 255, 255, 0.52);
  backdrop-filter: blur(32rpx) saturate(200%);
  -webkit-backdrop-filter: blur(32rpx) saturate(200%);
  border: 1rpx solid rgba(255, 255, 255, 0.58);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg), inset 0 1rpx 0 rgba(255, 255, 255, 0.65);
  position: relative;
  overflow: hidden;
}

/* === 可读性文字颜色系统 - 防重影优化 === */
.glass-text-primary {
  color: rgba(0, 0, 0, 0.88);
  /* 移除text-shadow防止重影 */
  font-weight: var(--font-weight-medium);
  /* 优化字体渲染 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.glass-text-secondary {
  color: rgba(0, 0, 0, 0.68);
  /* 移除text-shadow防止重影 */
  font-weight: var(--font-weight-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.glass-text-white {
  color: rgba(255, 255, 255, 0.96);
  /* 保留轻微阴影提升可读性 */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.15);
  font-weight: var(--font-weight-medium);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.glass-text-brand {
  color: var(--brand-primary);
  /* 移除text-shadow防止重影 */
  font-weight: var(--font-weight-semibold);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* === 交互动画系统 === */
.glass-btn-interactive {
  transition: all var(--duration-normal) var(--easing-standard);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transform-origin: center;
}

.glass-btn-interactive::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.28);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.65s var(--easing-decelerate),
              height 0.65s var(--easing-decelerate);
  pointer-events: none;
  z-index: 1;
}

.glass-btn-interactive:active::before {
  width: 280rpx;
  height: 280rpx;
}

.glass-btn-interactive:active {
  transform: translateY(1.5rpx) scale(0.975);
  background: rgba(255, 255, 255, 0.48);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-fast) var(--easing-bounce);
}

.glass-btn-interactive text,
.glass-btn-interactive view,
.glass-btn-interactive image {
  position: relative;
  z-index: 2;
}

@media (hover: hover) {
  .glass-btn-interactive:hover {
    transform: translateY(-1.5rpx);
    box-shadow: var(--shadow-lg);
    background: rgba(255, 255, 255, 0.38);
    transition: all var(--duration-normal) var(--easing-standard);
  }
}

/* === 深色模式适配 === */
@media (prefers-color-scheme: dark) {
  .glass-card,
  .glass-subtle,
  .glass-normal,
  .glass-strong {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .glass-card::before,
  .glass-subtle::before,
  .glass-normal::before,
  .glass-strong::before {
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.2) 50%,
      transparent 100%);
  }

  .glass-readable-subtle,
  .glass-readable-normal,
  .glass-readable-strong {
    background: rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.15);
  }

  .glass-text-primary {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.6);
  }

  .glass-text-secondary {
    color: rgba(255, 255, 255, 0.75);
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.4);
  }
}

/* === 输入框重影修复（从anti-ghosting-fixes.wxss迁移） === */
.input-safe {
  -webkit-appearance: none;
  appearance: none;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* === 媒体查询重影修复 === */
@media (prefers-reduced-motion: reduce) {
  .animation-safe,
  .transform-hover-safe,
  .transform-active-safe,
  .glass-btn-interactive {
    animation: none !important;
    transform: none !important;
    transition: none !important;
  }
}

/* === 高DPI屏幕重影修复 === */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .glass-text-primary,
  .glass-text-secondary {
    -webkit-font-smoothing: subpixel-antialiased;
  }
}
