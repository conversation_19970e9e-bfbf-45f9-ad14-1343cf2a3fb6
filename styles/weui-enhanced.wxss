/**
 * WeUI 规范增强样式
 * 基于微信官方WeUI设计规范，结合爱巢小记品牌特色
 * 参考：WeUI.sketch 设计规范
 */

/* ==================== WeUI 基础规范 ==================== */

/* 间距系统 - 基于WeUI 8rpx基础单位 */
:root {
  /* WeUI标准间距 */
  --weui-spacing-xs: 8rpx;    /* 最小间距 */
  --weui-spacing-sm: 16rpx;   /* 小间距 */
  --weui-spacing-md: 24rpx;   /* 中等间距 */
  --weui-spacing-lg: 32rpx;   /* 大间距 */
  --weui-spacing-xl: 48rpx;   /* 超大间距 */
  
  /* WeUI字体规范 */
  --weui-font-size-xs: 20rpx;   /* 辅助文字 */
  --weui-font-size-sm: 24rpx;   /* 次要文字 */
  --weui-font-size-md: 28rpx;   /* 正文 */
  --weui-font-size-lg: 32rpx;   /* 小标题 */
  --weui-font-size-xl: 36rpx;   /* 大标题 */
  --weui-font-size-xxl: 40rpx;  /* 主标题 */
  
  /* WeUI颜色规范 */
  --weui-text-primary: #000000;      /* 主要文字 */
  --weui-text-secondary: #888888;    /* 次要文字 */
  --weui-text-placeholder: #CCCCCC;  /* 占位文字 */
  --weui-text-disabled: #DDDDDD;     /* 禁用文字 */
  
  /* WeUI背景色 */
  --weui-bg-primary: #FFFFFF;        /* 主背景 */
  --weui-bg-secondary: #F7F7F7;      /* 次背景 */
  --weui-bg-tertiary: #EDEDED;       /* 三级背景 */
  
  /* WeUI边框 */
  --weui-border-color: #E5E5E5;      /* 边框色 */
  --weui-border-radius: 8rpx;        /* 圆角 */
  
  /* WeUI阴影 */
  --weui-shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  --weui-shadow-medium: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  --weui-shadow-heavy: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

/* ==================== WeUI 组件样式 ==================== */

/* WeUI 单元格组件 */
.weui-cell {
  display: flex;
  align-items: center;
  padding: var(--weui-spacing-md) var(--weui-spacing-lg);
  background: var(--weui-bg-primary);
  border-bottom: 1rpx solid var(--weui-border-color);
  min-height: 88rpx;
  box-sizing: border-box;
}

.weui-cell:last-child {
  border-bottom: none;
}

.weui-cell__icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: var(--weui-spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.weui-cell__bd {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.weui-cell__ft {
  display: flex;
  align-items: center;
  color: var(--weui-text-secondary);
  font-size: var(--weui-font-size-sm);
}

.weui-cell__title {
  font-size: var(--weui-font-size-md);
  color: var(--weui-text-primary);
  line-height: 1.4;
}

.weui-cell__desc {
  font-size: var(--weui-font-size-xs);
  color: var(--weui-text-secondary);
  margin-top: 4rpx;
  line-height: 1.3;
}

/* WeUI 按钮组件 */
.weui-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  border-radius: var(--weui-border-radius);
  font-size: var(--weui-font-size-md);
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
  min-height: 88rpx;
  box-sizing: border-box;
}

.weui-btn_primary {
  background: linear-gradient(135deg, #FF6B6B, #FF8FAB);
  color: white;
  box-shadow: var(--weui-shadow-light);
}

.weui-btn_primary:active {
  background: linear-gradient(135deg, #FF5252, #FF7A9A);
  transform: scale(0.98);
}

.weui-btn_default {
  background: var(--weui-bg-secondary);
  color: var(--weui-text-primary);
  border: 1rpx solid var(--weui-border-color);
}

.weui-btn_default:active {
  background: var(--weui-bg-tertiary);
}

.weui-btn_plain {
  background: transparent;
  color: #4ECDC4;
  border: 1rpx solid #4ECDC4;
}

.weui-btn_plain:active {
  background: rgba(78, 205, 196, 0.1);
}

/* WeUI 输入框组件 */
.weui-input {
  width: 100%;
  padding: var(--weui-spacing-md);
  font-size: var(--weui-font-size-md);
  color: var(--weui-text-primary);
  background: var(--weui-bg-primary);
  border: 1rpx solid var(--weui-border-color);
  border-radius: var(--weui-border-radius);
  box-sizing: border-box;
}

.weui-input:focus {
  border-color: #4ECDC4;
  outline: none;
}

.weui-input::placeholder {
  color: var(--weui-text-placeholder);
}

/* WeUI 卡片组件 */
.weui-panel {
  background: var(--weui-bg-primary);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--weui-shadow-light);
  margin-bottom: var(--weui-spacing-md);
}

.weui-panel__hd {
  padding: var(--weui-spacing-lg);
  border-bottom: 1rpx solid var(--weui-border-color);
  background: var(--weui-bg-secondary);
}

.weui-panel__title {
  font-size: var(--weui-font-size-lg);
  font-weight: 600;
  color: var(--weui-text-primary);
}

.weui-panel__bd {
  padding: 0;
}

/* ==================== 爱巢小记品牌增强 ==================== */

/* 毛玻璃效果增强 - 移除重复定义，使用统一标准 */
.weui-glass {
  background: rgba(255, 255, 255, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  /* 使用统一毛玻璃效果，避免重复定义 */
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
}

.weui-glass-card {
  background: rgba(255, 255, 255, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  /* 使用统一毛玻璃效果 */
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
}

/* 渐变背景 */
.weui-gradient-bg {
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
}

/* 品牌色彩按钮 */
.weui-btn_brand-primary {
  background: linear-gradient(135deg, #FF6B6B, #FF8FAB);
  color: white;
  border: none;
  box-shadow: var(--weui-shadow-medium);
}

.weui-btn_brand-secondary {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
  border: none;
  box-shadow: var(--weui-shadow-medium);
}

.weui-btn_brand-accent {
  background: linear-gradient(135deg, #45B7D1, #40a9ff);
  color: white;
  border: none;
  box-shadow: var(--weui-shadow-medium);
}

/* 现代化卡片 */
.weui-modern-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: var(--weui-spacing-lg);
  box-shadow: var(--weui-shadow-medium);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 统计卡片 */
.weui-stat-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: var(--weui-spacing-lg);
  position: relative;
  overflow: hidden;
}

.weui-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(90deg, #FF6B6B, #4ECDC4);
}

/* ==================== 响应式工具类 ==================== */

/* WeUI间距工具类 */
.weui-p-xs { padding: var(--weui-spacing-xs); }
.weui-p-sm { padding: var(--weui-spacing-sm); }
.weui-p-md { padding: var(--weui-spacing-md); }
.weui-p-lg { padding: var(--weui-spacing-lg); }
.weui-p-xl { padding: var(--weui-spacing-xl); }

.weui-m-xs { margin: var(--weui-spacing-xs); }
.weui-m-sm { margin: var(--weui-spacing-sm); }
.weui-m-md { margin: var(--weui-spacing-md); }
.weui-m-lg { margin: var(--weui-spacing-lg); }
.weui-m-xl { margin: var(--weui-spacing-xl); }

/* WeUI字体工具类 */
.weui-text-xs { font-size: var(--weui-font-size-xs); }
.weui-text-sm { font-size: var(--weui-font-size-sm); }
.weui-text-md { font-size: var(--weui-font-size-md); }
.weui-text-lg { font-size: var(--weui-font-size-lg); }
.weui-text-xl { font-size: var(--weui-font-size-xl); }
.weui-text-xxl { font-size: var(--weui-font-size-xxl); }

.weui-text-primary { color: var(--weui-text-primary); }
.weui-text-secondary { color: var(--weui-text-secondary); }
.weui-text-white { color: white; }

/* WeUI布局工具类 */
.weui-flex { display: flex; }
.weui-flex-column { display: flex; flex-direction: column; }
.weui-flex-center { display: flex; align-items: center; justify-content: center; }
.weui-flex-between { display: flex; align-items: center; justify-content: space-between; }
.weui-flex-around { display: flex; align-items: center; justify-content: space-around; }

/* WeUI圆角工具类 */
.weui-radius-sm { border-radius: 8rpx; }
.weui-radius-md { border-radius: 16rpx; }
.weui-radius-lg { border-radius: 24rpx; }
.weui-radius-xl { border-radius: 32rpx; }
.weui-radius-full { border-radius: 50%; }
