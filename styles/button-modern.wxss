/**
 * 现代化按键设计系统
 * 基于最新设计趋势和用户体验优化
 */

/* ===== 基础按钮样式 ===== */
.btn-modern {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx 32rpx;
  border: none;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  /* 使用轻量毛玻璃效果 */
  backdrop-filter: blur(16rpx) saturate(160%);
  -webkit-backdrop-filter: blur(16rpx) saturate(160%);
  box-sizing: border-box;
}

/* 按钮悬停效果 */
.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
  z-index: 1;
}

.btn-modern:active::before {
  left: 100%;
}

/* 按钮内容层级 */
.btn-modern text,
.btn-modern view,
.btn-modern image,
.btn-modern icon {
  position: relative;
  z-index: 2;
}

/* ===== 主要按钮样式 ===== */
.btn-primary-modern {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 50%, #FFB6C1 100%);
  color: #ffffff;
  box-shadow: 
    0 12rpx 40rpx rgba(255, 107, 107, 0.25),
    0 4rpx 16rpx rgba(255, 107, 107, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.btn-primary-modern:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 
    0 8rpx 24rpx rgba(255, 107, 107, 0.3),
    0 2rpx 8rpx rgba(255, 107, 107, 0.2);
}

/* ===== 次要按钮样式 ===== */
.btn-secondary-modern {
  background: linear-gradient(135deg, #4ECDC4 0%, #45B7D1 100%);
  color: #ffffff;
  box-shadow: 
    0 12rpx 40rpx rgba(78, 205, 196, 0.25),
    0 4rpx 16rpx rgba(78, 205, 196, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.btn-secondary-modern:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 
    0 8rpx 24rpx rgba(78, 205, 196, 0.3),
    0 2rpx 8rpx rgba(78, 205, 196, 0.2);
}

/* ===== 成功按钮样式 ===== */
.btn-success-modern {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: #ffffff;
  box-shadow: 
    0 12rpx 40rpx rgba(82, 196, 26, 0.25),
    0 4rpx 16rpx rgba(82, 196, 26, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.btn-success-modern:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 
    0 8rpx 24rpx rgba(82, 196, 26, 0.3),
    0 2rpx 8rpx rgba(82, 196, 26, 0.2);
}

/* ===== 警告按钮样式 ===== */
.btn-warning-modern {
  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
  color: #ffffff;
  box-shadow: 
    0 12rpx 40rpx rgba(250, 173, 20, 0.25),
    0 4rpx 16rpx rgba(250, 173, 20, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.btn-warning-modern:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 
    0 8rpx 24rpx rgba(250, 173, 20, 0.3),
    0 2rpx 8rpx rgba(250, 173, 20, 0.2);
}

/* ===== 危险按钮样式 ===== */
.btn-danger-modern {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: #ffffff;
  box-shadow: 
    0 12rpx 40rpx rgba(255, 77, 79, 0.25),
    0 4rpx 16rpx rgba(255, 77, 79, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.btn-danger-modern:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 
    0 8rpx 24rpx rgba(255, 77, 79, 0.3),
    0 2rpx 8rpx rgba(255, 77, 79, 0.2);
}

/* ===== 幽灵按钮样式 ===== */
.btn-ghost-modern {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
}

.btn-ghost-modern:active {
  transform: translateY(2rpx) scale(0.98);
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* ===== 按钮尺寸变体 ===== */
.btn-large-modern {
  padding: 32rpx 48rpx;
  font-size: 32rpx;
  border-radius: 24rpx;
}

.btn-small-modern {
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  border-radius: 16rpx;
}

.btn-mini-modern {
  padding: 12rpx 20rpx;
  font-size: 22rpx;
  border-radius: 12rpx;
}

/* ===== 圆形按钮 ===== */
.btn-round-modern {
  border-radius: 50rpx;
}

/* ===== 方形按钮 ===== */
.btn-square-modern {
  width: 80rpx;
  height: 80rpx;
  padding: 0;
  border-radius: 20rpx;
}

/* ===== 块级按钮 ===== */
.btn-block-modern {
  width: 100%;
  display: flex;
}

/* ===== 禁用状态 ===== */
.btn-disabled-modern {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.btn-disabled-modern::before {
  display: none;
}

/* ===== 加载状态 ===== */
.btn-loading-modern {
  pointer-events: none;
}

.btn-loading-modern .loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== 浮动操作按钮 ===== */
.fab-modern {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 112rpx;
  height: 112rpx;
  border-radius: 56rpx;
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
  color: #ffffff;
  box-shadow: 
    0 16rpx 48rpx rgba(255, 107, 107, 0.3),
    0 8rpx 24rpx rgba(255, 107, 107, 0.2);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fab-modern:active {
  transform: scale(0.9);
  box-shadow: 
    0 12rpx 32rpx rgba(255, 107, 107, 0.4),
    0 4rpx 16rpx rgba(255, 107, 107, 0.3);
}

/* ===== 按钮组 ===== */
.btn-group-modern {
  display: flex;
  gap: 16rpx;
}

.btn-group-modern .btn-modern {
  flex: 1;
}

/* ===== 响应式设计 ===== */
@media (max-width: 750rpx) {
  .btn-modern {
    padding: 20rpx 28rpx;
    font-size: 26rpx;
  }
  
  .btn-large-modern {
    padding: 28rpx 40rpx;
    font-size: 30rpx;
  }
}

/* ===== 深色模式适配 ===== */
@media (prefers-color-scheme: dark) {
  .btn-ghost-modern {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  .btn-ghost-modern:active {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
  }
}
