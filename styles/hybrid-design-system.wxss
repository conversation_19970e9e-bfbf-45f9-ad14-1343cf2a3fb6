/**
 * 混合设计系统 - Vant + WeUI + 爱巢小记品牌
 * 最佳实践：保留 Vant 的复杂组件，应用 WeUI 的设计规范
 */

/* ==================== 全局设计令牌 ==================== */
:root {
  /* 品牌色彩 - 保持爱巢小记特色 */
  --brand-primary: #FF6B6B;
  --brand-secondary: #4ECDC4;
  --brand-accent: #45B7D1;
  --brand-pink: #FFB6C1;
  --brand-warning: #faad14;
  
  /* WeUI 标准间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 48rpx;
  
  /* WeUI 标准字体 */
  --font-xs: 20rpx;
  --font-sm: 24rpx;
  --font-md: 28rpx;
  --font-lg: 32rpx;
  --font-xl: 36rpx;
  --font-xxl: 40rpx;
  
  /* WeUI 标准颜色 */
  --text-primary: #000000;
  --text-secondary: #888888;
  --text-placeholder: #CCCCCC;
  --text-white: #FFFFFF;
  
  /* 背景色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F7F7F7;
  --bg-tertiary: #EDEDED;
  
  /* 边框和阴影 */
  --border-color: #E5E5E5;
  --border-radius: 8rpx;
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

/* ==================== Vant 组件定制 ==================== */

/* Vant 按钮定制 - 应用品牌色彩 */
.van-button--primary {
  background: linear-gradient(135deg, var(--brand-primary), #FF8FAB) !important;
  border: none !important;
  box-shadow: var(--shadow-light) !important;
}

.van-button--info {
  background: linear-gradient(135deg, var(--brand-secondary), #44A08D) !important;
  border: none !important;
}

.van-button--warning {
  background: linear-gradient(135deg, var(--brand-warning), #ffd666) !important;
  border: none !important;
}

/* Vant 导航栏定制 */
.van-nav-bar {
  background: var(--brand-secondary) !important;
}

.van-nav-bar__title {
  color: white !important;
  font-weight: 600 !important;
}

/* Vant 标签页定制 */
.van-tab--active {
  color: var(--brand-secondary) !important;
}

.van-tabs__line {
  background: var(--brand-secondary) !important;
}

/* Vant 单元格定制 */
.van-cell {
  padding: var(--spacing-md) var(--spacing-lg) !important;
  font-size: var(--font-md) !important;
}

.van-cell__title {
  color: var(--text-primary) !important;
  font-weight: 500 !important;
}

.van-cell__value {
  color: var(--text-secondary) !important;
  font-size: var(--font-sm) !important;
}

/* Vant 输入框定制 */
.van-field__input {
  font-size: var(--font-md) !important;
  color: var(--text-primary) !important;
}

.van-field__placeholder {
  color: var(--text-placeholder) !important;
}

/* Vant 弹窗定制 */
.van-popup {
  border-radius: 24rpx 24rpx 0 0 !important;
}

.van-dialog {
  border-radius: 24rpx !important;
}

/* ==================== WeUI 风格组件 ==================== */

/* WeUI 风格的单元格 - 用于设置页面 */
.weui-cell-group {
  background: var(--bg-primary);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--shadow-light);
  margin-bottom: var(--spacing-md);
}

.weui-cell {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-primary);
  border-bottom: 1rpx solid var(--border-color);
  min-height: 88rpx;
  transition: background 0.3s ease;
}

.weui-cell:last-child {
  border-bottom: none;
}

.weui-cell:active {
  background: var(--bg-secondary);
}

.weui-cell__icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.weui-cell__bd {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.weui-cell__title {
  font-size: var(--font-md);
  color: var(--text-primary);
  font-weight: 500;
  line-height: 1.4;
}

.weui-cell__desc {
  font-size: var(--font-xs);
  color: var(--text-secondary);
  margin-top: 4rpx;
  line-height: 1.3;
}

.weui-cell__ft {
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  font-size: var(--font-sm);
}

/* ==================== 品牌特色组件 ==================== */

/* 毛玻璃卡片 - 移除重复定义，使用统一标准 */
/* 此定义已移至 glass-effects.wxss，避免重复定义导致重影 */

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 50%, var(--brand-accent) 100%);
}

/* 现代化按钮 */
.modern-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: 16rpx;
  font-size: var(--font-md);
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
  min-height: 88rpx;
}

.modern-btn--primary {
  background: linear-gradient(135deg, var(--brand-primary), #FF8FAB);
  color: white;
  box-shadow: var(--shadow-medium);
}

.modern-btn--primary:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-light);
}

.modern-btn--secondary {
  background: linear-gradient(135deg, var(--brand-secondary), #44A08D);
  color: white;
  box-shadow: var(--shadow-medium);
}

.modern-btn--ghost {
  background: transparent;
  color: var(--brand-secondary);
  border: 1rpx solid var(--brand-secondary);
}

/* ==================== 页面布局组件 ==================== */

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background: var(--bg-secondary);
}

.page-container--gradient {
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 50%, var(--brand-accent) 100%);
}

/* 内容区域 */
.content-area {
  padding: var(--spacing-md);
}

/* 卡片容器 */
.card-container {
  background: var(--bg-primary);
  border-radius: 16rpx;
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);
  margin-bottom: var(--spacing-md);
}

/* 统计卡片 - 使用统一毛玻璃效果 */
.stat-card {
  background: rgba(255, 255, 255, 0.15);
  /* 使用统一毛玻璃效果 */
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(90deg, var(--brand-primary), var(--brand-secondary));
}

/* ==================== 工具类 ==================== */

/* 间距工具类 */
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

/* 字体工具类 */
.text-xs { font-size: var(--font-xs); }
.text-sm { font-size: var(--font-sm); }
.text-md { font-size: var(--font-md); }
.text-lg { font-size: var(--font-lg); }
.text-xl { font-size: var(--font-xl); }
.text-xxl { font-size: var(--font-xxl); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-white { color: var(--text-white); }

/* 布局工具类 */
.flex { display: flex; }
.flex-column { display: flex; flex-direction: column; }
.flex-center { display: flex; align-items: center; justify-content: center; }
.flex-between { display: flex; align-items: center; justify-content: space-between; }

/* 圆角工具类 */
.radius-sm { border-radius: 8rpx; }
.radius-md { border-radius: 16rpx; }
.radius-lg { border-radius: 24rpx; }
.radius-full { border-radius: 50%; }
