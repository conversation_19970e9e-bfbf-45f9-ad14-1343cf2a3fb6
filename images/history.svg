<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="historyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#4ECDC4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#45B7D1;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="historyAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFB6C1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#87CEEB;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 主要列表背景 -->
  <rect x="4" y="5" width="16" height="14" rx="3" ry="3" fill="url(#historyGradient)" opacity="0.15"/>
  
  <!-- 列表项线条 -->
  <line x1="7" y1="8" x2="17" y2="8" stroke="url(#historyGradient)" stroke-width="2" stroke-linecap="round"/>
  <line x1="7" y1="12" x2="15" y2="12" stroke="url(#historyGradient)" stroke-width="2" stroke-linecap="round"/>
  <line x1="7" y1="16" x2="13" y2="16" stroke="url(#historyGradient)" stroke-width="2" stroke-linecap="round"/>
  
  <!-- 时钟图标（表示历史） -->
  <circle cx="18" cy="6" r="3" fill="url(#historyAccent)" opacity="0.9"/>
  <circle cx="18" cy="6" r="2" fill="none" stroke="white" stroke-width="1"/>
  <path d="M18 5v1.5l1 0.5" stroke="white" stroke-width="0.8" stroke-linecap="round"/>
  
  <!-- 装饰性高光 -->
  <circle cx="6" cy="8" r="1.5" fill="url(#historyAccent)" opacity="0.6"/>
  <circle cx="6" cy="12" r="1.5" fill="url(#historyAccent)" opacity="0.6"/>
  <circle cx="6" cy="16" r="1.5" fill="url(#historyAccent)" opacity="0.6"/>
</svg>
