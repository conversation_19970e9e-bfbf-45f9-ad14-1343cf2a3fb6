<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="loadingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ECDC4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#45B7D1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF6B6B;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 旋转的圆环 -->
  <circle 
    cx="12" 
    cy="12" 
    r="8" 
    fill="none" 
    stroke="url(#loadingGradient)" 
    stroke-width="3" 
    stroke-linecap="round"
    stroke-dasharray="25 15"
    opacity="0.8"
  >
    <animateTransform
      attributeName="transform"
      attributeType="XML"
      type="rotate"
      from="0 12 12"
      to="360 12 12"
      dur="1s"
      repeatCount="indefinite"
    />
  </circle>

  <!-- 内部小圆点 -->
  <circle 
    cx="12" 
    cy="12" 
    r="2" 
    fill="url(#loadingGradient)"
    opacity="0.6"
  >
    <animate
      attributeName="opacity"
      values="0.6;1;0.6"
      dur="1s"
      repeatCount="indefinite"
    />
  </circle>
</svg>
