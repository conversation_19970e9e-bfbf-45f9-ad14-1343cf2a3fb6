// pages/index/index.js
import storage from '../../utils/storage.js'
import dataManager from '../../utils/data-manager.js'
import auth from '../../utils/auth.js'
import userManager from '../../utils/userManager.js'
import { PAGES } from '../../utils/constants.js'
import Toast from '../../miniprogram_npm/@vant/weapp/toast/toast'
import unifiedPerformanceManager from '../../utils/unified-performance-manager.js'
import dataChangeNotifier, { DATA_CHANGE_TYPES } from '../../utils/data-change-notifier.js'
import hapticManager from '../../utils/haptic-manager.js'
// {{ AURA-X: Add - 引入首页旅行数据同步器和实时同步管理器. Approval: 寸止(ID:1738056000). }}
import homepageTravelSync from '../../utils/homepage-travel-sync.js'
import realtimeSyncManager from '../../utils/realtime-sync-manager.js'


Page({
  data: {
    appName: '爱巢小记',
    motto: '让记账变得简单而美好',
    userInfo: {},
    hasUserInfo: false,
    hasNotification: true, // 添加通知状态
    isGuest: false,
    // 财务概览数据 - 移除硬编码，使用真实数据
    financialOverview: {
      totalExpense: 0,           // 本月总支出
      dailyExpense: 0,           // 日常支出
      travelExpense: 0,          // 旅行支出
      totalBudget: 0,            // 总预算
      remainingBudget: 0,        // 剩余预算
      usagePercentage: 0,        // 使用百分比
      dailyPercentage: 0,        // 日常支出占比
      travelPercentage: 0        // 旅行支出占比
    },
    // 数据加载状态
    loading: {
      financial: false
    },
    // 页面状态管理
    _lastLoadTime: 0,           // 上次数据加载时间
    _needsRefresh: false,       // 是否需要刷新数据

    // {{ AURA-X: Add - 旅行数据状态. Approval: 寸止(ID:1738056000). }}
    // 旅行数据
    travelData: {
      currentPlan: null,        // 当前计划
      stats: {                  // 统计数据
        totalPlans: 0,
        activePlans: 0,
        completedPlans: 0,
        totalExpenses: 0,
        totalBudget: 0
      },
      recentPlans: [],          // 最近计划
      lastUpdate: 0             // 最后更新时间
    },
    // 旅行数据加载状态
    travelLoading: false
  },

  async onLoad() {
    // 初始化数据管理器
    await dataManager.initialize()

    // 登录检查
    const app = getApp()
    if (!app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/login/index'
      })
      return
    }

    // 使用用户管理器初始化页面
    await userManager.mixinPage(this)

    // 注册数据变更监听器
    this.setupDataChangeListeners()

    // 加载财务数据
    this.loadFinancialData()
  },



  async onShow() {
    // 🔥 检查登录状态
    const app = getApp()
    if (!app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/login/index'
      })
      return
    }

    try {
      // 智能刷新逻辑：只在必要时刷新数据
      const now = Date.now()
      const timeSinceLastLoad = now - this.data._lastLoadTime
      const shouldRefresh = this.data._needsRefresh ||
                           timeSinceLastLoad > 5 * 60 * 1000 || // 5分钟后自动刷新
                           this.data._lastLoadTime === 0        // 首次加载

      // 并行处理用户信息和财务数据
      const loadPromises = []

      // 用户信息处理（减少不必要的刷新）
      if (!this.data.userInfo || !this.data.userInfo.openid || shouldRefresh) {
        loadPromises.push(
          userManager.forceRefreshUserInfo()
            .then(() => userManager.mixinPage(this))
            .then(() => this.refreshUserDisplay())
        )
      }

      // 财务数据处理（使用缓存机制）
      if (shouldRefresh) {
        loadPromises.push(this.loadFinancialData())
      }

      // {{ AURA-X: Add - 旅行数据处理. Approval: 寸止(ID:1738056000). }}
      // 旅行数据处理（使用实时同步）
      if (shouldRefresh) {
        loadPromises.push(this.loadTravelData())
      }

      // 并行执行所有加载任务
      if (loadPromises.length > 0) {
        await Promise.allSettled(loadPromises)

        if (shouldRefresh) {
          unifiedPerformanceManager.smartSetData(this, {
            _lastLoadTime: now,
            _needsRefresh: false
          })
        }
      }

    } catch (error) {
      // 如果用户信息验证失败，跳转到登录页
      wx.reLaunch({
        url: '/pages/login/index'
      })
    }
  },

  // 刷新用户信息显示
  refreshUserDisplay() {
    const userInfo = userManager.getUserInfo()

    // 使用智能setData，只更新变化的数据
    unifiedPerformanceManager.smartSetData(this, {
      userInfo: userInfo,
      hasUserInfo: !!userInfo && !userInfo.isGuest,
      isGuest: userInfo.isGuest || false
    })
  },

  onUnload() {
    // 清理用户管理器监听器
    userManager.cleanupPage(this)

    // 清理性能管理器资源
    unifiedPerformanceManager.cleanup()

    // 清理数据变更监听器
    dataChangeNotifier.cleanupPage(this)

    // {{ AURA-X: Add - 清理旅行数据同步. Approval: 寸止(ID:1738056000). }}
    // 清理旅行数据同步监听器
    homepageTravelSync.offUpdate(this.onTravelDataUpdate)
    realtimeSyncManager.unregisterSync('travel_plans', 'homepage')
    realtimeSyncManager.unregisterSync('current_plan', 'homepage')
  },

  // 设置数据变更监听器
  setupDataChangeListeners() {
    // 监听记账数据变更
    dataChangeNotifier.addListener(DATA_CHANGE_TYPES.EXPENSE_ADDED, () => {
      this.loadFinancialData(true) // 强制刷新
    }, this)

    // 监听预算设置变更
    dataChangeNotifier.addListener(DATA_CHANGE_TYPES.BUDGET_UPDATED, () => {
      this.loadFinancialData(true) // 强制刷新
    }, this)

    // 监听旅行数据变更
    dataChangeNotifier.addListener(DATA_CHANGE_TYPES.TRAVEL_PLAN_ADDED, () => {
      this.loadFinancialData(true) // 刷新财务数据（包含旅行支出）
    }, this)

    // 监听旅行计划删除
    dataChangeNotifier.addListener('travelPlanDeleted', () => {
      // 清理本地缓存
      try {
        wx.removeStorageSync('financial_overview')
        wx.removeStorageSync('travel_statistics')
      } catch (error) {
        // 静默处理缓存清理失败
      }

      // 强制刷新财务数据，跳过缓存
      this.loadFinancialData(true)

      // 延迟再次刷新，确保数据更新
      setTimeout(() => {
        this.loadFinancialData(true)
      }, 1000)
    }, this)
  },

  // 手动刷新数据
  onRefresh() {
    unifiedPerformanceManager.smartSetData(this, { _needsRefresh: true })
    this.loadFinancialData(true) // 强制刷新
  },

  // 处理财务数据，修复百分比显示问题
  processFinancialData(data) {
    const processedData = { ...data }

    // 修复百分比显示问题
    if (processedData.usagePercentage) {
      processedData.usagePercentage = Math.round(processedData.usagePercentage * 100) / 100
    }

    if (processedData.dailyPercentage) {
      processedData.dailyPercentage = Math.round(processedData.dailyPercentage * 100) / 100
    }

    if (processedData.travelPercentage) {
      processedData.travelPercentage = Math.round(processedData.travelPercentage * 100) / 100
    }

    // 计算剩余预算百分比
    if (processedData.totalBudget > 0) {
      const remainingPercentage = 100 - (processedData.usagePercentage || 0)
      processedData.remainingPercentage = Math.max(0, Math.round(remainingPercentage * 100) / 100)
    } else {
      processedData.remainingPercentage = 0
    }

    return processedData
  },

  // 加载财务数据
  async loadFinancialData(forceRefresh = false) {
    // 再次检查登录状态，确保安全
    if (!auth.isLoggedIn()) {
      return
    }

    // 设置加载状态
    unifiedPerformanceManager.smartSetData(this, {
      'loading.financial': true
    })

    try {
      // 使用新的数据管理器获取财务数据
      const result = await dataManager.getFinancialOverview(forceRefresh)

      if (result.success && result.data) {

        // 验证数据
        const isValid = dataManager.validateFinancialData(result.data)

        if (isValid) {
          // 处理财务数据
          const processedData = this.processFinancialData(result.data)

          // 更新财务数据
          unifiedPerformanceManager.smartSetData(this, {
            financialOverview: processedData
          })
        } else {
          // 数据异常，使用默认数据
          this.setDefaultFinancialData()
        }
      } else {
        // 获取失败，使用默认数据
        this.setDefaultFinancialData()
      }
    } catch (error) {
      this.setDefaultFinancialData()
    } finally {
      // 清除加载状态
      unifiedPerformanceManager.smartSetData(this, {
        'loading.financial': false
      })
    }
  },

  // {{ AURA-X: Add - 加载旅行数据方法. Approval: 寸止(ID:1738056000). }}
  /**
   * 加载旅行数据
   * @param {boolean} forceRefresh 强制刷新
   */
  async loadTravelData(forceRefresh = false) {
    try {
      // 设置加载状态
      unifiedPerformanceManager.smartSetData(this, {
        travelLoading: true
      })

      // 获取旅行数据
      const result = await homepageTravelSync.getTravelData(forceRefresh)

      if (result.success && result.data) {
        // 更新旅行数据
        unifiedPerformanceManager.smartSetData(this, {
          travelData: result.data
        })

        // 注册实时同步监听器
        this.setupTravelDataSync()
      } else {
        // 使用默认数据
        this.setDefaultTravelData()
      }

    } catch (error) {
      console.error('加载旅行数据失败:', error)
      this.setDefaultTravelData()
    } finally {
      // 清除加载状态
      unifiedPerformanceManager.smartSetData(this, {
        travelLoading: false
      })
    }
  },

  /**
   * 设置旅行数据实时同步
   */
  setupTravelDataSync() {
    // 注册首页旅行数据更新回调
    homepageTravelSync.onUpdate(this.onTravelDataUpdate.bind(this))

    // 注册实时同步监听器
    realtimeSyncManager.registerSync('travel_plans', 'homepage', (data) => {
      this.onTravelPlansUpdate(data)
    })

    realtimeSyncManager.registerSync('current_plan', 'homepage', (data) => {
      this.onCurrentPlanUpdate(data)
    })
  },

  /**
   * 旅行数据更新回调
   * @param {Object} data 更新的数据
   */
  onTravelDataUpdate(data) {
    unifiedPerformanceManager.smartSetData(this, {
      travelData: data
    })
  },

  /**
   * 旅行计划列表更新回调
   * @param {Array} plans 计划列表
   */
  onTravelPlansUpdate(plans) {
    const currentData = this.data.travelData
    unifiedPerformanceManager.smartSetData(this, {
      'travelData.recentPlans': plans.slice(0, 3), // 只显示最近3个
      'travelData.lastUpdate': Date.now()
    })
  },

  /**
   * 当前计划更新回调
   * @param {Object} plan 当前计划
   */
  onCurrentPlanUpdate(plan) {
    unifiedPerformanceManager.smartSetData(this, {
      'travelData.currentPlan': plan,
      'travelData.lastUpdate': Date.now()
    })
  },

  /**
   * 设置默认旅行数据
   */
  setDefaultTravelData() {
    unifiedPerformanceManager.smartSetData(this, {
      travelData: {
        currentPlan: null,
        stats: {
          totalPlans: 0,
          activePlans: 0,
          completedPlans: 0,
          totalExpenses: 0,
          totalBudget: 0
        },
        recentPlans: [],
        lastUpdate: Date.now()
      }
    })
  },

  /**
   * 查看当前旅行计划
   */
  viewCurrentPlan() {
    const currentPlan = this.data.travelData.currentPlan
    if (!currentPlan) {
      Toast.fail('暂无进行中的旅行计划')
      return
    }

    wx.navigateTo({
      url: `/subpackages/travel-planning/plan-detail/index?id=${currentPlan._id || currentPlan.id}`
    })
  },

  /**
   * 查看旅行计划详情
   * @param {Event} e 事件对象
   */
  viewTravelPlan(e) {
    const planId = e.currentTarget.dataset.planId
    if (!planId) return

    wx.navigateTo({
      url: `/subpackages/travel-planning/plan-detail/index?id=${planId}`
    })
  },

  /**
   * 前往旅行规划页面
   */
  goToTravelPlanning() {
    wx.navigateTo({
      url: '/subpackages/travel-planning/index'
    })
  },

  /**
   * 刷新旅行数据
   */
  async refreshTravelData() {
    await this.loadTravelData(true)
  },

  // 设置默认财务数据
  setDefaultFinancialData() {
    const defaultData = {
      totalExpense: 0,
      dailyExpense: 0,
      travelExpense: 0,
      budgetUsage: 0,
      remainingBudget: 0,
      totalBudget: 0,
      usagePercentage: 0,
      dailyPercentage: 0,
      travelPercentage: 0
    }

    unifiedPerformanceManager.smartSetData(this, {
      financialOverview: defaultData
    })
  },



  getUserProfile(e) {
    // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认，开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
    wx.getUserProfile({
      desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        unifiedPerformanceManager.smartSetData(this, {
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },

  getUserInfo(e) {
    // 不推荐使用getUserInfo获取用户信息，预计自2021年4月13日起，getUserInfo将不再弹出弹窗，并直接返回匿名的用户个人信息
    unifiedPerformanceManager.smartSetData(this, {
      userInfo: e.detail.userInfo,
      hasUserInfo: true
    })
  },



  // 导航到旅行规划
  navigateToTravelPlanning() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/travel-planning/index'
    })
  },

  // 导航到记账管理 - 默认日常记账
  navigateToAccounting() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/account/travel-expense/index?mode=daily'
    })
  },

  // 导航到社交功能
  navigateToSocial() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/social/discover/index'
    })
  },

  // 导航到发现页面
  navigateToDiscover() {
    hapticManager.navigation()
    wx.switchTab({
      url: '/subpackages/social/discover/index'
    })
  },

  // 导航到预算设置
  navigateToBudgetSetting() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/settings/budget-setting/index'
    })
  },

  // 导航到个人中心
  navigateToProfile() {
    hapticManager.navigation()
    wx.navigateTo({
      url: PAGES.PROFILE
    })
  }
})
