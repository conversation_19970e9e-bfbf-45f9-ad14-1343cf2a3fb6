<!--pages/login/index.wxml-->
<view class="login-container">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="gradient-orb orb-1 {{showAnimation ? 'animate' : ''}}"></view>
    <view class="gradient-orb orb-2 {{showAnimation ? 'animate' : ''}}"></view>
    <view class="gradient-orb orb-3 {{showAnimation ? 'animate' : ''}}"></view>
    <view class="floating-shapes">
      <view class="shape shape-1"></view>
      <view class="shape shape-2"></view>
      <view class="shape shape-3"></view>
    </view>
  </view>

  <!-- 主要内容 -->
  <view class="content {{showAnimation ? 'show' : ''}}">
    <!-- 插画区域 -->
    <view class="illustration-section">
      <view class="travel-scene">
        <!-- 现代化旅行插画 -->
        <view class="travel-illustration">
          <!-- 飞机图标 -->
          <view class="icon-item airplane">
            <view class="icon-bg">
              <view class="svg-icon">
                <image src="/images/airplane.svg" mode="aspectFit" style="width: 32rpx; height: 32rpx;" />
              </view>
            </view>
          </view>
          <!-- 行李箱图标 -->
          <view class="icon-item suitcase">
            <view class="icon-bg">
              <view class="svg-icon">
                <image src="/images/suitcase.svg" mode="aspectFit" style="width: 32rpx; height: 32rpx;" />
              </view>
            </view>
          </view>
          <!-- 地图图标 -->
          <view class="icon-item map">
            <view class="icon-bg">
              <view class="svg-icon">
                <image src="/images/map.svg" mode="aspectFit" style="width: 32rpx; height: 32rpx;" />
              </view>
            </view>
          </view>
          <!-- 相机图标 -->
          <view class="icon-item camera">
            <view class="icon-bg">
              <view class="svg-icon">
                <image src="/images/camera.svg" mode="aspectFit" style="width: 32rpx; height: 32rpx;" />
              </view>
            </view>
          </view>
          <!-- 钱包图标 -->
          <view class="icon-item wallet">
            <view class="icon-bg">
              <view class="svg-icon">
                <image src="/images/wallet.svg" mode="aspectFit" style="width: 32rpx; height: 32rpx;" />
              </view>
            </view>
          </view>
          <!-- 好友图标 -->
          <view class="icon-item friends">
            <view class="icon-bg">
              <view class="svg-icon">
                <image src="/images/friends.svg" mode="aspectFit" style="width: 32rpx; height: 32rpx;" />
              </view>
            </view>
          </view>
        </view>
        <!-- 连接线 -->
        <view class="connection-lines">
          <view class="line line-1"></view>
          <view class="line line-2"></view>
          <view class="line line-3"></view>
        </view>
      </view>
    </view>

    <!-- 标题区域 -->
    <view class="title-section">
      <view class="logo-container">
        <view class="logo-icon">
          <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="24" cy="24" r="22" fill="url(#logoGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
            <path d="M24 8l4.5 9h10l-8 6.5 3 9.5-9.5-7-9.5 7 3-9.5-8-6.5h10L24 8z" fill="white" opacity="0.9"/>
            <defs>
              <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#FFA500;stop-opacity:1" />
              </linearGradient>
            </defs>
          </svg>
        </view>
        <text class="app-title">爱巢小记</text>
      </view>
      <text class="app-subtitle">旅行规划 · 智能记账 · 好友分享</text>
      <text class="app-desc">让每一次旅行都成为美好回忆</text>
    </view>

    <!-- 登录卡片 -->
    <van-cell-group custom-class="login-card">
      <!-- 登录说明 -->
      <van-cell>
        <view slot="title" class="login-intro">
          <text class="intro-title">欢迎使用爱巢小记</text>
          <text class="intro-desc">一键登录，开启精彩旅程</text>
        </view>
      </van-cell>

      <!-- 微信登录按钮 -->
      <van-cell>
        <view slot="title" class="login-methods">
          <van-button
            type="primary"
            size="large"
            loading="{{loading}}"
            disabled="{{loading}}"
            loading-text="登录中..."
            custom-class="wx-login-btn"
            bindtap="handleWxLogin"
          >
            <image wx:if="{{!loading}}" src="/images/wechat.svg" class="icon-medium" mode="aspectFit" />
            <text>{{loading ? '' : '微信快速登录'}}</text>
          </van-button>

          <text class="login-tip">点击登录即表示同意用户协议和隐私政策</text>
          <text class="privacy-tip">为保护隐私，微信可能不会提供真实昵称</text>
        </view>
      </van-cell>
    </van-cell-group>

    <!-- 底部说明 -->
    <view class="footer">
      <text class="privacy-text">登录即表示同意</text>
      <text class="privacy-link" bindtap="viewUserAgreement">《用户协议》</text>
      <text class="privacy-text">和</text>
      <text class="privacy-link" bindtap="viewPrivacyPolicy">《隐私政策》</text>
    </view>
  </view>

  <!-- Vant Toast 组件 -->
  <van-toast id="van-toast" />
</view>
