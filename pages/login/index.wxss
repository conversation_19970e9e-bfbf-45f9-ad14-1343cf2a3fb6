/* pages/login/index.wxss */

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 88rpx 32rpx 68rpx;
  box-sizing: border-box;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 渐变光球 */
.gradient-orb {
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  transform: scale(0);
  transition: all 1.2s cubic-bezier(0.4, 0, 0.2, 1);
  filter: blur(1rpx);
}

.gradient-orb.animate {
  opacity: 1;
  transform: scale(1);
}

.orb-1 {
  width: 300rpx;
  height: 300rpx;
  top: 8%;
  right: -10%;
  background: radial-gradient(circle, rgba(255, 107, 107, 0.15) 0%, rgba(255, 107, 107, 0.05) 50%, transparent 100%);
  animation: orbFloat1 20s ease-in-out infinite;
}

.orb-2 {
  width: 200rpx;
  height: 200rpx;
  bottom: 20%;
  left: -8%;
  background: radial-gradient(circle, rgba(78, 205, 196, 0.12) 0%, rgba(78, 205, 196, 0.04) 50%, transparent 100%);
  animation: orbFloat2 25s ease-in-out infinite;
}

.orb-3 {
  width: 150rpx;
  height: 150rpx;
  top: 35%;
  left: 15%;
  background: radial-gradient(circle, rgba(69, 183, 209, 0.1) 0%, rgba(69, 183, 209, 0.03) 50%, transparent 100%);
  animation: orbFloat3 30s ease-in-out infinite;
}

/* 浮动形状 */
.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
}

.shape-1 {
  width: 60rpx;
  height: 60rpx;
  top: 25%;
  right: 20%;
  animation: shapeFloat1 15s ease-in-out infinite;
}

.shape-2 {
  width: 40rpx;
  height: 40rpx;
  bottom: 40%;
  right: 30%;
  animation: shapeFloat2 18s ease-in-out infinite;
}

.shape-3 {
  width: 80rpx;
  height: 80rpx;
  top: 60%;
  left: 10%;
  animation: shapeFloat3 22s ease-in-out infinite;
}

/* 动画效果 */
@keyframes orbFloat1 {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1) rotate(0deg);
    opacity: 0.8;
  }
  33% {
    transform: translateY(-40rpx) translateX(30rpx) scale(1.1) rotate(120deg);
    opacity: 0.4;
  }
  66% {
    transform: translateY(20rpx) translateX(-20rpx) scale(0.9) rotate(240deg);
    opacity: 0.6;
  }
}

@keyframes orbFloat2 {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(35rpx) translateX(-25rpx) scale(1.2) rotate(180deg);
    opacity: 0.9;
  }
}

@keyframes orbFloat3 {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-25rpx) translateX(40rpx) scale(0.8) rotate(90deg);
    opacity: 0.3;
  }
  75% {
    transform: translateY(30rpx) translateX(-30rpx) scale(1.3) rotate(270deg);
    opacity: 0.5;
  }
}

@keyframes shapeFloat1 {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-50rpx) scale(1.5);
    opacity: 0.8;
  }
}

@keyframes shapeFloat2 {
  0%, 100% {
    transform: translateX(0px) scale(1);
    opacity: 0.4;
  }
  50% {
    transform: translateX(60rpx) scale(0.7);
    opacity: 0.2;
  }
}

@keyframes shapeFloat3 {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-30rpx) translateX(50rpx) scale(1.2);
    opacity: 0.1;
  }
}

/* 主要内容 */
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 640rpx;
  margin: 0 auto;
  opacity: 0;
  transform: translateY(50rpx);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.content.show {
  opacity: 1;
  transform: translateY(0);
}

/* 插画区域 */
.illustration-section {
  width: 100%;
  height: 300rpx;
  margin-bottom: 60rpx;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.travel-scene {
  position: relative;
  width: 100%;
  height: 100%;
}

.travel-illustration {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-item {
  position: absolute;
  animation: iconFloat 6s ease-in-out infinite;
}

.icon-bg {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.svg-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(255, 255, 255, 0.9);
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.svg-icon svg {
  width: 100%;
  height: 100%;
}

/* 图标位置 */
.airplane {
  top: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 0s;
}

.suitcase {
  top: 80rpx;
  right: 60rpx;
  animation-delay: 1s;
}

.map {
  top: 160rpx;
  left: 40rpx;
  animation-delay: 2s;
}

.camera {
  bottom: 80rpx;
  right: 80rpx;
  animation-delay: 3s;
}

.wallet {
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 4s;
}

.friends {
  bottom: 100rpx;
  left: 60rpx;
  animation-delay: 5s;
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-20rpx) scale(1.1);
  }
}

/* 连接线 */
.connection-lines {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.line {
  position: absolute;
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: lineGlow 4s ease-in-out infinite;
}

.line-1 {
  top: 50%;
  left: 20%;
  width: 60%;
  transform: rotate(15deg);
  animation-delay: 0s;
}

.line-2 {
  top: 30%;
  left: 30%;
  width: 40%;
  transform: rotate(-30deg);
  animation-delay: 1.5s;
}

.line-3 {
  bottom: 30%;
  left: 25%;
  width: 50%;
  transform: rotate(45deg);
  animation-delay: 3s;
}

@keyframes lineGlow {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.8;
  }
}

/* 标题区域 */
.title-section {
  text-align: center;
  margin-bottom: 80rpx;
  position: relative;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  position: relative;
}

.logo-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
  animation: logoGlow 3s ease-in-out infinite;
  filter: drop-shadow(0 4rpx 12rpx rgba(255, 215, 0, 0.4));
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-icon svg {
  width: 100%;
  height: 100%;
}

@keyframes logoGlow {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.1) rotate(5deg);
    opacity: 0.8;
  }
}

.app-title {
  font-size: 56rpx;
  font-weight: 700;
  color: #FFFFFF;
  text-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 3rpx;
  background: linear-gradient(135deg, #FFFFFF 0%, rgba(255, 255, 255, 0.85) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-subtitle {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  letter-spacing: 1rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  line-height: 1.6;
  margin-bottom: 16rpx;
  background: rgba(255, 255, 255, 0.1);
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.15);
  display: inline-block;
}

.app-desc {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
  letter-spacing: 1rpx;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
  line-height: 1.5;
  font-style: italic;
}

/* 登录卡片 */
.login-card {
  background: rgba(255, 255, 255, 0.12);
  border-radius: 28rpx;
  padding: 48rpx 36rpx 40rpx;
  backdrop-filter: blur(30rpx);
  box-shadow:
    0 20rpx 60rpx rgba(0, 0, 0, 0.15),
    0 8rpx 24rpx rgba(0, 0, 0, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
  margin-bottom: 40rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.5) 50%, transparent 100%);
}

.login-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent);
  animation: cardShimmer 4s ease-in-out infinite;
  animation-delay: 1s;
}

@keyframes cardShimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 按钮样式 - 使用微信官方button组件 */
.wx-login-btn {
  width: 100%;
  height: 100rpx;
  margin-bottom: 28rpx;
  border-radius: 24rpx;
  font-size: 30rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #07C160 0%, #06AD56 100%);
  box-shadow:
    0 12rpx 32rpx rgba(7, 193, 96, 0.3),
    0 4rpx 12rpx rgba(7, 193, 96, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.wx-login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
  transition: left 0.8s ease;
}

.wx-login-btn:active::before {
  left: 100%;
}

.wx-login-btn:active {
  transform: translateY(2rpx) scale(0.97);
  box-shadow:
    0 6rpx 20rpx rgba(7, 193, 96, 0.4),
    0 2rpx 8rpx rgba(7, 193, 96, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.phone-btn {
  width: 100%;
  height: 100rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20rpx);
  position: relative;
  overflow: hidden;
}

.phone-btn[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

.phone-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
  transition: left 0.6s ease;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  z-index: 1;
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.btn-text {
  font-size: inherit;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 分割线 */
.divider {
  display: flex;
  align-items: center;
  margin: 36rpx 0 28rpx;
  position: relative;
}

.divider-line {
  flex: 1;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
  position: relative;
}

.divider-line::after {
  content: '';
  position: absolute;
  top: 1rpx;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
}

.divider-text {
  margin: 0 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-size: 22rpx;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  backdrop-filter: blur(15rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.15);
}

/* 手机号登录 */
.phone-login {
  text-align: center;
}

.phone-tip {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 16rpx;
  line-height: 1.5;
  font-weight: 400;
  background: rgba(255, 255, 255, 0.05);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  display: inline-block;
}

/* 不支持提示 */
.unsupported-tip {
  text-align: center;
  padding: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.15);
}

/* 底部说明 */
.footer {
  text-align: center;
  padding: 24rpx 20rpx 0;
  margin-top: auto;
}

.privacy-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  font-weight: 400;
}

.privacy-link {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: underline;
  margin: 0 4rpx;
  font-weight: 500;
  text-decoration-color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.privacy-link:active {
  color: rgba(255, 255, 255, 1);
  text-decoration-color: rgba(255, 255, 255, 0.8);
}










/* 登录提示 */
.login-tip {
  display: block;
  text-align: center;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 24rpx;
  line-height: 1.5;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.privacy-tip {
  display: block;
  text-align: center;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 12rpx;
  line-height: 1.4;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  font-style: italic;
}

/* ===== Vant组件适配样式 ===== */

/* 登录卡片Vant适配 */
.login-card {
  margin: 32rpx 24rpx !important;
  background: rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(20rpx) saturate(180%);
  border-radius: 32rpx !important;
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
  box-shadow:
    0 16rpx 64rpx rgba(0, 0, 0, 0.1),
    0 2rpx 0 rgba(255, 255, 255, 0.3) inset !important;
}

.login-card .van-cell {
  background: transparent !important;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1) !important;
  padding: 32rpx !important;
}

.login-card .van-cell:last-child {
  border-bottom: none !important;
}

/* 登录介绍区域 */
.login-intro {
  text-align: center;
  margin-bottom: 24rpx;
}

.intro-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

.intro-desc {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

/* Vant按钮适配 */
.wx-login-btn {
  width: 100% !important;
  background: linear-gradient(135deg, #07C160, #06AD56) !important;
  border: none !important;
  border-radius: 16rpx !important;
  margin-bottom: 24rpx !important;
  box-shadow: 0 8rpx 24rpx rgba(7, 193, 96, 0.3) !important;
  font-size: 28rpx !important;
  font-weight: 500 !important;
}

.wx-login-btn:active {
  transform: scale(0.98) !important;
}

/* 登录方法区域 */
.login-methods {
  width: 100%;
}

/* 图标样式类 */
.icon-medium {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}
