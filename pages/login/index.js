// pages/login/index.js
import api from '../../utils/api.js'
import storage from '../../utils/storage.js'
import auth from '../../utils/auth.js'
import userManager from '../../utils/userManager.js'
import { PAGES } from '../../utils/constants.js'

Page({
  data: {
    // 页面状态
    loading: false,

    // 用户信息
    userInfo: null,
    hasUserInfo: false,

    // 微信登录能力检测
    canIUseGetUserProfile: false,

    // 动画状态
    showAnimation: false
  },

  onLoad() {
    // 检测微信API能力
    this.checkWxCapabilities()

    // 启动入场动画
    setTimeout(() => {
      this.setData({ showAnimation: true })
    }, 200)

    // 添加触觉反馈
    this.enableHapticFeedback()
  },

  // 启用触觉反馈
  enableHapticFeedback() {
    if (wx.canIUse('vibrateShort')) {
      setTimeout(() => {
        wx.vibrateShort({ type: 'light' })
      }, 600)
    }
  },

  // 触发触觉反馈
  triggerHapticFeedback(type = 'light') {
    if (wx.canIUse('vibrateShort')) {
      wx.vibrateShort({ type })
    }
  },

  onShow() {
    // 检查是否已登录
    this.checkLoginStatus()
  },

  // 检测微信API能力
  checkWxCapabilities() {
    const canUse = !!wx.getUserProfile
    this.setData({
      canIUseGetUserProfile: canUse
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = storage.getUserInfo()
    if (userInfo) {
      wx.reLaunch({
        url: PAGES.INDEX
      })
    }
  },

  // 微信授权登录
  async handleWxLogin() {
    if (this.data.loading) {
      return
    }

    // 触觉反馈
    this.triggerHapticFeedback('light')
    this.setData({ loading: true })

    try {
      // 检查getUserProfile API可用性
      if (!wx.getUserProfile) {
        throw new Error('当前微信版本不支持getUserProfile API，请升级微信')
      }

      // 获取用户信息
      const userInfo = await this.getUserProfile()

      // 验证获取到的用户信息
      if (!userInfo || !userInfo.userInfo) {
        throw new Error('getUserProfile 返回数据异常')
      }

      // 调用云函数登录
      const loginRes = await this.wxLogin(userInfo)

      // 🔥 构建用户数据
      const userData = {
        openid: loginRes.openid,
        unionid: loginRes.unionid,
        avatarUrl: userInfo.userInfo.avatarUrl,
        nickName: userInfo.userInfo.nickName,
        gender: userInfo.userInfo.gender,
        city: userInfo.userInfo.city,
        province: userInfo.userInfo.province,
        country: userInfo.userInfo.country,
        loginTime: new Date().toISOString(),
        loginType: 'wechat'
      }

      this.saveUserData(userData)
      this.showSuccessAndRedirect()

    } catch (error) {
      if (error.message && error.message.includes('getUserProfile')) {
        this.showError('获取微信用户信息失败，请重试')
      } else if (error.message && error.message.includes('auth deny')) {
        this.showError('您拒绝了授权，无法获取头像和昵称')
      } else {
        this.showError(error.message || '登录失败，请重试')
      }
    } finally {
      this.setData({ loading: false })
    }
  },





  // 微信登录 - 调用云函数
  async wxLogin(userInfo = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {
          userInfo: userInfo
        }
      })

      // 检查云函数调用是否成功
      if (result.errMsg && result.errMsg !== 'cloud.callFunction:ok') {
        throw new Error(`云函数调用失败: ${result.errMsg}`)
      }

      if (result.result && result.result.success) {
        return result.result.data
      } else {
        const errorMsg = result.result?.message || '云函数返回失败'
        throw new Error(errorMsg)
      }

    } catch (error) {
      // 根据错误类型提供更具体的错误信息
      if (error.errCode) {
        switch (error.errCode) {
          case -1:
            throw new Error('网络连接失败，请检查网络连接')
          case -404001:
            throw new Error('云函数不存在，请联系开发者')
          case -501002:
            throw new Error('云函数执行超时，请重试')
          case -501003:
            throw new Error('云函数内存不足')
          case -501004:
            throw new Error('云函数执行异常，请重试')
          default:
            throw new Error(`登录失败(${error.errCode}): ${error.errMsg || '未知错误'}`)
        }
      } else {
        throw new Error(error.message || '登录异常，请重试')
      }
    }
  },

  // 获取用户信息
  async getUserProfile() {
    return new Promise((resolve, reject) => {
      // 检查API可用性
      if (!wx.getUserProfile) {
        reject(new Error('wx.getUserProfile API 不可用'))
        return
      }

      // 调用微信getUserProfile API
      wx.getUserProfile({
        desc: '获取您的昵称、头像、地区及性别',
        success: (res) => {
          // 检查返回数据
          if (!res || !res.userInfo) {
            reject(new Error('getUserProfile 返回数据异常'))
            return
          }

          // {{ AURA-X: Modify - 移除guest-avatar引用，使用标准用户头像. Approval: 寸止(ID:1738056000). }}
          // 构建标准化的用户信息
          const userProfileData = {
            userInfo: {
              avatarUrl: res.userInfo.avatarUrl || '/images/user.svg',
              nickName: res.userInfo.nickName || '微信用户',
              gender: res.userInfo.gender || 0,
              city: res.userInfo.city || '',
              province: res.userInfo.province || '',
              country: res.userInfo.country || '中国'
            },
            rawData: res.rawData,
            signature: res.signature,
            encryptedData: res.encryptedData,
            iv: res.iv
          }

          resolve(userProfileData)
        },
        fail: (error) => {
          // 错误分类处理
          if (error.errMsg && error.errMsg.includes('auth deny')) {
            reject(new Error('用户拒绝了授权，无法获取头像和昵称'))
          } else if (error.errMsg && error.errMsg.includes('can only be invoked by user TAP gesture')) {
            reject(new Error('请直接点击登录按钮进行授权'))
          } else {
            reject(new Error(`获取用户信息失败: ${error.errMsg || '未知错误'}`))
          }
        }
      })
    })
  },

  // 保存用户数据
  saveUserData(userData) {
    try {
      // 🔥 登录前先清理所有缓存，防止数据混乱
      wx.clearStorageSync()

      // 使用auth工具统一设置用户信息
      auth.setUserInfo(userData)

      // 🔥 设置用户登录状态（移除游客模式）
      userManager.setUserLogin(userData, false)

      // 更新全局登录状态
      const app = getApp()
      app.globalData.isLoggedIn = true
      app.globalData.openid = userData.openid

    } catch (error) {
      throw error
    }
  },



  // 显示成功并跳转
  showSuccessAndRedirect() {
    // 触觉反馈
    if (wx.canIUse('vibrateShort')) {
      wx.vibrateShort({ type: 'medium' })
    }

    wx.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 1500
    })

    setTimeout(() => {
      wx.reLaunch({ url: PAGES.INDEX })
    }, 1500)
  },







  // 显示错误信息
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  },
  // 查看用户协议
  viewUserAgreement() {
    wx.navigateTo({
      url: '/subpackages/settings/user-agreement/index'
    })
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    wx.navigateTo({
      url: '/subpackages/settings/privacy/index'
    })
  }






})
