/* pages/user-setup/index.wxss */
.user-setup-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  position: relative;
  overflow: hidden;
}

/* 顶部装饰 */
.top-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 400rpx;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: -100rpx;
  right: -50rpx;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 120rpx;
  height: 120rpx;
  top: 100rpx;
  left: -30rpx;
  animation: float 8s ease-in-out infinite reverse;
}

.circle-3 {
  width: 80rpx;
  height: 80rpx;
  top: 200rpx;
  right: 100rpx;
  animation: float 10s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
}

/* 主要内容 */
.main-content {
  position: relative;
  z-index: 10;
  padding: 120rpx 48rpx 48rpx;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.main-title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}

.sub-title {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 表单区域 */
.form-section {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 48rpx;
  margin-bottom: 48rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 20rpx 60rpx rgba(0, 0, 0, 0.1),
    0 1rpx 0 rgba(255, 255, 255, 0.3) inset;
}

.form-item {
  margin-bottom: 48rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  margin-bottom: 16rpx;
}

.required {
  color: #FF6B6B;
  margin-left: 4rpx;
}

/* 头像设置 */
.avatar-section {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.avatar-container {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-container:active .avatar-overlay {
  opacity: 1;
}

.camera-icon {
  font-size: 32rpx;
}

.avatar-tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 表单输入 */
.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  font-size: 28rpx;
  color: rgba(45, 52, 54, 0.9);
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.form-input::placeholder {
  color: rgba(45, 52, 54, 0.5);
}

.form-input:focus {
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 
    0 4rpx 12rpx rgba(0, 0, 0, 0.1),
    0 0 0 4rpx rgba(255, 255, 255, 0.1);
}

/* 选择器 */
.form-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.form-selector:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.5);
}

.selector-text {
  font-size: 28rpx;
  color: rgba(45, 52, 54, 0.9);
}

.selector-arrow {
  font-size: 32rpx;
  color: rgba(45, 52, 54, 0.6);
  transform: rotate(90deg);
}

/* 按钮区域 */
.button-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.complete-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
  border: none;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  box-shadow: 
    0 12rpx 32rpx rgba(255, 107, 107, 0.4),
    0 4rpx 12rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.complete-btn:active {
  transform: translateY(2rpx) scale(0.97);
  box-shadow: 
    0 8rpx 24rpx rgba(255, 107, 107, 0.5),
    0 4rpx 12rpx rgba(255, 107, 107, 0.4);
}

.complete-btn[disabled] {
  opacity: 0.6;
  transform: none;
}

.skip-btn {
  width: 100%;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.skip-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.15);
}

/* 底部装饰 */
.bottom-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200rpx;
  pointer-events: none;
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 100% 100% 0 0;
}

.wave-1 {
  animation: wave 8s ease-in-out infinite;
}

.wave-2 {
  height: 80rpx;
  background: rgba(255, 255, 255, 0.05);
  animation: wave 10s ease-in-out infinite reverse;
}

@keyframes wave {
  0%, 100% { transform: translateX(0px); }
  50% { transform: translateX(20rpx); }
}

/* ===== WeUI组件适配样式 ===== */

/* 表单区域WeUI适配 */
.user-form {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  margin: 0 48rpx 48rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20rpx 60rpx rgba(0, 0, 0, 0.1),
    0 1rpx 0 rgba(255, 255, 255, 0.3) inset;
}

.user-form .weui-cell {
  background: transparent;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  padding: 32rpx;
}

.user-form .weui-cell:last-child {
  border-bottom: none;
}

/* WeUI标签样式 */
.weui-label {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.weui-label_required::after {
  content: '*';
  color: #FF6B6B;
  margin-left: 4rpx;
}

/* 昵称输入框 */
.nickname-input {
  background: rgba(255, 255, 255, 0.4) !important;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 16rpx !important;
  color: rgba(45, 52, 54, 0.9) !important;
  padding: 16rpx 24rpx !important;
}

/* 选择器内容 */
.selector-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
}

.selector-text {
  font-size: 28rpx;
  color: rgba(45, 52, 54, 0.9);
  flex: 1;
}

/* WeUI按钮适配 */
.complete-btn {
  width: 100% !important;
  height: 100rpx !important;
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%) !important;
  border: none !important;
  border-radius: 24rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  color: white !important;
  box-shadow:
    0 12rpx 32rpx rgba(255, 107, 107, 0.4),
    0 4rpx 12rpx rgba(255, 107, 107, 0.3) !important;
  margin-bottom: 24rpx !important;
}

.complete-btn:active {
  transform: translateY(2rpx) scale(0.97) !important;
}

.skip-btn {
  width: 100% !important;
  height: 88rpx !important;
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 20rpx !important;
  font-size: 28rpx !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

.skip-btn:active {
  transform: scale(0.95) !important;
  background: rgba(255, 255, 255, 0.15) !important;
}
