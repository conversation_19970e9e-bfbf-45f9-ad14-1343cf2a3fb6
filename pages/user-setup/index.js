// pages/user-setup/index.js
import userManager from '../../utils/userManager.js'
import { PAGES } from '../../utils/constants.js'

Page({
  data: {
    // {{ AURA-X: Modify - 移除guest-avatar引用，使用标准用户头像. Approval: 寸止(ID:1738056000). }}
    // 用户信息
    userInfo: {
      avatarUrl: '/images/user.svg',
      nickName: '',
      gender: 0,
      city: '',
      province: '',
      country: ''
    },
    
    // 页面状态
    loading: false,
    
    // 来源信息
    wechatInfo: null,
    locationInfo: null,
    
    // 性别选择器
    showGenderPicker: false,
    genderOptions: ['未设置', '男', '女']
  },

  onLoad(options) {
    // 解析传入的参数
    if (options.wechatInfo) {
      try {
        const wechatInfo = JSON.parse(decodeURIComponent(options.wechatInfo))
        this.setData({ wechatInfo })

        // 使用微信信息初始化
        this.setData({
          'userInfo.avatarUrl': wechatInfo.avatarUrl,
          'userInfo.nickName': wechatInfo.nickName,
          'userInfo.gender': wechatInfo.gender || 0
        })
      } catch (error) {
        // 静默处理解析失败
      }
    }
    
    if (options.location) {
      try {
        const locationInfo = JSON.parse(decodeURIComponent(options.location))
        this.setData({ locationInfo })
        
        // 使用位置信息初始化
        if (locationInfo.address) {
          this.setData({
            'userInfo.province': locationInfo.address.province || '',
            'userInfo.city': locationInfo.address.city || '',
            'userInfo.country': locationInfo.address.country || '中国'
          })
        }
      } catch (error) {
        console.error('解析位置信息失败:', error)
      }
    }
  },

  // 选择头像
  chooseAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath
        this.uploadAvatar(tempFilePath)
      },
      fail: (err) => {
        console.error('选择头像失败:', err)
        if (!err.errMsg.includes('cancel')) {
          wx.showToast({
            title: '选择头像失败',
            icon: 'none'
          })
        }
      }
    })
  },

  // 上传头像
  async uploadAvatar(tempFilePath) {
    wx.showLoading({ title: '上传中...' })
    
    try {
      const cloudPath = `avatars/setup_${Date.now()}.jpg`
      
      const uploadRes = await wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: tempFilePath
      })

      this.setData({
        'userInfo.avatarUrl': uploadRes.fileID
      })
      
      wx.hideLoading()
      wx.showToast({
        title: '头像上传成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('上传头像失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '头像上传失败',
        icon: 'none'
      })
    }
  },

  // 昵称输入
  onNickNameInput(e) {
    this.setData({
      'userInfo.nickName': e.detail.value
    })
  },

  // 显示性别选择器
  showGenderPicker() {
    this.setData({ showGenderPicker: true })
  },

  // 选择性别
  onGenderChange(e) {
    const gender = parseInt(e.detail.value)
    this.setData({
      'userInfo.gender': gender,
      showGenderPicker: false
    })
  },

  // 取消性别选择
  onGenderCancel() {
    this.setData({ showGenderPicker: false })
  },

  // 选择地区
  chooseRegion() {
    wx.chooseLocation({
      success: (res) => {
        // 简单解析地址
        const address = res.address || ''
        const parts = address.split(/[省市区县]/)
        
        this.setData({
          'userInfo.province': parts[0] || '',
          'userInfo.city': parts[1] || res.name || '',
          'userInfo.country': '中国'
        })
        
        wx.showToast({
          title: '地区设置成功',
          icon: 'success'
        })
      },
      fail: (err) => {
        console.error('选择地区失败:', err)
        if (!err.errMsg.includes('cancel')) {
          wx.showToast({
            title: '选择地区失败',
            icon: 'none'
          })
        }
      }
    })
  },

  // 完成设置
  async completeSetup() {
    const { userInfo } = this.data
    
    // 验证必填信息
    if (!userInfo.nickName.trim()) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })

    try {
      // 获取当前用户信息并合并
      const currentUserInfo = userManager.getUserInfo() || {}
      const updatedUserInfo = {
        ...currentUserInfo,
        ...userInfo,
        isGuest: false,
        setupCompleted: true,
        updateTime: new Date().toISOString()
      }

      // 更新用户信息
      const success = await userManager.updateUserInfo(updatedUserInfo)
      
      if (success) {
        // 更新全局登录状态
        const app = getApp()
        app.globalData.isLoggedIn = true
        
        wx.showToast({
          title: '设置完成',
          icon: 'success'
        })

        // 延迟跳转到首页
        setTimeout(() => {
          wx.reLaunch({ url: PAGES.INDEX })
        }, 1500)
      } else {
        throw new Error('保存用户信息失败')
      }
    } catch (error) {
      wx.showToast({
        title: '设置失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 跳过设置
  skipSetup() {
    wx.showModal({
      title: '提示',
      content: '跳过设置将使用默认信息，您可以稍后在个人中心修改',
      confirmText: '跳过',
      cancelText: '继续设置',
      success: (res) => {
        if (res.confirm) {
          // 使用默认信息完成设置
          const defaultUserInfo = {
            ...userManager.getUserInfo(),
            nickName: this.data.userInfo.nickName || '爱巢用户',
            setupCompleted: true
          }
          
          userManager.updateUserInfo(defaultUserInfo)
          
          // 更新全局登录状态
          const app = getApp()
          app.globalData.isLoggedIn = true
          
          wx.reLaunch({ url: PAGES.INDEX })
        }
      }
    })
  }
})
