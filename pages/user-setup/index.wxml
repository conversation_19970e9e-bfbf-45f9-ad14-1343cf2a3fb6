<!--pages/user-setup/index.wxml-->
<view class="user-setup-container">
  <!-- 顶部装饰 -->
  <view class="top-decoration">
    <view class="decoration-circle circle-1"></view>
    <view class="decoration-circle circle-2"></view>
    <view class="decoration-circle circle-3"></view>
  </view>

  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 标题区域 -->
    <view class="header-section">
      <text class="main-title">完善个人信息</text>
      <text class="sub-title">让我们更好地为您服务</text>
    </view>

    <!-- 表单区域 -->
    <view class="form-section">
      <!-- 头像设置 -->
      <view class="form-item">
        <text class="form-label">头像</text>
        <view class="avatar-section">
          <view class="avatar-container" bindtap="chooseAvatar">
            <image class="avatar-image" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
            <view class="avatar-overlay">
              <text class="camera-icon">📷</text>
            </view>
          </view>
          <text class="avatar-tip">点击更换头像</text>
        </view>
      </view>

      <!-- 昵称设置 -->
      <view class="form-item">
        <text class="form-label">昵称 <text class="required">*</text></text>
        <input
          class="form-input"
          type="text"
          placeholder="请输入您的昵称"
          value="{{userInfo.nickName}}"
          bindinput="onNickNameInput"
          maxlength="20"
        />
      </view>

      <!-- 性别设置 -->
      <view class="form-item">
        <text class="form-label">性别</text>
        <view class="form-selector" bindtap="showGenderPicker">
          <text class="selector-text">{{genderOptions[userInfo.gender]}}</text>
          <text class="selector-arrow">›</text>
        </view>
      </view>

      <!-- 地区设置 -->
      <view class="form-item">
        <text class="form-label">地区</text>
        <view class="form-selector" bindtap="chooseRegion">
          <text class="selector-text">
            {{userInfo.province}}{{userInfo.city || '请选择地区'}}
          </text>
          <text class="selector-arrow">›</text>
        </view>
      </view>
    </view>

    <!-- 按钮区域 -->
    <view class="button-section">
      <button
        class="complete-btn"
        bindtap="completeSetup"
        loading="{{loading}}"
        disabled="{{loading}}"
      >
        完成设置
      </button>

      <button class="skip-btn" bindtap="skipSetup">
        跳过，稍后设置
      </button>
    </view>
  </view>

  <!-- 性别选择器 -->
  <picker
    wx:if="{{showGenderPicker}}"
    mode="selector"
    range="{{genderOptions}}"
    value="{{userInfo.gender}}"
    bindchange="onGenderChange"
    bindcancel="onGenderCancel"
  >
    <view></view>
  </picker>

  <!-- 底部装饰 -->
  <view class="bottom-decoration">
    <view class="wave wave-1"></view>
    <view class="wave wave-2"></view>
  </view>

</view>
