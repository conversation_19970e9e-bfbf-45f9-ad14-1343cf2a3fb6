# SVG图标使用指南

## 📋 概述

爱巢小记项目采用统一的SVG图标系统，所有图标都经过精心设计，具有一致的视觉风格和色彩体系。本指南将帮助开发者正确使用和管理SVG图标。

## 🎯 设计原则

### 视觉一致性
- **统一风格**：所有图标采用现代化扁平设计风格
- **色彩体系**：使用渐变色彩，与应用主题保持一致
- **高光效果**：添加径向渐变高光，增强立体感
- **边框装饰**：适当的描边和装饰线条

### 功能性
- **语义明确**：图标名称和视觉表达清晰对应
- **尺寸适配**：支持多种尺寸，适应不同使用场景
- **颜色可控**：支持自定义颜色，适应不同主题

## 📏 尺寸标准

### 标准尺寸体系 - 2025年7月优化版
```
超小图标 (24rpx)：状态指示器、小装饰元素、细节操作
小图标   (28rpx)：辅助信息、联系方式、次要操作
中图标   (32rpx)：卡片标题、分组标题、导航辅助
标准图标 (40rpx)：主要功能入口、页面标题、重要操作
大图标   (48rpx)：主要功能卡片、重要按钮、突出显示
超大图标 (54rpx)：页面主要元素、品牌展示、特殊强调
```

### 使用场景对照表 - 优化后标准
| 尺寸    | 使用场景 | 示例 |
|-------|----------|------|
| 24rpx | 状态指示 | 照片删除按钮、消息提示点 |
| 28rpx | 辅助信息 | 联系电话、邮箱地址、箭头、地点信息 |
| 32rpx | 分组标题 | 隐私设置、用户信息标题、弹窗关闭 |
| 40rpx | 页面标题 | 用户协议、关于我们、快捷操作 |
| 48rpx | 功能卡片 | 预算设置、主要功能入口、模式选择 |
| 54rpx | 重要展示 | 应用Logo、首页功能模块 |

## 🎨 使用方法

### 基础用法
```xml
<!-- 使用默认尺寸(32rpx) -->
<custom-icon name="wallet" color="#FF6B6B" />

<!-- 指定尺寸 -->
<custom-icon name="shield" size="28" color="#52c41a" />

<!-- 联系方式小图标 -->
<custom-icon name="phone" size="24" color="#4ECDC4" />
```

### 样式类用法
```xml
<!-- 使用预定义样式类 -->
<custom-icon name="user" customClass="icon-lg icon-primary" />
<custom-icon name="settings" customClass="icon-md icon-secondary" />
```

### 响应式使用
```xml
<!-- 在不同设备上使用不同尺寸 -->
<custom-icon
  name="chart"
  size="{{screenWidth > 750 ? 40 : 32}}"
  color="#45B7D1"
/>
```

## 📚 图标库

### 基础功能图标 (9个)
- `user.svg` - 用户/个人资料
- `settings.svg` - 设置
- `search.svg` - 搜索/发现
- `plus.svg` - 添加/新建
- `bell.svg` - 通知
- `info.svg` - 信息/关于
- `help-circle.svg` - 帮助
- `shield.svg` - 安全/保护
- `wechat.svg` - 微信登录

### 业务功能图标 (4个)
- `wallet.svg` - 钱包/记账
- `chart.svg` - 图表/分析
- `trending-up.svg` - 趋势/统计
- `location.svg` - 位置/地点

### 旅行相关图标 (4个)
- `airplane.svg` - 飞机/旅行
- `suitcase.svg` - 行李箱
- `map.svg` - 地图/规划
- `camera.svg` - 相机/拍照

### 社交相关图标 (2个)
- `heart.svg` - 喜欢/收藏
- `friends.svg` - 好友/分享

### 通讯相关图标 (3个)
- `microphone.svg` - 语音/录音
- `phone.svg` - 电话/联系
- `message.svg` - 邮件/消息

### 数据统计图标 (6个)
- `target.svg` - 目标/预算
- `utensils.svg` - 餐饮美食
- `gamepad.svg` - 娱乐休闲
- `more-horizontal.svg` - 更多选项
- `lightbulb.svg` - 提示/洞察
- `alert-triangle.svg` - 警告/注意

### 其他实用图标 (12个)
- `bookmark.svg` - 书签/收藏
- `calendar.svg` - 日历/日期
- `car.svg` - 汽车/交通
- `clock.svg` - 时钟/时间
- `coffee.svg` - 咖啡/休闲
- `entertainment.svg` - 娱乐/休闲
- `food.svg` - 食物/餐饮
- `map-pin.svg` - 地图标记
- `music.svg` - 音乐/娱乐
- `photo.svg` - 照片/图片
- `shopping-bag.svg` - 购物/消费
- `switch.svg` - 开关/切换
- `tag.svg` - 标签/分类

### UI界面图标 (5个)
- `home.svg` - 首页/主页
- `arrow-right.svg` - 右箭头/导航
- `star.svg` - 星星/评分
- `close.svg` - 关闭/删除
- `check.svg` - 勾选/确认
- `edit.svg` - 编辑/修改
- `minus.svg` - 减号/减少

## ⚡ 性能优化

### 图标复用
- **优先复用**：使用现有图标，避免创建重复资源
- **语义匹配**：选择语义最接近的现有图标
- **适当修改**：通过颜色和尺寸调整适应不同场景

### 加载优化
- **按需加载**：图标文件较小，支持快速加载
- **缓存机制**：浏览器自动缓存SVG文件
- **压缩优化**：SVG文件已经过优化，体积最小

## 🔧 开发规范

### 命名规范
- **语义化命名**：使用功能描述作为文件名
- **小写字母**：文件名使用小写字母和连字符
- **避免缩写**：使用完整单词，提高可读性

### 创建新图标
1. **检查现有**：确认现有图标库中没有合适的图标
2. **设计一致**：遵循现有图标的设计风格
3. **尺寸标准**：使用24x24px的画布尺寸
4. **色彩规范**：使用项目标准色彩和渐变
5. **文件优化**：确保SVG代码简洁高效

### 质量检查
- **视觉检查**：在不同尺寸下检查图标清晰度
- **兼容性测试**：确保在不同设备上正常显示
- **性能测试**：检查加载速度和渲染性能

## 📱 最佳实践

### 设计一致性
- **统一间距**：图标与文字保持一致的间距
- **对齐方式**：确保图标与文字垂直居中对齐
- **颜色搭配**：选择与页面主题协调的颜色

### 用户体验
- **点击区域**：确保图标有足够的点击区域
- **视觉反馈**：添加适当的悬停和点击效果
- **可访问性**：为图标添加适当的语义标签

### 维护管理
- **文档更新**：新增图标时及时更新文档
- **版本控制**：记录图标的修改历史
- **定期清理**：移除不再使用的图标文件

---

**最后更新**：2025-01-25  
**版本**：v1.2.0  
**维护者**：爱巢小记开发团队
