# 爱巢小记功能模块详细设计

## 🎨 UI设计系统 - <PERSON><PERSON> Weapp + 自定义组件

### 组件库使用规范
- **设计原则**：遵循微信小程序设计指南 + 现代化设计语言
- **主要组件库**：Vant Weapp (主力UI组件库)
- **辅助组件库**：WeUI (特定场景使用)
- **自定义组件**：SVG图标系统 + 业务组件
- **视觉风格**：毛玻璃效果 + 渐变色彩 + 现代卡片设计
- **交互体验**：友好礼貌、清晰明确、便捷优雅、统一稳定

### 全局样式系统
```css
/* 品牌色彩 */
--primary-color: #FFB6C1;    /* 主色调-粉色 */
--secondary-color: #4ECDC4;  /* 辅助色-青绿 */
--accent-color: #45B7D1;     /* 功能色-蓝色 */
--warning-color: #faad14;    /* 警告色-黄色 */
--error-color: #ff4d4f;      /* 错误色-红色 */
--success-color: #52c41a;    /* 成功色-绿色 */

/* 毛玻璃效果 */
.glass-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}

/* Vant组件适配 */
.van-cell-group {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
}

/* SVG图标样式 */
.icon-small { width: 24rpx; height: 24rpx; }
.icon-medium { width: 32rpx; height: 32rpx; }
.icon-large { width: 48rpx; height: 48rpx; }
```

## 🏠 首页综合数据展示

### 数据概览卡片设计
```
┌─────────────────────────────────────┐
│  本月财务概览                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│  │日常支出 │ │旅行支出 │ │总预算   │ │
│  │¥1,240  │ │¥2,580  │ │¥5,000  │ │
│  └─────────┘ └─────────┘ └─────────┘ │
│  ████████░░ 76% 已使用              │
└─────────────────────────────────────┘
```

### 快捷操作区域
- **查看详细分析**：功能开发中
- **预算管理**：预算设置，使用情况监控
- **旅行计划**：计划创建，行程管理
- **设置中心**：个人偏好，主题切换

## 🎨 SVG图标系统设计

### 图标设计规范
- **存储位置**：统一存放在 `/images/` 目录
- **命名规范**：功能名称.svg (如: wallet.svg, airplane.svg)
- **设计风格**：渐变色彩 + 现代化设计 + 高光效果
- **尺寸规格**：24x24px 基础尺寸，支持任意缩放
- **大小标准**：12/16/20/24/32/48rpx 六级尺寸体系
- **使用规范**：通过size参数控制大小，保持页面一致性

### 图标分类体系
```
基础功能图标 (8个)：
├── user.svg          # 用户/个人资料
├── settings.svg      # 设置
├── search.svg        # 搜索/发现
├── plus.svg          # 添加/新建
├── bell.svg          # 通知
├── info.svg          # 信息/关于
├── help-circle.svg   # 帮助
└── wechat.svg        # 微信登录

业务功能图标 (4个)：
├── wallet.svg        # 钱包/记账
├── chart.svg         # 图表/分析
├── trending-up.svg   # 趋势/统计
└── location.svg      # 位置/地点

旅行相关图标 (4个)：
├── airplane.svg      # 飞机/旅行
├── suitcase.svg      # 行李箱
├── map.svg           # 地图/规划
└── camera.svg        # 相机/拍照

社交相关图标 (3个)：
├── heart.svg         # 喜欢/收藏
├── friends.svg       # 好友/分享
└── microphone.svg    # 语音/录音
```

### 使用方式
```xml
<!-- 标准用法：使用custom-icon组件 -->
<custom-icon name="wallet" size="24" color="#FF6B6B" />

<!-- 不同尺寸示例 -->
<custom-icon name="phone" size="16" color="#4ECDC4" />    <!-- 联系方式 -->
<custom-icon name="shield" size="20" color="#52c41a" />   <!-- 分组标题 -->
<custom-icon name="info" size="24" color="#4ECDC4" />     <!-- 页面标题 -->
<custom-icon name="wallet" size="32" color="#FF6B6B" />   <!-- 功能卡片 -->

<!-- 使用样式类 -->
<custom-icon name="user" customClass="icon-lg icon-primary" />

<!-- 直接使用image标签（不推荐） -->
<image src="/images/wallet.svg" class="icon-medium" mode="aspectFit" />
```

## 💰 智能记账模块

### 1. 双重记账模式
**日常记账模式**：
- 简化分类：餐饮、交通、购物、娱乐、其他
- 快速记录：金额 + 分类 + 备注
- 月度预算跟踪

**旅行记账模式**：
- 专项分类：交通、住宿、餐饮、门票、购物、其他
- 关联旅行计划
- 地理位置记录
- 照片附件支持

### 2. 智能分类系统
**分类识别算法**：
```javascript
// 基于关键词匹配的智能分类
const categoryRules = {
  transport: ["高铁", "飞机", "出租车", "地铁", "公交", "滴滴"],
  food: ["餐厅", "美食", "早餐", "午餐", "晚餐", "咖啡"],
  hotel: ["酒店", "民宿", "住宿", "宾馆"],
  ticket: ["门票", "景点", "博物馆", "公园"],
  shopping: ["购物", "商场", "超市", "纪念品"]
};

function smartCategorize(description, location) {
  // 1. 关键词匹配
  // 2. 地理位置分析
  // 3. 历史行为学习
  // 4. 返回推荐分类
}
```

### 3. 预算管理系统
**预算设置**：
- 月度总预算
- 分类预算分配
- 旅行专项预算

**预算跟踪**：
- 实时使用率显示
- 超支预警（80%、100%、120%）
- 预算调整建议

**预警机制**：
```javascript
function budgetAlert(userId, category, amount) {
  const budget = getBudget(userId, category);
  const used = getUsedAmount(userId, category);
  const percentage = (used + amount) / budget.total;
  
  if (percentage >= 0.8) {
    sendNotification(userId, {
      type: 'budget_warning',
      message: `${category}预算即将超支`,
      percentage: Math.round(percentage * 100)
    });
  }
}
```

## 🗺️ 旅行规划模块 - 重新设计

### 设计理念
- **现代化界面**：采用卡片式设计，毛玻璃效果，渐变色彩
- **分步骤流程**：将复杂的创建过程分解为简单的步骤
- **直观的导航**：清晰的步骤指示器和进度展示
- **统一的交互**：一致的按钮设计和操作反馈
- **数据可视化**：集成ECharts图表，展示真实旅游数据分析

### 数据分析功能
- **概览统计**：总旅行数、总花费、预算使用率实时展示
- **月度趋势图**：最近12个月的旅行计划数和花费趋势（柱状图+折线图）
- **目的地分布**：热门目的地饼图，显示旅行频次分布
- **旅行时长分布**：短途/中途/长途/超长旅行的比例分析
- **预算分析**：各计划的预算使用情况对比

### 页面架构
```
旅行规划模块
├── 主页面 (index)
│   ├── 欢迎区域 (统计数据展示)
│   ├── 当前计划 (进行中的旅行)
│   ├── 快速操作 (创建、记录、探索、统计)
│   └── 我的计划 (计划列表)
├── 创建计划 (create-plan)
│   ├── 步骤1：基本信息 (标题、目的地、人数)
│   ├── 步骤2：时间预算 (日期、预算、备注)
│   └── 步骤3：确认创建 (信息确认)
├── 计划详情 (plan-detail)
├── 行程规划 (itinerary)
├── 目的地探索 (destination)
└── 旅行记录 (travel-record)
```

### 1. 重新设计的创建流程
```
步骤1：基本信息 → 步骤2：时间预算 → 步骤3：确认创建
```

### 2. 目的地选择页面设计
```
目的地选择页面
├── 搜索区域
│   ├── 智能搜索输入框
│   └── 实时搜索建议
├── 快速选择
│   ├── 地图选择 (调用微信地图)
│   └── 当前位置 (GPS定位)
├── 最近选择
│   ├── 历史选择记录
│   └── 一键清空功能
├── 热门推荐
│   ├── 热门目的地卡片
│   ├── 评分和标签展示
│   └── 精美图片展示
└── 分类浏览
    ├── 国内/国外/周边分类
    ├── 城市列表展示
    └── 详细描述信息
```

#### 设计特色
- **多种选择方式**：搜索、地图、分类、推荐等多种方式满足不同用户需求
- **智能记忆**：记录用户最近选择，提升使用效率
- **视觉丰富**：热门目的地配图和评分，增强选择信心
- **交互友好**：实时搜索建议，快速定位目标

### 3. 数据管理系统
```
数据服务架构
├── TravelDataService (数据服务类)
│   ├── 旅行计划管理 (CRUD操作)
│   ├── 统计数据计算 (进度、预算等)
│   ├── 目的地数据管理 (搜索、分类、推荐)
│   └── 本地存储管理 (缓存、持久化)
├── DataInitializer (数据初始化工具)
│   ├── 示例数据创建 (首次使用体验)
│   ├── 数据完整性检查 (错误修复)
│   ├── 数据导入导出 (备份恢复)
│   └── 数据统计分析 (使用情况)
└── 存储键管理
    ├── travel_plans (旅行计划)
    ├── popular_destinations (热门目的地)
    ├── recent_destinations (最近选择)
    └── user_travel_preferences (用户偏好)
```

#### 数据管理特色
- **动态数据**：移除所有硬编码，实现真实的数据管理
- **智能初始化**：首次使用自动创建示例数据
- **数据完整性**：自动检查和修复数据问题
- **本地存储**：高效的本地数据管理和缓存策略

### 2. 智能路线优化算法
**TSP问题求解**：
```javascript
// 旅行商问题的近似解算法
function optimizeRoute(attractions, startPoint, constraints) {
  // 1. 计算景点间距离矩阵
  const distanceMatrix = calculateDistances(attractions);
  
  // 2. 考虑约束条件
  const timeConstraints = getTimeConstraints(attractions);
  const userPreferences = getUserPreferences();
  
  // 3. 使用贪心算法 + 2-opt优化
  let route = greedyTSP(distanceMatrix, startPoint);
  route = twoOptImprovement(route, distanceMatrix);
  
  // 4. 时间安排优化
  return scheduleOptimization(route, timeConstraints);
}
```

### 3. 行程时间安排
**时间分配算法**：
- 景点游览时长预估
- 交通时间计算
- 用餐时间安排
- 休息时间预留

## 🔍 发现功能模块

### 1. 智能推荐系统
**推荐算法架构**：
```
用户画像 + 内容特征 + 协同过滤 + 热度排序 = 个性化推荐
```

**推荐策略**：
```javascript
function generateRecommendations(userId, destination) {
  // 1. 用户画像分析
  const userProfile = analyzeUserProfile(userId);
  
  // 2. 基于内容的推荐
  const contentBased = contentBasedRecommend(userProfile, destination);
  
  // 3. 协同过滤推荐
  const collaborative = collaborativeFilter(userId, destination);
  
  // 4. 热度排序
  const trending = getTrendingAttractions(destination);
  
  // 5. 融合排序
  return mergeAndRank([contentBased, collaborative, trending]);
}
```

### 2. 用户行为分析
**行为权重设置**：
- 浏览：1分
- 点赞：3分
- 收藏：5分
- 分享：8分
- 评论：10分

### 3. 景点排名算法
```javascript
function calculateAttractionScore(attraction, userBehaviors) {
  const baseScore = attraction.rating * 20; // 基础评分
  const popularityScore = Math.log(attraction.reviewCount + 1) * 10; // 热度分
  const behaviorScore = calculateBehaviorScore(attraction.id, userBehaviors); // 行为分
  const freshnessScore = calculateFreshnessScore(attraction.updateTime); // 新鲜度
  
  return baseScore + popularityScore + behaviorScore + freshnessScore;
}
```

## 👥 社交分享模块

### 1. 好友系统
**好友关系管理**：
- 好友申请/接受/拒绝
- 好友列表管理
- 隐私设置控制

### 2. 内容分享机制
**分享类型**：
- 旅行计划分享
- 记账记录分享
- 旅行回顾分享
- 攻略经验分享

### 3. 协作规划功能
**多人协作流程**：
```
发起者创建计划 → 邀请协作者 → 共同编辑行程 → 费用分摊 → 实时同步
```

**权限管理**：
- 创建者：完全控制权
- 协作者：编辑权限
- 观察者：只读权限

## 📊 数据分析模块

### 功能状态
数据分析相关功能已移除，专注于核心记账和旅行规划功能。

## 🎨 WeUI组件库集成

### 组件设计规范

#### 核心组件使用
- **按钮组件**：统一的操作按钮样式
- **输入框组件**：表单输入控件
- **选择器组件**：数据选择控件
- **反馈组件**：用户操作反馈

### 样式定制原则
1. **使用ext-class**：通过ext-class进行样式定制
2. **保持品牌色**：确保组件体现项目色彩
3. **毛玻璃效果**：在组件上应用backdrop-filter
4. **统一圆角**：使用24rpx圆角保持设计一致性

### 最佳实践
- **组件选择**：优先使用成熟组件库，减少自定义开发
- **样式覆盖**：通过全局样式和ext-class进行定制
- **交互反馈**：利用组件的原生交互效果
- **可访问性**：保持组件的无障碍特性

