/**app.wxss**/
/* 引入统一设计系统 v2.0 */
@import "styles/design-tokens.wxss";
@import "styles/glass-effects.wxss";
@import "styles/button-system.wxss";
@import "styles/utilities.wxss";
/* 保留必要的组件覆盖 */
@import "styles/vant-override.wxss";
/* 重影问题修复样式已整合到glass-effects.wxss中 */

/* 全局样式 */
page {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: var(--font-size-md);
  /* 重影修复样式已整合到glass-effects.wxss中 */
}

/* 全局容器 */
.container {
  min-height: 100vh;
  padding: var(--spacing-lg);
  box-sizing: border-box;
}

/* 页面容器 */
.page {
  padding: var(--spacing-md);
}

/* 卡片容器 */
.card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}


