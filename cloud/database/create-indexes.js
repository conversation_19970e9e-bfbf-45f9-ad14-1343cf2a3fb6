// 数据库索引优化脚本
// 在微信开发者工具的云开发控制台中运行此脚本
// 解决性能警告：发起的如下数据库查询经自动检测发现以下问题，且可能缺乏高效的索引支持

// 获取数据库实例
const db = cloud.database()

// 1. records 集合索引

// 用户ID + 创建时间索引（用于获取用户记录列表）
db.collection('records').createIndex({
  "_openid": 1,
  "createTime": -1
}, {
  name: "user_createTime_index",
  background: true
})

// 用户ID + 日期索引（用于按日期查询）
db.collection('records').createIndex({
  "_openid": 1,
  "date": 1
}, {
  name: "user_date_index",
  background: true
})

// 用户ID + 类型 + 日期索引（用于分类统计）
db.collection('records').createIndex({
  "_openid": 1,
  "type": 1,
  "date": 1
}, {
  name: "user_type_date_index",
  background: true
})

// 用户ID + 分类索引（用于分类查询）
db.collection('records').createIndex({
  "_openid": 1,
  "category.main": 1
}, {
  name: "user_category_index",
  background: true
})

// 2. travel_plans 集合索引
console.log('创建 travel_plans 索引...')

// 用户ID + 创建时间索引
db.collection('travel_plans').createIndex({
  "_openid": 1,
  "createdAt": -1
}, {
  name: "openid_createTime_index",
  background: true
})

// 用户ID + 状态索引（用于查询特定状态的计划）
db.collection('travel_plans').createIndex({
  "_openid": 1,
  "status": 1
}, {
  name: "openid_status_index",
  background: true
})

// 用户ID + 开始日期索引（用于按时间查询）
db.collection('travel_plans').createIndex({
  "_openid": 1,
  "startDate": 1
}, {
  name: "openid_startDate_index",
  background: true
})

// 3. users 集合索引
console.log('创建 users 索引...')

// openid 唯一索引（如果还没有的话）
try {
  db.collection('users').createIndex({
    "openid": 1
  }, {
    unique: true,
    name: "openid_unique_index",
    background: true
  })
} catch (e) {
  console.log('openid 索引已存在')
}

// _openid 索引（用于云函数查询）
db.collection('users').createIndex({
  "_openid": 1
}, {
  name: "cloud_openid_index",
  background: true
})

// 最后登录时间索引（用于用户活跃度统计）
db.collection('users').createIndex({
  "lastLoginTime": -1
}, {
  name: "lastLogin_index",
  background: true
})

// 4. categories 集合索引
console.log('创建 categories 索引...')

// 类型索引（用于按类型查询分类）
db.collection('categories').createIndex({
  "type": 1
}, {
  name: "type_index",
  background: true
})

// 主分类索引
db.collection('categories').createIndex({
  "main": 1
}, {
  name: "main_category_index",
  background: true
})

// 5. records 集合索引（通用记录）
console.log('创建 records 索引...')

// 用户ID + 创建时间索引
db.collection('records').createIndex({
  "_openid": 1,
  "createTime": -1
}, {
  name: "openid_createTime_index",
  background: true
})

// 用户ID + 类型索引
db.collection('records').createIndex({
  "_openid": 1,
  "type": 1
}, {
  name: "openid_type_index",
  background: true
})

console.log('所有索引创建完成！')

// 查看索引创建结果
console.log('=== 索引创建结果 ===')

console.log('records 索引:')
db.collection('records').listIndexes()

console.log('travel_plans 索引:')
db.collection('travel_plans').listIndexes()

console.log('users 索引:')
db.collection('users').listIndexes()

console.log('categories 索引:')
db.collection('categories').listIndexes()

console.log('records 索引:')
db.collection('records').listIndexes()

console.log('=== 索引优化完成 ===')
