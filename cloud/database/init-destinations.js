// 目的地数据初始化脚本
// 在云开发控制台运行此脚本来初始化真实的目的地数据

const db = cloud.database()

// 创建destinations集合
db.createCollection('destinations')

// 创建destination_visits集合
db.createCollection('destination_visits')

// 初始化真实的目的地数据
const realDestinations = [
  {
    name: '北京',
    province: '北京',
    country: '中国',
    description: '中华人民共和国首都，历史文化名城，拥有故宫、长城等世界文化遗产',
    coordinates: {
      latitude: 39.9042,
      longitude: 116.4074
    },
    tags: ['历史', '文化', '首都', '古建筑'],
    images: [
      'https://example.com/beijing1.jpg',
      'https://example.com/beijing2.jpg'
    ],
    isHot: true, // 保留字段，将在发现页面中使用
    isNearby: false,
    status: 'active',
    visitCount: 15680,
    rating: 4.8,
    reviewCount: 3240,
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: '上海',
    province: '上海',
    country: '中国',
    description: '国际化大都市，中国经济中心，现代化建筑与历史文化并存',
    coordinates: {
      latitude: 31.2304,
      longitude: 121.4737
    },
    tags: ['现代化', '经济中心', '国际化', '购物'],
    images: [
      'https://example.com/shanghai1.jpg',
      'https://example.com/shanghai2.jpg'
    ],
    isHot: true,
    isNearby: false,
    status: 'active',
    visitCount: 18920,
    rating: 4.7,
    reviewCount: 4150,
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: '杭州',
    province: '浙江',
    country: '中国',
    description: '人间天堂，以西湖美景闻名，江南水乡文化的代表',
    coordinates: {
      latitude: 30.2741,
      longitude: 120.1551
    },
    tags: ['西湖', '江南', '水乡', '文化'],
    images: [
      'https://example.com/hangzhou1.jpg',
      'https://example.com/hangzhou2.jpg'
    ],
    isHot: true,
    isNearby: false,
    status: 'active',
    visitCount: 12450,
    rating: 4.9,
    reviewCount: 2890,
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: '成都',
    province: '四川',
    country: '中国',
    description: '天府之国，休闲之都，以美食和大熊猫闻名',
    coordinates: {
      latitude: 30.5728,
      longitude: 104.0668
    },
    tags: ['美食', '大熊猫', '休闲', '川菜'],
    images: [
      'https://example.com/chengdu1.jpg',
      'https://example.com/chengdu2.jpg'
    ],
    isHot: true,
    isNearby: false,
    status: 'active',
    visitCount: 14320,
    rating: 4.8,
    reviewCount: 3560,
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: '大理',
    province: '云南',
    country: '中国',
    description: '风花雪月的浪漫古城，白族文化的发源地',
    coordinates: {
      latitude: 25.6066,
      longitude: 100.2675
    },
    tags: ['古城', '白族文化', '风花雪月', '浪漫'],
    images: [
      'https://example.com/dali1.jpg',
      'https://example.com/dali2.jpg'
    ],
    isHot: true,
    isNearby: false,
    status: 'active',
    visitCount: 9870,
    rating: 4.8,
    reviewCount: 2340,
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: '三亚',
    province: '海南',
    country: '中国',
    description: '热带海滨度假胜地，中国的夏威夷',
    coordinates: {
      latitude: 18.2479,
      longitude: 109.5146
    },
    tags: ['海滨', '度假', '热带', '海岛'],
    images: [
      'https://example.com/sanya1.jpg',
      'https://example.com/sanya2.jpg'
    ],
    isHot: true,
    isNearby: false,
    status: 'active',
    visitCount: 11230,
    rating: 4.7,
    reviewCount: 2780,
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: '西安',
    province: '陕西',
    country: '中国',
    description: '十三朝古都，兵马俑的故乡，丝绸之路的起点',
    coordinates: {
      latitude: 34.3416,
      longitude: 108.9398
    },
    tags: ['古都', '兵马俑', '历史', '丝绸之路'],
    images: [
      'https://example.com/xian1.jpg',
      'https://example.com/xian2.jpg'
    ],
    isHot: true,
    isNearby: false,
    status: 'active',
    visitCount: 13450,
    rating: 4.8,
    reviewCount: 3120,
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: '厦门',
    province: '福建',
    country: '中国',
    description: '海上花园，文艺小城，鼓浪屿的浪漫情怀',
    coordinates: {
      latitude: 24.4798,
      longitude: 118.0894
    },
    tags: ['海岛', '文艺', '鼓浪屿', '浪漫'],
    images: [
      'https://example.com/xiamen1.jpg',
      'https://example.com/xiamen2.jpg'
    ],
    isHot: true,
    isNearby: false,
    status: 'active',
    visitCount: 8760,
    rating: 4.6,
    reviewCount: 2150,
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: '东京',
    province: '东京都',
    country: '日本',
    description: '日本首都，现代与传统完美融合的国际大都市',
    coordinates: {
      latitude: 35.6762,
      longitude: 139.6503
    },
    tags: ['国际化', '现代', '传统', '购物'],
    images: [
      'https://example.com/tokyo1.jpg',
      'https://example.com/tokyo2.jpg'
    ],
    isHot: true,
    isNearby: false,
    status: 'active',
    visitCount: 6540,
    rating: 4.7,
    reviewCount: 1890,
    createTime: new Date(),
    updateTime: new Date()
  },
  {
    name: '首尔',
    province: '首尔特别市',
    country: '韩国',
    description: '韩国首都，时尚购物天堂，韩流文化的发源地',
    coordinates: {
      latitude: 37.5665,
      longitude: 126.9780
    },
    tags: ['时尚', '购物', '韩流', '美食'],
    images: [
      'https://example.com/seoul1.jpg',
      'https://example.com/seoul2.jpg'
    ],
    isHot: false,
    isNearby: false,
    status: 'active',
    visitCount: 4320,
    rating: 4.5,
    reviewCount: 1240,
    createTime: new Date(),
    updateTime: new Date()
  }
]

// 批量插入目的地数据
db.collection('destinations').add({
  data: realDestinations,
  success: function(res) {
    console.log('目的地数据初始化成功，插入了', realDestinations.length, '条记录')
    console.log('插入的记录ID:', res._ids)
  },
  fail: function(error) {
    console.error('目的地数据初始化失败:', error)
  }
})

console.log('目的地数据初始化脚本执行完成')
