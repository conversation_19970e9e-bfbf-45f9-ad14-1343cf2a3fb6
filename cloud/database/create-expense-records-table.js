// {{ AURA-X: Add - 创建expense_records表和索引. Approval: 寸止(ID:1738056000). }}
// expense_records表创建和索引优化脚本
// 在微信开发者工具的云开发控制台中运行此脚本

const db = cloud.database()

console.log('开始创建 expense_records 表和索引...')

// 1. 创建 expense_records 集合
try {
  db.createCollection('expense_records')
  console.log('✅ expense_records 集合创建成功')
} catch (error) {
  console.log('⚠️ expense_records 集合可能已存在:', error.message)
}

// 2. 创建 expense_records 表的索引

// 用户ID + 创建时间索引（用于获取用户记录列表）
db.collection('expense_records').createIndex({
  "_openid": 1,
  "createTime": -1
}, {
  name: "expense_user_createTime_index",
  background: true
})

// 用户ID + 日期索引（用于按日期查询）
db.collection('expense_records').createIndex({
  "_openid": 1,
  "date": 1
}, {
  name: "expense_user_date_index",
  background: true
})

// 用户ID + 类型索引（用于分类查询）
db.collection('expense_records').createIndex({
  "_openid": 1,
  "record_type": 1
}, {
  name: "expense_user_type_index",
  background: true
})

// 用户ID + 模式索引（用于区分日常和旅行记录）
db.collection('expense_records').createIndex({
  "_openid": 1,
  "type": 1
}, {
  name: "expense_user_mode_index",
  background: true
})

// 旅行计划ID索引（用于查询特定计划的费用记录）
db.collection('expense_records').createIndex({
  "travel_plan_id": 1
}, {
  name: "expense_travel_plan_index",
  background: true
})

// 用户ID + 旅行计划ID索引（用于查询用户在特定计划下的费用）
db.collection('expense_records').createIndex({
  "_openid": 1,
  "travel_plan_id": 1
}, {
  name: "expense_user_travel_plan_index",
  background: true
})

// 用户ID + 分类索引（用于分类统计）
db.collection('expense_records').createIndex({
  "_openid": 1,
  "category.main": 1
}, {
  name: "expense_user_category_index",
  background: true
})

// 复合索引：用户ID + 类型 + 日期（用于高效的分类统计查询）
db.collection('expense_records').createIndex({
  "_openid": 1,
  "record_type": 1,
  "date": 1
}, {
  name: "expense_user_type_date_index",
  background: true
})

// 复合索引：用户ID + 模式 + 创建时间（用于区分日常和旅行记录的时间查询）
db.collection('expense_records').createIndex({
  "_openid": 1,
  "type": 1,
  "createTime": -1
}, {
  name: "expense_user_mode_createTime_index",
  background: true
})

console.log('✅ expense_records 索引创建完成')

// 3. 验证表结构 - 插入一条测试记录来验证字段结构
const testRecord = {
  amount: 0.01,
  date: new Date().toISOString().split('T')[0],
  description: '测试记录-请删除',
  category: {
    main: '测试',
    sub: '测试子分类',
    icon: '🧪',
    color: '#999999'
  },
  location: {
    name: '测试地点',
    address: '测试地址',
    latitude: 0,
    longitude: 0
  },
  images: [],
  type: 'daily',                    // "daily" 或 "travel"
  record_type: 'expense',           // "expense" 或 "income"
  travel_plan_id: null,             // 旅行计划ID，日常记录为null
  _openid: 'test_openid_please_delete',
  createTime: new Date(),
  updateTime: new Date(),
  isTestRecord: true                // 标记为测试记录
}

// {{ AURA-X: Modify - 修复异步函数调用问题. Approval: 寸止(ID:1738056000). }}
// 注意：在云开发控制台中，需要将此代码包装在异步函数中执行
async function testTableStructure() {
  try {
    const insertResult = await db.collection('expense_records').add({
      data: testRecord
    })
    console.log('✅ 测试记录插入成功，记录ID:', insertResult._id)

    // 立即删除测试记录
    await db.collection('expense_records').doc(insertResult._id).remove()
    console.log('✅ 测试记录已删除')

  } catch (error) {
    console.error('❌ 测试记录操作失败:', error)
  }
}

// 执行测试（在云开发控制台中需要手动调用）
// testTableStructure()

// 4. 检查现有数据迁移需求
console.log('检查是否需要数据迁移...')

// {{ AURA-X: Modify - 修复异步函数调用问题. Approval: 寸止(ID:1738056000). }}
async function checkMigrationNeeds() {
  try {
    // 检查旧表 records 中是否有旅行模式的记录
    const oldRecordsResult = await db.collection('records')
      .where({
        mode: 'travel'
      })
      .limit(5)
      .get()

    if (oldRecordsResult.data && oldRecordsResult.data.length > 0) {
      console.log(`⚠️ 发现 ${oldRecordsResult.data.length} 条旧格式的旅行记录，建议进行数据迁移`)
      console.log('旧记录示例:', JSON.stringify(oldRecordsResult.data[0], null, 2))
    } else {
      console.log('✅ 未发现需要迁移的旧记录')
    }

  } catch (error) {
    console.log('⚠️ 检查旧记录时出错:', error.message)
  }
}

// 执行检查（在云开发控制台中需要手动调用）
// checkMigrationNeeds()

// 5. 验证索引创建结果
console.log('验证索引创建结果...')

// {{ AURA-X: Modify - 修复异步函数调用问题. Approval: 寸止(ID:1738056000). }}
async function verifyIndexes() {
  try {
    const indexes = await db.collection('expense_records').listIndexes()
    console.log('✅ expense_records 表索引列表:')
    indexes.forEach((index, i) => {
      console.log(`  ${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`)
    })
  } catch (error) {
    console.log('⚠️ 获取索引列表失败:', error.message)
  }
}

// 执行验证（在云开发控制台中需要手动调用）
// verifyIndexes()

console.log('=== expense_records 表创建和优化完成 ===')

// 6. 提供数据迁移脚本（可选执行）
console.log(`
如需迁移旧数据，请执行以下步骤：
1. 备份现有数据
2. 运行数据迁移脚本
3. 验证迁移结果
4. 清理旧数据（可选）

数据迁移脚本将在单独的文件中提供。
`)

// 7. 字段结构说明
console.log(`
expense_records 表字段结构：
- _id: 记录唯一标识（自动生成）
- _openid: 用户openid（自动添加）
- amount: 金额（数字）
- date: 日期（字符串，YYYY-MM-DD格式）
- description: 描述（字符串）
- category: 分类对象 { main, sub, icon, color }
- location: 位置对象 { name, address, latitude, longitude }
- images: 图片数组
- type: 模式（"daily" 或 "travel"）
- record_type: 记录类型（"expense" 或 "income"）
- travel_plan_id: 旅行计划ID（字符串，日常记录为null）
- createTime: 创建时间（Date对象）
- updateTime: 更新时间（Date对象）
`)

console.log('脚本执行完成！')
