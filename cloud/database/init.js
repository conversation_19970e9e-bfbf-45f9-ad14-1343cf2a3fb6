// 数据库初始化脚本
// 在微信开发者工具的云开发控制台中运行此脚本

// 1. 创建用户集合
db.createCollection('users')

// 2. 创建用户集合索引
db.collection('users').createIndex({
  "openid": 1
}, {
  unique: true,
  name: "openid_unique"
})

// 3. 创建记账记录集合
db.createCollection('records')

// 4. 创建记账记录索引
db.collection('records').createIndex({
  "openid": 1,
  "createTime": -1
}, {
  name: "user_time_index"
})

// 5. 创建分类集合
db.createCollection('categories')

// 6. 插入默认分类数据
db.collection('categories').add({
  data: [
    {
      name: '餐饮',
      icon: '🍽️',
      type: 'expense',
      color: '#FF6B6B',
      isDefault: true,
      createTime: new Date()
    },
    {
      name: '交通',
      icon: '🚗',
      type: 'expense',
      color: '#4ECDC4',
      isDefault: true,
      createTime: new Date()
    },
    {
      name: '购物',
      icon: '🛍️',
      type: 'expense',
      color: '#45B7D1',
      isDefault: true,
      createTime: new Date()
    },
    {
      name: '娱乐',
      icon: '🎮',
      type: 'expense',
      color: '#96CEB4',
      isDefault: true,
      createTime: new Date()
    },
    {
      name: '工资',
      icon: '💰',
      type: 'income',
      color: '#FFEAA7',
      isDefault: true,
      createTime: new Date()
    },
    {
      name: '奖金',
      icon: '🎁',
      type: 'income',
      color: '#DDA0DD',
      isDefault: true,
      createTime: new Date()
    }
  ]
})

console.log('数据库初始化完成！')
