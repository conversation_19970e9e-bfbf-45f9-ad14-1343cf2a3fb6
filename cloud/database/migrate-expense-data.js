// {{ AURA-X: Add - 数据迁移脚本，将旧格式记录迁移到新表. Approval: 寸止(ID:1738056000). }}
// 数据迁移脚本：将 records 表中的旅行记录迁移到 expense_records 表
// 在微信开发者工具的云开发控制台中运行此脚本

const db = cloud.database()
const _ = db.command

console.log('开始数据迁移...')

// 迁移配置
const MIGRATION_CONFIG = {
  batchSize: 100,           // 每批处理的记录数
  dryRun: false,            // 是否为试运行（true=不实际迁移，只统计）
  backupOldData: true,      // 是否备份旧数据
  deleteOldData: false      // 迁移完成后是否删除旧数据（谨慎使用）
}

// 统计信息
let migrationStats = {
  totalOldRecords: 0,
  migratedRecords: 0,
  failedRecords: 0,
  skippedRecords: 0,
  errors: []
}

// 主迁移函数
async function migrateExpenseData() {
  try {
    console.log('=== 数据迁移开始 ===')
    console.log('配置:', JSON.stringify(MIGRATION_CONFIG, null, 2))

    // 1. 统计需要迁移的记录数量
    const countResult = await db.collection('records')
      .where({
        mode: 'travel'  // 只迁移旅行模式的记录
      })
      .count()
    
    migrationStats.totalOldRecords = countResult.total
    console.log(`发现 ${migrationStats.totalOldRecords} 条需要迁移的旧记录`)

    if (migrationStats.totalOldRecords === 0) {
      console.log('✅ 没有需要迁移的记录')
      return
    }

    // 2. 分批迁移数据
    let skip = 0
    const batchSize = MIGRATION_CONFIG.batchSize

    while (skip < migrationStats.totalOldRecords) {
      console.log(`处理第 ${Math.floor(skip / batchSize) + 1} 批，记录 ${skip + 1}-${Math.min(skip + batchSize, migrationStats.totalOldRecords)}`)
      
      // 获取一批旧记录
      const oldRecordsResult = await db.collection('records')
        .where({
          mode: 'travel'
        })
        .orderBy('createTime', 'asc')
        .skip(skip)
        .limit(batchSize)
        .get()

      if (!oldRecordsResult.data || oldRecordsResult.data.length === 0) {
        break
      }

      // 处理这批记录
      await processBatch(oldRecordsResult.data)
      
      skip += batchSize
    }

    // 3. 输出迁移结果
    console.log('=== 数据迁移完成 ===')
    console.log('迁移统计:', JSON.stringify(migrationStats, null, 2))

    if (migrationStats.errors.length > 0) {
      console.log('错误详情:')
      migrationStats.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`)
      })
    }

  } catch (error) {
    console.error('数据迁移失败:', error)
    migrationStats.errors.push(`迁移过程异常: ${error.message}`)
  }
}

// 处理一批记录
async function processBatch(oldRecords) {
  for (const oldRecord of oldRecords) {
    try {
      // 检查是否已经迁移过
      const existingRecord = await db.collection('expense_records')
        .where({
          _openid: oldRecord._openid,
          amount: oldRecord.amount,
          date: oldRecord.date,
          description: oldRecord.description || '',
          'category.main': oldRecord.category?.main || ''
        })
        .get()

      if (existingRecord.data && existingRecord.data.length > 0) {
        console.log(`跳过已存在的记录: ${oldRecord._id}`)
        migrationStats.skippedRecords++
        continue
      }

      // 转换数据格式
      const newRecord = convertRecordFormat(oldRecord)

      if (!MIGRATION_CONFIG.dryRun) {
        // 插入新记录
        await db.collection('expense_records').add({
          data: newRecord
        })
      }

      migrationStats.migratedRecords++
      
      if (migrationStats.migratedRecords % 10 === 0) {
        console.log(`已迁移 ${migrationStats.migratedRecords} 条记录`)
      }

    } catch (error) {
      console.error(`迁移记录失败 ${oldRecord._id}:`, error.message)
      migrationStats.failedRecords++
      migrationStats.errors.push(`记录 ${oldRecord._id}: ${error.message}`)
    }
  }
}

// 转换记录格式
function convertRecordFormat(oldRecord) {
  return {
    // 基本字段
    amount: oldRecord.amount || 0,
    date: oldRecord.date || new Date().toISOString().split('T')[0],
    description: oldRecord.description || oldRecord.category?.main || '支出',
    
    // 分类信息
    category: {
      main: oldRecord.category?.main || '其他',
      sub: oldRecord.category?.sub || '',
      icon: oldRecord.category?.icon || '💰',
      color: oldRecord.category?.color || '#999999'
    },
    
    // 位置信息
    location: oldRecord.location || {
      name: '',
      address: '',
      latitude: 0,
      longitude: 0
    },
    
    // 图片
    images: oldRecord.images || [],
    
    // 类型字段（新格式）
    type: 'travel',                           // 固定为 travel
    record_type: oldRecord.type || 'expense', // expense 或 income
    
    // 旅行计划ID（字段名转换）
    travel_plan_id: oldRecord.planId || null,
    
    // 用户信息
    _openid: oldRecord._openid,
    
    // 时间信息
    createTime: oldRecord.createTime || new Date(),
    updateTime: new Date(),
    
    // 迁移标记
    migratedFrom: 'records',
    migratedAt: new Date(),
    originalId: oldRecord._id
  }
}

// 备份旧数据（可选）
async function backupOldData() {
  if (!MIGRATION_CONFIG.backupOldData) {
    return
  }

  try {
    console.log('开始备份旧数据...')
    
    // 创建备份集合
    const backupCollectionName = `records_backup_${Date.now()}`
    db.createCollection(backupCollectionName)
    
    // 获取所有旅行记录
    const allTravelRecords = await db.collection('records')
      .where({
        mode: 'travel'
      })
      .get()
    
    if (allTravelRecords.data && allTravelRecords.data.length > 0) {
      // 批量插入备份数据
      const batchSize = 100
      for (let i = 0; i < allTravelRecords.data.length; i += batchSize) {
        const batch = allTravelRecords.data.slice(i, i + batchSize)
        await db.collection(backupCollectionName).add({
          data: batch
        })
      }
      
      console.log(`✅ 备份完成，共备份 ${allTravelRecords.data.length} 条记录到 ${backupCollectionName}`)
    }
    
  } catch (error) {
    console.error('备份失败:', error)
    migrationStats.errors.push(`备份失败: ${error.message}`)
  }
}

// 清理旧数据（谨慎使用）
async function cleanupOldData() {
  if (!MIGRATION_CONFIG.deleteOldData) {
    return
  }

  try {
    console.log('开始清理旧数据...')
    
    const deleteResult = await db.collection('records')
      .where({
        mode: 'travel'
      })
      .remove()
    
    console.log(`✅ 清理完成，删除了 ${deleteResult.stats.removed} 条旧记录`)
    
  } catch (error) {
    console.error('清理旧数据失败:', error)
    migrationStats.errors.push(`清理失败: ${error.message}`)
  }
}

// 验证迁移结果
async function validateMigration() {
  try {
    console.log('验证迁移结果...')
    
    // 统计新表记录数
    const newRecordsCount = await db.collection('expense_records')
      .where({
        type: 'travel'
      })
      .count()
    
    // 统计旧表记录数
    const oldRecordsCount = await db.collection('records')
      .where({
        mode: 'travel'
      })
      .count()
    
    console.log(`新表记录数: ${newRecordsCount.total}`)
    console.log(`旧表记录数: ${oldRecordsCount.total}`)
    
    if (newRecordsCount.total >= migrationStats.migratedRecords) {
      console.log('✅ 迁移验证通过')
    } else {
      console.log('⚠️ 迁移验证失败，记录数不匹配')
    }
    
  } catch (error) {
    console.error('验证迁移结果失败:', error)
  }
}

// 执行迁移
async function runMigration() {
  // 1. 备份旧数据
  await backupOldData()
  
  // 2. 执行迁移
  await migrateExpenseData()
  
  // 3. 验证结果
  await validateMigration()
  
  // 4. 清理旧数据（可选）
  await cleanupOldData()
  
  console.log('迁移脚本执行完成！')
}

// 启动迁移
runMigration().catch(error => {
  console.error('迁移脚本执行失败:', error)
})
