/**
 * 网页爬虫服务
 * 实现圆周旅迹的魔法解析功能
 */

const axios = require('axios')
const cheerio = require('cheerio')

class WebCrawlerService {
  constructor() {
    this.timeout = 15000 // 15秒超时
    this.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1'
  }

  /**
   * 魔法解析 - 圆周旅迹核心功能
   * @param {string} url 链接地址
   * @returns {object} 解析结果
   */
  async magicParse(url) {
    try {
      console.log('开始魔法解析:', url)

      // 检测链接类型
      const linkType = this.detectLinkType(url)
      console.log('链接类型:', linkType)

      // 获取网页内容
      const htmlContent = await this.fetchWebContent(url)
      
      // 根据平台类型进行专门解析
      let parseResult
      switch (linkType) {
        case 'xiaohongshu':
          parseResult = await this.parseXiaohongshu(htmlContent, url)
          break
        case 'wechat':
          parseResult = await this.parseWechatArticle(htmlContent, url)
          break
        case 'weibo':
          parseResult = await this.parseWeibo(htmlContent, url)
          break
        default:
          parseResult = await this.parseGeneral(htmlContent, url)
      }

      return {
        success: true,
        data: {
          ...parseResult,
          originalUrl: url,
          linkType: linkType,
          parsedAt: new Date().toISOString()
        }
      }
    } catch (error) {
      console.error('魔法解析失败:', error)
      return {
        success: false,
        message: `解析失败: ${error.message}`,
        error: error.stack
      }
    }
  }

  /**
   * 检测链接类型
   * @param {string} url 链接地址
   * @returns {string} 链接类型
   */
  detectLinkType(url) {
    if (url.includes('xiaohongshu.com') || url.includes('xhslink.com')) {
      return 'xiaohongshu'
    }
    if (url.includes('mp.weixin.qq.com')) {
      return 'wechat'
    }
    if (url.includes('weibo.com') || url.includes('weibo.cn')) {
      return 'weibo'
    }
    if (url.includes('dianping.com')) {
      return 'dianping'
    }
    if (url.includes('mafengwo.cn')) {
      return 'mafengwo'
    }
    return 'general'
  }

  /**
   * 获取网页内容
   * @param {string} url 网页地址
   * @returns {string} HTML内容
   */
  async fetchWebContent(url) {
    try {
      const response = await axios.get(url, {
        timeout: this.timeout,
        headers: {
          'User-Agent': this.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1'
        },
        maxRedirects: 5
      })

      return response.data
    } catch (error) {
      console.error('获取网页内容失败:', error)
      throw new Error(`无法访问链接: ${error.message}`)
    }
  }

  /**
   * 解析小红书内容
   * @param {string} html HTML内容
   * @param {string} url 原始链接
   * @returns {object} 解析结果
   */
  async parseXiaohongshu(html, url) {
    const $ = cheerio.load(html)
    
    // 提取标题
    const title = $('title').text() || 
                  $('h1').first().text() || 
                  $('.note-title').text() ||
                  '小红书攻略'

    // 提取内容文本
    const contentSelectors = [
      '.note-content',
      '.content',
      '.desc',
      '.note-text',
      'p'
    ]
    
    let content = ''
    for (const selector of contentSelectors) {
      const text = $(selector).text().trim()
      if (text && text.length > content.length) {
        content = text
      }
    }

    // 提取图片
    const images = []
    $('img').each((i, elem) => {
      const src = $(elem).attr('src') || $(elem).attr('data-src')
      if (src && !src.includes('avatar') && !src.includes('icon')) {
        images.push(src)
      }
    })

    return {
      title: title.trim(),
      content: content,
      images: images.slice(0, 9), // 最多9张图片
      platform: '小红书',
      type: 'travel_guide'
    }
  }

  /**
   * 解析微信公众号文章
   * @param {string} html HTML内容
   * @param {string} url 原始链接
   * @returns {object} 解析结果
   */
  async parseWechatArticle(html, url) {
    const $ = cheerio.load(html)
    
    // 提取标题
    const title = $('#activity-name').text() || 
                  $('.rich_media_title').text() || 
                  $('h1').first().text() ||
                  '微信文章'

    // 提取内容
    const content = $('.rich_media_content').text() || 
                   $('#js_content').text() || 
                   $('.content').text() ||
                   $('body').text()

    // 提取图片
    const images = []
    $('.rich_media_content img, #js_content img').each((i, elem) => {
      const src = $(elem).attr('data-src') || $(elem).attr('src')
      if (src) {
        images.push(src)
      }
    })

    return {
      title: title.trim(),
      content: content.trim(),
      images: images.slice(0, 9),
      platform: '微信公众号',
      type: 'travel_guide'
    }
  }

  /**
   * 解析微博内容
   * @param {string} html HTML内容
   * @param {string} url 原始链接
   * @returns {object} 解析结果
   */
  async parseWeibo(html, url) {
    const $ = cheerio.load(html)
    
    const title = $('.txt').first().text() || 
                  $('.weibo-text').text() || 
                  '微博内容'

    const content = $('.txt').text() || 
                   $('.weibo-text').text() ||
                   $('.content').text()

    const images = []
    $('.pic img').each((i, elem) => {
      const src = $(elem).attr('src')
      if (src) {
        images.push(src)
      }
    })

    return {
      title: title.trim(),
      content: content.trim(),
      images: images.slice(0, 9),
      platform: '微博',
      type: 'travel_guide'
    }
  }

  /**
   * 通用内容解析
   * @param {string} html HTML内容
   * @param {string} url 原始链接
   * @returns {object} 解析结果
   */
  async parseGeneral(html, url) {
    const $ = cheerio.load(html)
    
    // 提取标题
    const title = $('title').text() || 
                  $('h1').first().text() || 
                  $('.title').first().text() ||
                  '网页内容'

    // 提取主要内容
    const contentSelectors = [
      'article',
      '.content',
      '.main-content',
      '.post-content',
      '.article-content',
      'main',
      'p'
    ]
    
    let content = ''
    for (const selector of contentSelectors) {
      const text = $(selector).text().trim()
      if (text && text.length > 100 && text.length > content.length) {
        content = text
      }
    }

    // 提取图片
    const images = []
    $('img').each((i, elem) => {
      const src = $(elem).attr('src') || $(elem).attr('data-src')
      if (src && !src.includes('logo') && !src.includes('avatar')) {
        images.push(src)
      }
    })

    return {
      title: title.trim(),
      content: content || $('body').text().trim(),
      images: images.slice(0, 9),
      platform: '网页',
      type: 'travel_guide'
    }
  }

  /**
   * 清理和格式化文本
   * @param {string} text 原始文本
   * @returns {string} 清理后的文本
   */
  cleanText(text) {
    return text
      .replace(/\s+/g, ' ')
      .replace(/[\r\n]+/g, '\n')
      .trim()
  }
}

module.exports = WebCrawlerService
