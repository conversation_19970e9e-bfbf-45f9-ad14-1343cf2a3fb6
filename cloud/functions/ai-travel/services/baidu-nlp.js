/**
 * 百度NLP服务
 * 处理文本解析和地址识别
 */

const fetch = require('node-fetch')

class BaiduNLPService {
  constructor(appId, apiKey, secretKey) {
    this.appId = appId
    this.apiKey = apiKey
    this.secretKey = secretKey
    this.baseURL = 'https://aip.baidubce.com'
    this.accessToken = null
    this.tokenExpireTime = null
  }

  /**
   * 获取Access Token
   * @returns {string} Access Token
   */
  async getAccessToken() {
    try {
      // 检查token是否还有效
      if (this.accessToken && this.tokenExpireTime && Date.now() < this.tokenExpireTime) {
        return this.accessToken
      }

      console.log('获取百度NLP Access Token')
      
      const response = await fetch(`${this.baseURL}/oauth/2.0/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: this.api<PERSON><PERSON>,
          client_secret: this.secretKey
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      
      if (data.error) {
        throw new Error(`获取Token失败: ${data.error_description}`)
      }

      this.accessToken = data.access_token
      // Token有效期30天，提前1天刷新
      this.tokenExpireTime = Date.now() + (data.expires_in - 86400) * 1000
      
      console.log('百度NLP Token获取成功')
      return this.accessToken
    } catch (error) {
      console.error('获取百度NLP Access Token失败:', error)
      throw error
    }
  }

  /**
   * 调用百度NLP API（带超时和重试机制）
   * @param {string} endpoint API端点
   * @param {object} data 请求数据
   * @param {number} timeout 超时时间（毫秒）
   * @param {number} retries 重试次数
   * @returns {object} API响应
   */
  async callAPI(endpoint, data, timeout = 5000, retries = 2) {
    let lastError

    for (let i = 0; i <= retries; i++) {
      try {
        const token = await this.getAccessToken()

        console.log(`百度NLP API请求 (尝试 ${i + 1}/${retries + 1}):`, { endpoint })

        const response = await Promise.race([
          fetch(`${this.baseURL}${endpoint}?access_token=${token}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'AI-Travel-MiniProgram/1.0'
            },
            body: JSON.stringify(data)
          }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('API调用超时')), timeout)
          )
        ])

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()

        // 检查API错误
        if (result.error_code) {
          throw new Error(`百度NLP错误 ${result.error_code}: ${result.error_msg}`)
        }

        return result
      } catch (error) {
        lastError = error
        console.error(`百度NLP API调用失败 (尝试 ${i + 1}):`, error)

        if (i < retries) {
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
        }
      }
    }

    throw lastError
  }

  /**
   * 地址识别
   * @param {string} text 包含地址的文本
   * @returns {object} 地址识别结果
   */
  async addressRecognition(text) {
    try {
      const result = await this.callAPI('/rpc/2.0/nlp/v1/address', {
        text: text
      })

      return {
        success: true,
        data: result,
        extractedAddresses: this.parseAddressResult(result)
      }
    } catch (error) {
      console.error('地址识别失败:', error)
      throw new Error(`地址识别失败: ${error.message}`)
    }
  }

  /**
   * 词法分析
   * @param {string} text 待分析文本
   * @returns {object} 词法分析结果
   */
  async lexicalAnalysis(text) {
    try {
      const result = await this.callAPI('/rpc/2.0/nlp/v1/lexer', {
        text: text
      })

      return {
        success: true,
        data: result,
        locations: this.extractLocationsFromLexer(result)
      }
    } catch (error) {
      console.error('词法分析失败:', error)
      throw new Error(`词法分析失败: ${error.message}`)
    }
  }

  /**
   * 短文本相似度
   * @param {string} text1 文本1
   * @param {string} text2 文本2
   * @returns {object} 相似度结果
   */
  async textSimilarity(text1, text2) {
    try {
      const result = await this.callAPI('/rpc/2.0/nlp/v1/simnet', {
        text_1: text1,
        text_2: text2
      })

      return {
        success: true,
        similarity: result.score,
        data: result
      }
    } catch (error) {
      console.error('文本相似度计算失败:', error)
      throw new Error(`文本相似度计算失败: ${error.message}`)
    }
  }

  /**
   * 提取文本中的地址信息（标准化错误处理）
   * @param {string} text 文本内容
   * @returns {object} 标准化响应格式
   */
  async extractAddresses(text) {
    try {
      // 同时使用地址识别和词法分析
      const [addressResult, lexerResult] = await Promise.allSettled([
        this.addressRecognition(text),
        this.lexicalAnalysis(text)
      ])

      const addresses = []
      const sources = {
        addressRecognition: false,
        lexicalAnalysis: false
      }

      // 从地址识别结果提取
      if (addressResult.status === 'fulfilled' && addressResult.value.success) {
        addresses.push(...addressResult.value.extractedAddresses)
        sources.addressRecognition = true
      }

      // 从词法分析结果提取地点
      if (lexerResult.status === 'fulfilled' && lexerResult.value.success) {
        addresses.push(...lexerResult.value.locations)
        sources.lexicalAnalysis = true
      }

      // 去重
      const uniqueAddresses = this.deduplicateAddresses(addresses)

      return {
        success: true,
        data: uniqueAddresses,
        message: `成功提取${uniqueAddresses.length}个地址`,
        sources
      }
    } catch (error) {
      console.error('提取地址信息失败:', error)
      return {
        success: false,
        message: `提取地址信息失败: ${error.message}`,
        data: [],
        error: error.message
      }
    }
  }

  /**
   * 解析地址识别结果
   * @param {object} result 百度地址识别API结果
   * @returns {array} 解析后的地址列表
   */
  parseAddressResult(result) {
    const addresses = []
    
    if (result.items && Array.isArray(result.items)) {
      result.items.forEach(item => {
        if (item.address) {
          addresses.push({
            text: item.address,
            type: 'address',
            confidence: item.confidence || 0.8,
            source: 'baidu_address_recognition'
          })
        }
      })
    }

    return addresses
  }

  /**
   * 从词法分析结果提取地点
   * @param {object} result 百度词法分析API结果
   * @returns {array} 地点列表
   */
  extractLocationsFromLexer(result) {
    const locations = []
    
    if (result.items && Array.isArray(result.items)) {
      result.items.forEach(item => {
        // 提取地名（LOC）和机构名（ORG）
        if (item.ne === 'LOC' || item.ne === 'ORG') {
          locations.push({
            text: item.item,
            type: item.ne === 'LOC' ? 'location' : 'organization',
            pos: item.pos,
            confidence: 0.7,
            source: 'baidu_lexical_analysis'
          })
        }
      })
    }

    return locations
  }

  /**
   * 地址去重
   * @param {array} addresses 地址列表
   * @returns {array} 去重后的地址列表
   */
  deduplicateAddresses(addresses) {
    const seen = new Set()
    const unique = []

    addresses.forEach(addr => {
      const key = addr.text.toLowerCase().trim()
      if (!seen.has(key) && key.length > 1) {
        seen.add(key)
        unique.push(addr)
      }
    })

    return unique
  }

  /**
   * 测试连接
   * @returns {boolean} 连接是否成功
   */
  async testConnection() {
    try {
      // 测试获取Token
      await this.getAccessToken()
      
      // 测试简单的词法分析
      await this.lexicalAnalysis('北京天安门')
      
      return true
    } catch (error) {
      console.error('百度NLP连接测试失败:', error)
      throw error
    }
  }

  /**
   * 获取服务状态
   * @returns {object} 服务状态
   */
  getServiceStatus() {
    return {
      configured: !!(this.appId && this.apiKey && this.secretKey),
      tokenValid: !!(this.accessToken && this.tokenExpireTime && Date.now() < this.tokenExpireTime),
      tokenExpireTime: this.tokenExpireTime ? new Date(this.tokenExpireTime).toISOString() : null,
      services: [
        '地址识别',
        '词法分析',
        '文本相似度',
        '地点提取'
      ]
    }
  }

  /**
   * 获取API使用统计（模拟）
   * @returns {object} 使用统计
   */
  getUsageStats() {
    return {
      dailyLimit: 50000,
      estimatedUsage: 0, // 实际项目中可以记录使用次数
      resetTime: '每日00:00',
      note: '免费额度每日5万次调用'
    }
  }
}

module.exports = BaiduNLPService
