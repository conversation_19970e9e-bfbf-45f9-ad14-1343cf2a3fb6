/**
 * 和风天气JWT认证工具
 * 实现Ed25519算法的JWT生成
 */

const crypto = require('crypto')

class QWeatherJWT {
  constructor(keyId, projectId, privateKey) {
    this.keyId = keyId
    this.projectId = projectId
    this.privateKey = privateKey
    
    // 验证必要参数
    if (!keyId || !projectId || !privateKey) {
      throw new Error('JWT参数不完整：需要keyId、projectId和privateKey')
    }
  }

  /**
   * 生成JWT Token
   * @returns {string} JWT Token
   */
  generateToken() {
    try {
      // 构建Header
      const header = {
        alg: 'EdDSA',
        kid: this.keyId
      }

      // 构建Payload
      const iat = Math.floor(Date.now() / 1000) - 30  // 当前时间-30秒，防止时间误差
      const exp = iat + 900  // 15分钟有效期

      const payload = {
        sub: this.projectId,
        iat: iat,
        exp: exp
      }

      // Base64URL编码Header和Payload
      const headerEncoded = this.base64urlEncode(JSON.stringify(header))
      const payloadEncoded = this.base64urlEncode(JSON.stringify(payload))
      const data = `${headerEncoded}.${payloadEncoded}`

      // 使用Ed25519算法签名
      const signature = crypto.sign(null, Buffer.from(data), {
        key: this.privateKey,
        format: 'pem'
      })

      // Base64URL编码签名
      const signatureEncoded = this.base64urlEncode(signature)
      
      // 组合最终的JWT
      const jwt = `${data}.${signatureEncoded}`
      
      console.log('JWT生成成功:', {
        keyId: this.keyId,
        projectId: this.projectId,
        iat: iat,
        exp: exp,
        jwtLength: jwt.length
      })
      
      return jwt
    } catch (error) {
      console.error('JWT生成失败:', error)
      throw new Error(`JWT生成失败: ${error.message}`)
    }
  }

  /**
   * Base64URL编码
   * @param {string|Buffer} input 输入数据
   * @returns {string} Base64URL编码结果
   */
  base64urlEncode(input) {
    let buffer
    if (typeof input === 'string') {
      buffer = Buffer.from(input, 'utf8')
    } else {
      buffer = input
    }
    
    return buffer
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
  }

  /**
   * 验证私钥格式
   * @returns {boolean} 私钥是否有效
   */
  validatePrivateKey() {
    try {
      // 尝试创建一个测试签名来验证私钥
      const testData = 'test'
      crypto.sign(null, Buffer.from(testData), {
        key: this.privateKey,
        format: 'pem'
      })
      return true
    } catch (error) {
      console.error('私钥验证失败:', error)
      return false
    }
  }

  /**
   * 获取JWT信息（不包含签名）
   * @returns {object} JWT信息
   */
  getJWTInfo() {
    const iat = Math.floor(Date.now() / 1000) - 30
    const exp = iat + 900
    
    return {
      header: {
        alg: 'EdDSA',
        kid: this.keyId
      },
      payload: {
        sub: this.projectId,
        iat: iat,
        exp: exp
      },
      issuedAt: new Date(iat * 1000).toISOString(),
      expiresAt: new Date(exp * 1000).toISOString(),
      validFor: '15 minutes'
    }
  }
}

/**
 * 创建JWT实例的工厂函数
 * @param {string} keyId 凭据ID
 * @param {string} projectId 项目ID
 * @param {string} privateKey 私钥（PEM格式）
 * @returns {QWeatherJWT} JWT实例
 */
function createJWT(keyId, projectId, privateKey) {
  return new QWeatherJWT(keyId, projectId, privateKey)
}

/**
 * 快速生成JWT Token
 * @param {string} keyId 凭据ID
 * @param {string} projectId 项目ID
 * @param {string} privateKey 私钥（PEM格式）
 * @returns {string} JWT Token
 */
function generateQuickToken(keyId, projectId, privateKey) {
  const jwt = new QWeatherJWT(keyId, projectId, privateKey)
  return jwt.generateToken()
}

/**
 * 验证JWT配置
 * @param {string} keyId 凭据ID
 * @param {string} projectId 项目ID
 * @param {string} privateKey 私钥（PEM格式）
 * @returns {object} 验证结果
 */
function validateJWTConfig(keyId, projectId, privateKey) {
  const result = {
    valid: false,
    errors: [],
    warnings: []
  }

  // 检查必要参数
  if (!keyId) result.errors.push('缺少keyId')
  if (!projectId) result.errors.push('缺少projectId')
  if (!privateKey) result.errors.push('缺少privateKey')

  if (result.errors.length > 0) {
    return result
  }

  // 检查私钥格式
  if (!privateKey.includes('-----BEGIN PRIVATE KEY-----')) {
    result.errors.push('私钥格式不正确，应为PEM格式')
  }

  // 尝试创建JWT实例
  try {
    const jwt = new QWeatherJWT(keyId, projectId, privateKey)
    if (!jwt.validatePrivateKey()) {
      result.errors.push('私钥验证失败')
    }
  } catch (error) {
    result.errors.push(`JWT配置错误: ${error.message}`)
  }

  // 检查keyId格式
  if (keyId.length < 5) {
    result.warnings.push('keyId长度可能不正确')
  }

  // 检查projectId格式
  if (projectId.length < 5) {
    result.warnings.push('projectId长度可能不正确')
  }

  result.valid = result.errors.length === 0

  return result
}

module.exports = {
  QWeatherJWT,
  createJWT,
  generateQuickToken,
  validateJWTConfig
}
