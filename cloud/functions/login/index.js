// 云函数入口文件
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV // 使用当前云环境
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('=== 登录云函数被调用 ===')
  console.log('event:', JSON.stringify(event, null, 2))
  console.log('context:', JSON.stringify(context, null, 2))



  const wxContext = cloud.getWXContext()
  console.log('微信上下文:', JSON.stringify(wxContext, null, 2))

  try {
    // 获取用户的 openid 和 unionid
    const { OPENID, UNIONID, APPID } = wxContext

    if (!OPENID) {
      console.error('无法获取用户OPENID')
      return {
        success: false,
        error: 'INVALID_OPENID',
        message: '无法获取用户身份信息'
      }
    }

    console.log('用户OPENID:', OPENID)

    // 检查用户是否已存在
    const userQuery = await db.collection('users').where({
      openid: OPENID
    }).get()

    console.log('用户查询结果:', userQuery)
    
    let userInfo = null
    
    if (userQuery.data.length === 0) {
      // 新用户，创建用户记录
      console.log('创建新用户')
      const createTime = new Date()
      const newUser = {
        openid: OPENID,
        unionid: UNIONID || null,
        appid: APPID,
        createTime,
        updateTime: createTime,
        loginCount: 1,
        lastLoginTime: createTime,
        // 用户基本信息（需要前端传入）
        nickName: event.userInfo?.nickName || '微信用户',
        avatarUrl: event.userInfo?.avatarUrl || '',
        gender: event.userInfo?.gender || 0,
        country: event.userInfo?.country || '',
        province: event.userInfo?.province || '',
        city: event.userInfo?.city || '',
        language: event.userInfo?.language || 'zh_CN'
      }

      console.log('准备创建用户数据:', newUser)

      const createResult = await db.collection('users').add({
        data: newUser
      })

      console.log('用户创建结果:', createResult)

      userInfo = {
        ...newUser,
        _id: createResult._id,
        isNewUser: true
      }
      
    } else {
      // 老用户，更新登录信息
      console.log('更新老用户信息')
      const existingUser = userQuery.data[0]
      const updateData = {
        updateTime: new Date(),
        loginCount: (existingUser.loginCount || 0) + 1,
        lastLoginTime: new Date()
      }

      // 如果前端传入了新的用户信息，则更新
      if (event.userInfo) {
        Object.assign(updateData, {
          nickName: event.userInfo.nickName || existingUser.nickName,
          avatarUrl: event.userInfo.avatarUrl || existingUser.avatarUrl,
          gender: event.userInfo.gender || existingUser.gender,
          country: event.userInfo.country || existingUser.country,
          province: event.userInfo.province || existingUser.province,
          city: event.userInfo.city || existingUser.city,
          language: event.userInfo.language || existingUser.language
        })
      }

      console.log('准备更新用户数据:', updateData)

      await db.collection('users').doc(existingUser._id).update({
        data: updateData
      })

      userInfo = {
        ...existingUser,
        ...updateData,
        isNewUser: false
      }
    }
    
    // 返回用户信息
    console.log('登录成功，返回用户信息:', userInfo)
    return {
      success: true,
      data: {
        openid: OPENID,
        unionid: UNIONID,
        userInfo: userInfo,
        timestamp: new Date().getTime()
      },
      message: '登录成功'
    }

  } catch (error) {
    console.error('登录失败，详细错误:', error)
    return {
      success: false,
      error: error.message || 'UNKNOWN_ERROR',
      errorCode: error.code || 'UNKNOWN',
      message: '登录失败，请重试'
    }
  }
}
