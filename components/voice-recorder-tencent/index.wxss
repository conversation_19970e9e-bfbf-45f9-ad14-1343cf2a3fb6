/* components/voice-recorder-tencent/index.wxss */

/* 语音录音按钮 */
.voice-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(78, 205, 196, 0.3);
  transition: all 0.3s ease;
}

.voice-btn:active {
  /* 组合transform避免冲突，添加硬件加速 */
  transform: scale(0.95) translateZ(0);
  -webkit-transform: scale(0.95) translateZ(0);
  box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.4);
}

.voice-icon {
  width: 32rpx;
  height: 32rpx;
}

.voice-text {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

/* 模态框 */
.voice-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10rpx);
}

.modal-content {
  position: relative;
  width: 600rpx;
  background: white;
  border-radius: 24rpx;
  padding: 48rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

/* 录音状态 */
.recording-status {
  text-align: center;
}

.recording-animation {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wave {
  position: absolute;
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid #4ECDC4;
  border-radius: 50%;
  animation: wave-animation 2s infinite;
}

.wave1 {
  animation-delay: 0s;
}

.wave2 {
  animation-delay: 0.5s;
}

.wave3 {
  animation-delay: 1s;
}

@keyframes wave-animation {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.recording-info {
  margin-bottom: 32rpx;
}

.status-text {
  display: block;
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.time-text {
  display: block;
  font-size: 48rpx;
  color: #4ECDC4;
  font-weight: bold;
}

.realtime-result {
  margin-top: 24rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #4ECDC4;
}

.realtime-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.recording-controls {
  display: flex;
  gap: 24rpx;
  justify-content: center;
}

.cancel-btn, .stop-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.stop-btn {
  background: #4ECDC4;
  color: white;
}

/* 识别中状态 */
.recognizing-status {
  text-align: center;
  padding: 48rpx 0;
}

.loading-animation {
  display: flex;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 32rpx;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  background: #4ECDC4;
  border-radius: 50%;
  animation: loading-animation 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-animation {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 识别结果状态 */
.result-status {
  text-align: left;
}

.voice-text-display {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.label {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 12rpx;
}

.voice-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.parsed-info {
  margin-bottom: 32rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  width: 120rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.result-controls {
  display: flex;
  gap: 24rpx;
}

.re-record-btn, .confirm-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.re-record-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #4ECDC4;
  color: white;
}

/* 错误状态 */
.error-status {
  text-align: center;
  padding: 24rpx 0;
}

.error-icon {
  font-size: 64rpx;
  margin-bottom: 24rpx;
}

.error-message {
  display: block;
  font-size: 28rpx;
  color: #ff4d4f;
  margin-bottom: 32rpx;
  line-height: 1.5;
}

.error-controls {
  display: flex;
  gap: 24rpx;
}

.retry-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  background: #4ECDC4;
  color: white;
}

/* 按钮激活效果 - 添加硬件加速 */
.cancel-btn:active,
.stop-btn:active,
.re-record-btn:active,
.confirm-btn:active,
.retry-btn:active {
  /* 组合transform避免冲突，添加硬件加速 */
  transform: scale(0.95) translateZ(0);
  -webkit-transform: scale(0.95) translateZ(0);
  transition: transform 0.15s ease;
}
