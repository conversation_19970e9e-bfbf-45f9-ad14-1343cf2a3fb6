// components/voice-recorder-tencent/index.js
// 使用腾讯云语音识别的录音组件

import tencentASR from '../../utils/tencentASR.js'

Component({
  properties: {
    // 当前记账模式（travel/daily）
    recordMode: {
      type: String,
      value: 'travel'
    }
  },

  data: {
    // UI状态
    showModal: false,
    recording: false,
    status: 'idle', // idle/recording/recognizing/result/error
    
    // 录音相关
    recordTime: 0,
    showCancelZone: false,
    
    // 识别结果
    voiceText: '',
    realtimeText: '', // 实时识别结果
    parsedData: {},
    errorMessage: '',
    
    // 计时器
    recordTimer: null
  },

  lifetimes: {
    attached() {
      this.checkEnvironment()
      this.initTencentASR()
    },

    detached() {
      this.cleanup()
    }
  },

  methods: {
    // 检查运行环境
    checkEnvironment() {
      let systemInfo
      try {
        systemInfo = {
          ...wx.getWindowInfo(),
          ...wx.getDeviceInfo(),
          ...wx.getAppBaseInfo()
        }
      } catch (error) {
        systemInfo = wx.getSystemInfoSync()
      }

      // 检查是否支持录音
      if (!wx.getRecorderManager) {
        this.setData({
          errorMessage: '当前环境不支持录音功能'
        })
        return false
      }

      return true
    },

    // 初始化腾讯云语音识别
    initTencentASR() {
      // 配置腾讯云参数
      tencentASR.init({
        appid: '1370752426',
        secretid: 'AKIDxjITdaN1UkqnZ8UksnV4TlspbDh4IMDi',
        secretkey: 'MabO1Vnv6ECyeJ2rZ6QXffT6YqzxvmTz',
        engine_model_type: '16k_zh',
        voice_format: 8,
        duration: 60000,
        needvad: 1,
        filter_dirty: 1,
        filter_modal: 1,
        convert_num_mode: 1
      })
    },

    // 切换录音状态
    toggleRecord() {
      if (this.data.recording) {
        this.stopRecord()
      } else {
        this.startRecord()
      }
    },

    // 开始录音
    startRecord() {
      // 检查录音权限
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.record'] === false) {
            this.requestRecordPermission()
            return
          }

          if (res.authSetting['scope.record'] === undefined) {
            wx.authorize({
              scope: 'scope.record',
              success: () => {
                this.startRecording()
              },
              fail: () => {
                this.requestRecordPermission()
              }
            })
            return
          }

          this.startRecording()
        },
        fail: (err) => {
          this.showError('获取权限设置失败，请重试')
        }
      })
    },

    // 实际开始录音的方法
    startRecording() {
      this.setData({
        showModal: true,
        status: 'recording',
        recordTime: 0,
        realtimeText: '',
        voiceText: '',
        errorMessage: ''
      })

      // 开始计时
      this.startTimer()

      // 检查网络状态
      wx.getNetworkType({
        success: (res) => {
          if (res.networkType === 'none') {
            this.showError('网络连接失败，语音识别需要网络支持')
            return
          }
        },
        fail: () => {
          // 静默处理网络状态获取失败
        }
      })

      // 检查腾讯云ASR是否可用
      if (typeof tencentASR === 'undefined') {
        this.showError('语音识别服务未加载，请刷新页面重试')
        return
      }

      // 开始腾讯云语音识别
      const success = tencentASR.start({
        onStart: (result) => {
          this.setData({
            recording: true,
            status: 'recording'
          })
        },

        onResult: (result) => {
          if (result.isFinal) {
            // 最终结果
            this.setData({
              voiceText: result.text
            })
          } else {
            // 实时结果
            this.setData({
              realtimeText: result.text
            })
          }
        },

        onComplete: (result) => {
          this.handleRecognitionComplete()
        },

        onError: (error) => {
          this.showError(error.message || '语音识别失败')
        }
      })

      if (!success) {
        this.showError('启动语音识别失败，请检查网络连接')
      }
    },

    // 停止录音
    stopRecord() {

      if (!this.data.recording) {
        console.log('当前未在录音，忽略停止操作')
        return
      }

      try {
        console.log('调用腾讯云ASR停止')
        tencentASR.stop()
        this.stopTimer()

        this.setData({
          recording: false,
          status: 'recognizing'
        })

        console.log('录音停止成功，等待识别结果')

        // 设置超时处理，防止识别结果一直不返回
        setTimeout(() => {
          if (this.data.status === 'recognizing') {
            console.log('识别超时，显示错误')
            this.showError('语音识别超时，请重试')
          }
        }, 10000) // 10秒超时

      } catch (error) {
        console.error('停止录音失败:', error)
        this.showError('停止录音失败，请重试')
      }
    },

    // 取消录音
    cancelRecord() {
      tencentASR.stop()
      this.stopTimer()
      
      this.setData({
        showModal: false,
        recording: false,
        status: 'idle',
        recordTime: 0,
        realtimeText: '',
        voiceText: '',
        errorMessage: ''
      })
    },

    // 处理识别完成
    handleRecognitionComplete() {
      const finalText = this.data.voiceText || this.data.realtimeText
      
      if (finalText) {
        const parsedData = this.parseVoiceText(finalText)
        
        this.setData({
          status: 'result',
          voiceText: finalText,
          parsedData: parsedData
        })
      } else {
        this.showError('未识别到语音内容，请重试')
      }
    },

    // 请求录音权限
    requestRecordPermission() {
      console.log('请求录音权限')
      wx.showModal({
        title: '需要录音权限',
        content: '语音记账需要使用录音功能，请在设置中允许录音权限后重试',
        confirmText: '去设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            console.log('用户选择去设置')
            wx.openSetting({
              success: (settingRes) => {
                console.log('设置页面返回:', settingRes.authSetting)
                if (settingRes.authSetting['scope.record']) {
                  console.log('用户已授权录音权限')
                  wx.showToast({
                    title: '权限已开启，请重新尝试',
                    icon: 'success'
                  })
                } else {
                  console.log('用户仍未授权录音权限')
                  wx.showToast({
                    title: '需要录音权限才能使用语音记账',
                    icon: 'none',
                    duration: 3000
                  })
                }
              },
              fail: (err) => {
                console.error('打开设置页面失败:', err)
              }
            })
          } else {
            console.log('用户取消权限设置')
          }
        }
      })
    },

    // 开始计时
    startTimer() {
      this.data.recordTimer = setInterval(() => {
        this.setData({
          recordTime: this.data.recordTime + 1
        })
      }, 1000)
    },

    // 停止计时
    stopTimer() {
      if (this.data.recordTimer) {
        clearInterval(this.data.recordTimer)
        this.setData({ recordTimer: null })
      }
    },

    // 解析语音文字（与原组件相同的逻辑）
    parseVoiceText(text) {
      const result = {
        amount: null,
        category: null,
        categoryName: '',
        description: ''
      }

      // 提取金额
      const amountPatterns = [
        /(\d+(?:\.\d+)?)\s*(?:元|块|块钱|元钱)/g,
        /花了\s*(\d+(?:\.\d+)?)/g,
        /(\d+(?:\.\d+)?)\s*(?:块|元)/g
      ]

      for (let pattern of amountPatterns) {
        const match = pattern.exec(text)
        if (match) {
          result.amount = parseFloat(match[1])
          break
        }
      }

      // 分类识别逻辑（与原组件相同）
      const travelCategories = {
        transport: { name: '交通', keywords: ['打车', '出租车', '地铁', '公交', '飞机', '火车', '高铁'] },
        accommodation: { name: '住宿', keywords: ['酒店', '宾馆', '民宿', '住宿'] },
        food: { name: '餐饮', keywords: ['吃饭', '餐厅', '小吃', '早餐', '午餐', '晚餐', '喝水', '咖啡'] },
        attraction: { name: '景点', keywords: ['门票', '景点', '博物馆', '公园'] },
        shopping: { name: '购物', keywords: ['买', '购物', '纪念品', '特产'] },
        other: { name: '其他', keywords: ['其他', '杂费'] }
      }

      const dailyCategories = {
        food: { name: '餐饮', keywords: ['吃饭', '餐厅', '小吃', '早餐', '午餐', '晚餐', '外卖'] },
        transport: { name: '交通', keywords: ['打车', '出租车', '地铁', '公交', '加油'] },
        shopping: { name: '购物', keywords: ['买', '购物', '超市', '商场'] },
        entertainment: { name: '娱乐', keywords: ['电影', '游戏', 'KTV', '娱乐'] },
        medical: { name: '医疗', keywords: ['医院', '药店', '看病', '体检'] },
        other: { name: '其他', keywords: ['其他', '杂费'] }
      }

      const categoryKeywords = this.data.recordMode === 'travel' ? travelCategories : dailyCategories

      for (let [key, category] of Object.entries(categoryKeywords)) {
        for (let keyword of category.keywords) {
          if (text.includes(keyword)) {
            result.category = key
            result.categoryName = category.name
            break
          }
        }
        if (result.category) break
      }

      // 生成描述
      let description = text
        .replace(/(\d+(?:\.\d+)?)\s*(?:元|块|块钱|元钱)/g, '')
        .replace(/花了\s*/g, '')
        .replace(/我|刚才|刚刚/g, '')
        .trim()

      result.description = description || '语音记账'

      return result
    },

    // 显示错误
    showError(message) {
      this.setData({
        status: 'error',
        errorMessage: message
      })
    },

    // 重新录音
    reRecord() {
      this.setData({
        status: 'idle',
        voiceText: '',
        realtimeText: '',
        parsedData: {},
        errorMessage: '',
        recordTime: 0
      })
      
      setTimeout(() => {
        this.startRecord()
      }, 300)
    },

    // 确认使用识别结果
    confirmResult() {
      const { parsedData } = this.data
      
      // 触发父组件事件
      this.triggerEvent('voiceResult', {
        amount: parsedData.amount,
        category: parsedData.category,
        description: parsedData.description,
        voiceText: this.data.voiceText
      })
      
      this.closeModal()
    },

    // 关闭模态框
    closeModal() {
      this.setData({
        showModal: false,
        status: 'idle',
        voiceText: '',
        realtimeText: '',
        parsedData: {},
        errorMessage: '',
        recordTime: 0
      })
    },

    // 清理资源
    cleanup() {
      try {
        this.stopTimer()

        if (tencentASR && tencentASR.getStatus && tencentASR.getStatus().isRecognizing) {
          tencentASR.stop()
        }

        // 重置组件状态
        this.setData({
          showModal: false,
          recording: false,
          status: 'idle',
          recordTime: 0,
          realtimeText: '',
          voiceText: '',
          errorMessage: ''
        })

      } catch (error) {
        // 静默处理清理错误
      }
    }
  }
})
