<!--components/voice-recorder-tencent/index.wxml-->

<!-- 语音录音按钮 -->
<view class="voice-btn" bindtap="toggleRecord">
  <image class="voice-icon" src="/images/microphone.svg" mode="aspectFit"></image>
  <text class="voice-text">语音记账</text>
</view>

<!-- 录音模态框 -->
<view class="voice-modal" wx:if="{{showModal}}">
  <view class="modal-mask" bindtap="cancelRecord"></view>
  
  <view class="modal-content">
    <!-- 录音状态 -->
    <view class="recording-status" wx:if="{{status === 'recording'}}">
      <view class="recording-animation">
        <view class="wave wave1"></view>
        <view class="wave wave2"></view>
        <view class="wave wave3"></view>
      </view>
      
      <view class="recording-info">
        <text class="status-text">正在录音...</text>
        <text class="time-text">{{recordTime}}s</text>
        
        <!-- 实时识别结果 -->
        <view class="realtime-result" wx:if="{{realtimeText}}">
          <text class="realtime-text">{{realtimeText}}</text>
        </view>
      </view>
      
      <view class="recording-controls">
        <button class="cancel-btn" bindtap="cancelRecord">取消</button>
        <button class="stop-btn" bindtap="stopRecord">完成</button>
      </view>
    </view>

    <!-- 识别中状态 -->
    <view class="recognizing-status" wx:if="{{status === 'recognizing'}}">
      <view class="loading-animation">
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
      </view>
      <text class="status-text">正在识别语音...</text>
    </view>

    <!-- 识别结果 -->
    <view class="result-status" wx:if="{{status === 'result'}}">
      <view class="result-content">
        <view class="voice-text-display">
          <text class="label">识别结果：</text>
          <text class="voice-text">{{voiceText}}</text>
        </view>
        
        <view class="parsed-info">
          <view class="info-item" wx:if="{{parsedData.amount}}">
            <text class="info-label">金额：</text>
            <text class="info-value">¥{{parsedData.amount}}</text>
          </view>
          
          <view class="info-item" wx:if="{{parsedData.categoryName}}">
            <text class="info-label">分类：</text>
            <text class="info-value">{{parsedData.categoryName}}</text>
          </view>
          
          <view class="info-item" wx:if="{{parsedData.description}}">
            <text class="info-label">描述：</text>
            <text class="info-value">{{parsedData.description}}</text>
          </view>
        </view>
      </view>
      
      <view class="result-controls">
        <button class="re-record-btn" bindtap="reRecord">重新录音</button>
        <button class="confirm-btn" bindtap="confirmResult">确认使用</button>
      </view>
    </view>

    <!-- 错误状态 -->
    <view class="error-status" wx:if="{{status === 'error'}}">
      <view class="error-icon">⚠️</view>
      <text class="error-message">{{errorMessage}}</text>
      
      <view class="error-controls">
        <button class="cancel-btn" bindtap="closeModal">关闭</button>
        <button class="retry-btn" bindtap="reRecord">重试</button>
      </view>
    </view>
  </view>
</view>
