/* components/icon/index.wxss */

.icon-container {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.icon-image {
  width: 100%;
  height: 100%;
  display: block;
}

/* 主题色彩类 */
.icon-primary {
  color: #FF6B6B;
}

.icon-secondary {
  color: #4ECDC4;
}

.icon-accent {
  color: #45B7D1;
}

/* 功能色彩类 */
.icon-success {
  color: #52c41a;
}

.icon-warning {
  color: #faad14;
}

.icon-error {
  color: #ff4d4f;
}

.icon-info {
  color: #1890ff;
}

/* 中性色彩类 */
.icon-default {
  color: rgba(0, 0, 0, 0.65);
}

.icon-light {
  color: rgba(255, 255, 255, 0.85);
}

.icon-disabled {
  color: rgba(0, 0, 0, 0.25);
}

/* 标准尺寸类 */
.icon-xs {
  width: 26rpx;
  height: 26rpx;
}

.icon-sm {
  width: 30rpx;
  height: 30rpx;
}

.icon-md {
  width: 34rpx;
  height: 34rpx;
}

.icon-lg {
  width: 38rpx;
  height: 38rpx;
}

.icon-xl {
  width: 46rpx;
  height: 46rpx;
}

.icon-2xl {
  width: 60rpx;
  height: 60rpx;
}

/* 兼容性尺寸类（旧版本） - 避免重复定义 */
/* 这些类已被新的标准尺寸类替代，保留仅为向后兼容 */
.icon-size-12 { width: 26rpx; height: 26rpx; }
.icon-size-16 { width: 30rpx; height: 30rpx; }
.icon-size-20 { width: 34rpx; height: 34rpx; }
.icon-size-24 { width: 38rpx; height: 38rpx; }
.icon-size-32 { width: 46rpx; height: 46rpx; }
.icon-size-48 { width: 60rpx; height: 60rpx; }

/* 动画效果 */
.icon-spin {
  animation: iconSpin 1s linear infinite;
}

.icon-pulse {
  animation: iconPulse 2s ease-in-out infinite;
}

.icon-bounce {
  animation: iconBounce 1s ease-in-out infinite;
}

@keyframes iconSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes iconPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes iconBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}
