// components/chart/index.js
// ECharts图表组件 - 适配爱巢小记设计风格

import * as echarts from '../../miniprogram_npm/echarts/index'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 图表类型
    type: {
      type: String,
      value: 'line' // line, bar, pie, area
    },
    // 图表数据
    data: {
      type: Object,
      value: {}
    },
    // 图表配置
    options: {
      type: Object,
      value: {}
    },
    // 图表主题
    theme: {
      type: String,
      value: 'light' // light, dark
    },
    // 图表高度
    height: {
      type: Number,
      value: 300
    },
    // 是否显示加载动画
    loading: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    ec: {
      onInit: null
    },
    chartInstance: null
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initChart()
    },
    
    detached() {
      // 清理图表实例
      if (this.data.chartInstance) {
        try {
          // 移除所有事件监听器
          this.data.chartInstance.off()
          // 销毁图表实例
          this.data.chartInstance.dispose()
          // 清空引用
          this.setData({ chartInstance: null })
          console.log('图表组件资源清理完成')
        } catch (error) {
          console.error('图表组件清理失败:', error)
        }
      }

      // 清理观察者
      if (this.observers) {
        Object.keys(this.observers).forEach(key => {
          if (this.observers[key] && typeof this.observers[key].disconnect === 'function') {
            this.observers[key].disconnect()
          }
        })
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化图表
     */
    initChart() {
      this.setData({
        ec: {
          onInit: this.initECharts.bind(this)
        }
      })
    },

    /**
     * ECharts初始化回调
     */
    initECharts(canvas, width, height, dpr) {
      const chart = echarts.init(canvas, this.getTheme(), {
        width: width,
        height: height,
        devicePixelRatio: dpr
      })
      
      this.setData({ chartInstance: chart })
      this.updateChart()
      
      return chart
    },

    /**
     * 获取图表主题
     */
    getTheme() {
      return {
        color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFB6C1', '#faad14', '#52c41a'],
        backgroundColor: 'transparent',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.9)',
          fontSize: 12
        },
        title: {
          textStyle: {
            color: 'rgba(255, 255, 255, 0.95)',
            fontSize: 16,
            fontWeight: 600
          }
        },
        legend: {
          textStyle: {
            color: 'rgba(255, 255, 255, 0.8)',
            fontSize: 12
          }
        },
        grid: {
          borderColor: 'rgba(255, 255, 255, 0.1)'
        },
        categoryAxis: {
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.2)'
            }
          },
          axisTick: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.2)'
            }
          },
          axisLabel: {
            color: 'rgba(255, 255, 255, 0.7)',
            fontSize: 11
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        valueAxis: {
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.2)'
            }
          },
          axisTick: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.2)'
            }
          },
          axisLabel: {
            color: 'rgba(255, 255, 255, 0.7)',
            fontSize: 11
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        }
      }
    },

    /**
     * 更新图表
     */
    updateChart() {
      if (!this.data.chartInstance) return
      
      const option = this.generateOption()
      this.data.chartInstance.setOption(option, true)
      
      if (this.properties.loading) {
        this.data.chartInstance.showLoading({
          text: '加载中...',
          color: '#4ECDC4',
          textColor: 'rgba(255, 255, 255, 0.8)',
          maskColor: 'rgba(0, 0, 0, 0.3)'
        })
      } else {
        this.data.chartInstance.hideLoading()
      }
    },

    /**
     * 生成图表配置
     */
    generateOption() {
      const { type, data } = this.properties
      
      switch (type) {
        case 'pie':
          return this.generatePieOption(data)
        case 'bar':
          return this.generateBarOption(data)
        case 'line':
          return this.generateLineOption(data)
        case 'area':
          return this.generateAreaOption(data)
        default:
          return {}
      }
    },

    /**
     * 生成饼图配置
     */
    generatePieOption(data) {
      return {
        title: {
          text: data.title || '',
          left: 'center',
          top: 20
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: ¥{c} ({d}%)',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'rgba(255, 255, 255, 0.2)',
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          orient: 'horizontal',
          bottom: 10,
          itemWidth: 12,
          itemHeight: 12
        },
        series: [{
          name: data.name || '支出分类',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 4,
            borderColor: 'rgba(255, 255, 255, 0.1)',
            borderWidth: 1
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 14,
              fontWeight: 'bold'
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: false
          },
          data: data.series || []
        }]
      }
    },

    /**
     * 生成柱状图配置
     */
    generateBarOption(data) {
      return {
        title: {
          text: data.title || '',
          left: 'center',
          top: 20
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'rgba(255, 255, 255, 0.2)',
          textStyle: {
            color: '#fff'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.categories || []
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '¥{value}'
          }
        },
        series: [{
          name: data.name || '金额',
          type: 'bar',
          data: data.series || [],
          itemStyle: {
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
    },

    /**
     * 生成折线图配置
     */
    generateLineOption(data) {
      return {
        title: {
          text: data.title || '',
          left: 'center',
          top: 20
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'rgba(255, 255, 255, 0.2)',
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: data.categories || []
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '¥{value}'
          }
        },
        series: data.series || []
      }
    },

    /**
     * 生成面积图配置
     */
    generateAreaOption(data) {
      const option = this.generateLineOption(data)
      if (option.series && option.series.length > 0) {
        option.series.forEach(series => {
          series.areaStyle = {
            opacity: 0.3
          }
        })
      }
      return option
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'data, type, options': function() {
      if (this.data.chartInstance) {
        this.updateChart()
      }
    },
    
    'loading': function(loading) {
      if (this.data.chartInstance) {
        if (loading) {
          this.data.chartInstance.showLoading()
        } else {
          this.data.chartInstance.hideLoading()
        }
      }
    }
  }
})
