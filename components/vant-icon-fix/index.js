Component({
  externalClasses: ['custom-class'],
  
  properties: {
    name: {
      type: String,
      value: ''
    },
    size: {
      type: String,
      value: '14px'
    },
    color: {
      type: String,
      value: ''
    },
    customStyle: {
      type: String,
      value: ''
    }
  },

  data: {
    iconMap: {
      // 箭头类
      'arrow-left': '←',
      'arrow-right': '→',
      'arrow-up': '↑',
      'arrow-down': '↓',
      
      // 状态类
      'success': '✓',
      'cross': '✕',
      'plus': '+',
      'minus': '−',
      'question': '?',
      'info': 'ⓘ',
      'warning': '⚠',
      'clear': '⊗',
      
      // 功能类
      'search': '🔍',
      'star': '★',
      'star-o': '☆',
      'heart': '♥',
      'heart-o': '♡',
      'location': '📍',
      'phone': '📞',
      'setting': '⚙',
      'home': '🏠',
      'user': '👤',
      'contact': '👤',
      'friends': '👥',
      'chat': '💬',
      'wechat': '💬',
      'camera': '📷',
      'photo': '🖼',
      'delete': '🗑',
      'edit': '✏',
      'share': '📤',
      'download': '📥',
      'upload': '📤',
      'calendar': '📅',
      'clock': '🕐',
      'eye': '👁',
      'lock': '🔒',
      'unlock': '🔓',
      'fire': '🔥',
      'gift': '🎁',
      'like': '👍',
      'comment': '💭',
      'more': '⋯',
      'more-o': '⋮',
      
      // 业务类
      'balance-pay': '💰',
      'chart-trending-o': '📈',
      'bar-chart-o': '📊'
    }
  },

  methods: {
    onClick(event) {
      this.triggerEvent('click', event.detail);
    }
  }
});
