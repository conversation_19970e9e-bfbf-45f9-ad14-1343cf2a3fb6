name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  lint:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: |
        # 检查文件格式
        echo "检查文件格式..."
        
        # 检查是否有敏感信息
        echo "检查敏感信息..."
        if grep -r "password\|secret\|key" . --exclude-dir=node_modules --exclude-dir=.git; then
          echo "警告: 发现可能的敏感信息"
        fi
        
        # 检查文件大小
        echo "检查文件大小..."
        find . -name "*.js" -o -name "*.json" -o -name "*.wxss" -o -name "*.wxml" | xargs wc -l
        
        echo "代码检查完成"

  security:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Security scan
      run: |
        echo "安全扫描..."
        # 检查是否有硬编码的密钥
        if grep -r "sk-" . --exclude-dir=node_modules --exclude-dir=.git; then
          echo "警告: 发现可能的API密钥"
          exit 1
        fi
        
        # 检查是否有明文密码
        if grep -r "password.*=" . --exclude-dir=node_modules --exclude-dir=.git; then
          echo "警告: 发现可能的明文密码"
          exit 1
        fi
        
        echo "安全扫描完成" 