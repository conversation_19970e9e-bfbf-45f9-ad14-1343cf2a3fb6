<!--subpackages/discovery/index.wxml-->
<view class="discovery-container">
  <!-- 渐变背景 -->
  <view class="gradient-background"></view>

  <!-- 页面内容 -->
  <scroll-view class="content-scroll" scroll-y="true" enhanced="true" show-scrollbar="false">
    
    <!-- 搜索区域 -->
    <view class="search-section">
      <view class="search-card">
        <view class="search-input-wrapper">
          <custom-icon name="search" size="24" color="rgba(255,255,255,0.8)" />
          <input 
            class="search-input" 
            placeholder="搜索目的地、攻略..." 
            value="{{searchKeyword}}"
            bindinput="onSearchInput"
            bindconfirm="onSearchConfirm"
          />
        </view>
      </view>
    </view>

    <!-- 分类标签 -->
    <view class="category-section">
      <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
        <view class="category-list">
          <view 
            wx:for="{{categories}}" 
            wx:key="id" 
            class="category-item {{selectedCategory === item.id ? 'active' : ''}}"
            bindtap="selectCategory"
            data-category="{{item.id}}"
          >
            <custom-icon name="{{item.icon}}" size="22" color="{{selectedCategory === item.id ? '#fff' : 'rgba(255,255,255,0.8)'}}" />
            <text class="category-name">{{item.name}}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 开发中提示 -->
    <view class="development-notice">
      <view class="notice-card">
        <view class="notice-icon">
          <custom-icon name="info" size="48" color="#4ECDC4" />
        </view>
        <view class="notice-content">
          <text class="notice-title">发现页面开发中</text>
          <text class="notice-desc">热门目的地、旅行攻略等精彩内容即将上线</text>
        </view>
      </view>
    </view>

    <!-- 热门目的地预览 -->
    <view class="preview-section">
      <view class="section-header">
        <text class="section-title">热门目的地</text>
        <text class="section-subtitle">即将为您推荐最受欢迎的旅行目的地</text>
      </view>
      
      <view class="preview-grid">
        <view class="preview-item">
          <view class="preview-placeholder">
            <custom-icon name="map-pin" size="32" color="rgba(255,255,255,0.6)" />
          </view>
          <text class="preview-text">目的地推荐</text>
        </view>
        
        <view class="preview-item">
          <view class="preview-placeholder">
            <custom-icon name="bookmark" size="32" color="rgba(255,255,255,0.6)" />
          </view>
          <text class="preview-text">旅行攻略</text>
        </view>
        
        <view class="preview-item">
          <view class="preview-placeholder">
            <custom-icon name="heart" size="32" color="rgba(255,255,255,0.6)" />
          </view>
          <text class="preview-text">精选内容</text>
        </view>
        
        <view class="preview-item">
          <view class="preview-placeholder">
            <custom-icon name="star" size="32" color="rgba(255,255,255,0.6)" />
          </view>
          <text class="preview-text">用户分享</text>
        </view>
      </view>
    </view>

    <!-- 功能预告 -->
    <view class="features-section">
      <view class="section-header">
        <text class="section-title">即将推出</text>
      </view>
      
      <view class="features-list">
        <view class="feature-item">
          <view class="feature-icon">
            <custom-icon name="trending-up" size="24" color="#FF6B6B" />
          </view>
          <view class="feature-content">
            <text class="feature-title">热门目的地推荐</text>
            <text class="feature-desc">基于用户喜好的智能推荐</text>
          </view>
        </view>
        
        <view class="feature-item">
          <view class="feature-icon">
            <custom-icon name="edit" size="24" color="#4ECDC4" />
          </view>
          <view class="feature-content">
            <text class="feature-title">旅行攻略分享</text>
            <text class="feature-desc">用户原创攻略与经验分享</text>
          </view>
        </view>
        
        <view class="feature-item">
          <view class="feature-icon">
            <custom-icon name="friends" size="24" color="#45B7D1" />
          </view>
          <view class="feature-content">
            <text class="feature-title">社交互动</text>
            <text class="feature-desc">与其他旅行者交流互动</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部安全距离 -->
    <view class="safe-bottom"></view>
  </scroll-view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

</view>
