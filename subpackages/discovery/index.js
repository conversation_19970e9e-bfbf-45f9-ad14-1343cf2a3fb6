// subpackages/discovery/index.js
// 发现页面 - 热门目的地、旅行攻略、精选内容

Page({
  data: {
    // 页面状态
    loading: false,
    
    // 搜索关键词
    searchKeyword: '',
    
    // 分类标签
    categories: [
      { id: 'hot', name: '热门推荐', icon: 'trending-up' },
      { id: 'domestic', name: '国内游', icon: 'map' },
      { id: 'international', name: '出境游', icon: 'airplane' },
      { id: 'guide', name: '旅行攻略', icon: 'bookmark' }
    ],
    selectedCategory: 'hot',
    
    // 热门目的地
    hotDestinations: [],
    
    // 旅行攻略
    travelGuides: [],
    
    // 精选内容
    featuredContent: []
  },

  onLoad(options) {
    this.initPage()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadData()
  },

  onPullDownRefresh() {
    this.loadData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 初始化页面
  initPage() {
    this.loadData()
  },

  // 加载数据
  loadData() {
    return new Promise((resolve) => {
      this.setData({ loading: true })
      
      // 模拟数据加载
      setTimeout(() => {
        this.setData({
          loading: false,
          hotDestinations: this.getMockDestinations(),
          travelGuides: this.getMockGuides(),
          featuredContent: this.getMockContent()
        })
        resolve()
      }, 1000)
    })
  },

  // 模拟热门目的地数据
  getMockDestinations() {
    return [
      {
        id: 1,
        name: '北京',
        image: '/images/placeholder.jpg',
        description: '历史文化名城',
        rating: 4.8,
        visitCount: 15680
      },
      {
        id: 2,
        name: '上海',
        image: '/images/placeholder.jpg',
        description: '国际化大都市',
        rating: 4.7,
        visitCount: 18920
      }
    ]
  },

  // 模拟旅行攻略数据
  getMockGuides() {
    return [
      {
        id: 1,
        title: '北京三日游完整攻略',
        author: '旅行达人',
        cover: '/images/placeholder.jpg',
        readCount: 1234,
        likeCount: 89
      }
    ]
  },

  // 模拟精选内容数据
  getMockContent() {
    return [
      {
        id: 1,
        type: 'article',
        title: '春季赏花最佳目的地推荐',
        cover: '/images/placeholder.jpg',
        summary: '春暖花开的季节，这些地方最适合赏花踏青...',
        publishTime: '2024-03-15'
      }
    ]
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  // 搜索确认
  onSearchConfirm() {
    const keyword = this.data.searchKeyword.trim()
    if (keyword) {
      wx.showToast({
        title: '搜索功能开发中',
        icon: 'none'
      })
    }
  },

  // 选择分类
  selectCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      selectedCategory: category
    })
    
    wx.showToast({
      title: '分类功能开发中',
      icon: 'none'
    })
  },

  // 查看目的地详情
  viewDestination(e) {
    const destination = e.currentTarget.dataset.destination
    wx.showToast({
      title: '目的地详情开发中',
      icon: 'none'
    })
  },

  // 查看攻略详情
  viewGuide(e) {
    const guide = e.currentTarget.dataset.guide
    wx.showToast({
      title: '攻略详情开发中',
      icon: 'none'
    })
  },

  // 查看内容详情
  viewContent(e) {
    const content = e.currentTarget.dataset.content
    wx.showToast({
      title: '内容详情开发中',
      icon: 'none'
    })
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '爱巢小记 - 发现精彩旅程',
      path: '/subpackages/discovery/index'
    }
  }
})
