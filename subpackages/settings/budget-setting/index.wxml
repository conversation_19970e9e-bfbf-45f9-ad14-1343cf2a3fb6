<!--subpackages/settings/budget-setting/index.wxml-->
<view class="budget-container">
  <!-- 预算设置表单 -->
  <view class="budget-form">
    <!-- 月度总预算 -->
    <view class="form-section">
      <view class="section-header">
        <custom-icon name="wallet" size="24" color="#FF6B6B" />
        <text class="section-title">月度总预算</text>
      </view>
      <view class="input-group">
        <text class="currency">¥</text>
        <input 
          class="budget-input" 
          type="digit" 
          placeholder="请输入月度总预算"
          value="{{budget.monthly}}"
          bindinput="onMonthlyBudgetChange"
          focus="{{monthlyFocus}}"
        />
      </view>
      <text class="input-desc">设置您每月的总支出预算</text>
    </view>

    <!-- 日常支出预算 -->
    <view class="form-section">
      <view class="section-header">
        <custom-icon name="coffee" size="24" color="#4ECDC4" />
        <text class="section-title">日常支出预算</text>
      </view>
      <view class="input-group">
        <text class="currency">¥</text>
        <input 
          class="budget-input" 
          type="digit" 
          placeholder="请输入日常支出预算"
          value="{{budget.daily}}"
          bindinput="onDailyBudgetChange"
        />
      </view>
      <text class="input-desc">日常生活开销预算</text>
    </view>

    <!-- 旅行支出预算 -->
    <view class="form-section">
      <view class="section-header">
        <custom-icon name="airplane" size="24" color="#9B59B6" />
        <text class="section-title">旅行支出预算</text>
      </view>
      <view class="input-group">
        <text class="currency">¥</text>
        <input 
          class="budget-input" 
          type="digit" 
          placeholder="请输入旅行支出预算"
          value="{{budget.travel}}"
          bindinput="onTravelBudgetChange"
        />
      </view>
      <text class="input-desc">旅行相关开销预算</text>
    </view>

    <!-- 预算分配提示 -->
    <view class="budget-tips" wx:if="{{showTips}}">
      <view class="tips-header">
        <custom-icon name="star" size="16" color="#F39C12" />
        <text class="tips-title">预算分配建议</text>
      </view>
      <view class="tips-content">
        <text class="tips-text">• 日常预算 + 旅行预算 = 月度总预算</text>
        <text class="tips-text">• 建议日常预算占总预算的60-70%</text>
        <text class="tips-text">• 旅行预算可根据出行频率调整</text>
      </view>
    </view>

    <!-- 预算验证提示 -->
    <view class="error-tips" wx:if="{{errorMessage}}">
      <custom-icon name="close" size="16" color="#ff4d4f" />
      <text class="error-text">{{errorMessage}}</text>
    </view>
  </view>

  <!-- 保存按钮 -->
  <view class="save-section">
    <button 
      class="save-btn {{loading ? 'loading' : ''}}" 
      bindtap="saveBudget"
      disabled="{{loading}}"
    >
      <view wx:if="{{loading}}" class="loading-spinner"></view>
      <text>{{loading ? '保存中...' : '保存设置'}}</text>
    </button>
  </view>
</view>
