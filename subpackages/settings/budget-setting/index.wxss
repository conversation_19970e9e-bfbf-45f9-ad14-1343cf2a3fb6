/**subpackages/settings/budget-setting/index.wxss**/

.budget-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  position: relative;
}

/* 预算表单 */
.budget-form {
  padding: 32rpx;
}

.form-section {
  background: rgba(255, 255, 255, 0.12);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  margin-left: 12rpx;
}

.input-group {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 0 24rpx;
  margin-bottom: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.currency {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-right: 8rpx;
}

.budget-input {
  flex: 1;
  height: 88rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}

.budget-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-weight: 400;
}

.input-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 8rpx;
}

/* 预算提示 */
.budget-tips {
  background: rgba(243, 156, 18, 0.15);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid rgba(243, 156, 18, 0.3);
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.tips-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #F39C12;
  margin-left: 8rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tips-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* 错误提示 */
.error-tips {
  display: flex;
  align-items: center;
  background: rgba(255, 77, 79, 0.15);
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid rgba(255, 77, 79, 0.3);
}

.error-text {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-left: 8rpx;
}

/* 保存按钮 */
.save-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.3);
  transition: all 0.3s ease;
  border: none;
}

.save-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.4);
}

.save-btn.loading {
  background: rgba(255, 255, 255, 0.3);
  box-shadow: none;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
