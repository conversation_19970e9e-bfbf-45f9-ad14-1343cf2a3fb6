<!--subpackages/settings/user-agreement/index.wxml-->
<view class="agreement-container">
  <!-- 用户协议内容 -->
  <view class="agreement-card">
    <view class="section-title">
      <custom-icon name="info" size="24" color="#4ECDC4"></custom-icon>
      <text class="title-text">爱巢小记用户协议</text>
    </view>

    <view class="agreement-content">
      <!-- 协议生效 -->
      <view class="agreement-section">
        <text class="section-title-text">1. 协议生效</text>
        <text class="section-content">本协议自您使用爱巢小记服务之日起生效。使用本服务即表示您同意遵守本协议的所有条款。</text>
      </view>

      <!-- 服务说明 -->
      <view class="agreement-section">
        <text class="section-title-text">2. 服务说明</text>
        <text class="section-content">爱巢小记是一款集记账管理和旅行规划于一体的微信小程序，为用户提供：</text>
        <text class="section-content">• 智能记账功能，支持语音识别记账</text>
        <text class="section-content">• 旅行规划和预算管理</text>
        <text class="section-content">• 数据分析和统计报表</text>
        <text class="section-content">• 云端数据存储和同步</text>
      </view>

      <!-- 用户权利 -->
      <view class="agreement-section">
        <text class="section-title-text">3. 用户权利</text>
        <text class="section-content">您在使用本服务时享有以下权利：</text>
        <text class="section-content">• 免费使用基础功能</text>
        <text class="section-content">• 数据隐私保护</text>
        <text class="section-content">• 随时导出个人数据</text>
        <text class="section-content">• 随时停用或删除账户</text>
        <text class="section-content">• 获得技术支持和客户服务</text>
      </view>

      <!-- 用户义务 -->
      <view class="agreement-section">
        <text class="section-title-text">4. 用户义务</text>
        <text class="section-content">使用本服务时，您需要遵守以下义务：</text>
        <text class="section-content">• 提供真实、准确的个人信息</text>
        <text class="section-content">• 不得利用本服务进行违法活动</text>
        <text class="section-content">• 不得恶意攻击或破坏服务</text>
        <text class="section-content">• 保护账户安全，不得与他人共享账户</text>
        <text class="section-content">• 尊重其他用户的权利</text>
      </view>

      <!-- 数据使用 -->
      <view class="agreement-section">
        <text class="section-title-text">5. 数据使用</text>
        <text class="section-content">关于您的数据使用，我们承诺：</text>
        <text class="section-content">• 仅收集必要的功能数据</text>
        <text class="section-content">• 不会将您的数据出售给第三方</text>
        <text class="section-content">• 采用行业标准的安全措施保护数据</text>
        <text class="section-content">• 详细的数据使用说明请参见《隐私政策》</text>
      </view>

      <!-- 服务变更 -->
      <view class="agreement-section">
        <text class="section-title-text">6. 服务变更</text>
        <text class="section-content">我们保留在必要时修改或终止服务的权利：</text>
        <text class="section-content">• 功能更新和改进</text>
        <text class="section-content">• 安全维护和升级</text>
        <text class="section-content">• 重大变更将提前通知用户</text>
        <text class="section-content">• 用户可选择接受变更或停用服务</text>
      </view>

      <!-- 免责声明 -->
      <view class="agreement-section">
        <text class="section-title-text">7. 免责声明</text>
        <text class="section-content">在法律允许的范围内：</text>
        <text class="section-content">• 本服务按"现状"提供，不保证完全无错误</text>
        <text class="section-content">• 不对因使用本服务造成的间接损失承担责任</text>
        <text class="section-content">• 不对第三方服务的可用性承担责任</text>
        <text class="section-content">• 用户应自行备份重要数据</text>
      </view>

      <!-- 争议解决 -->
      <view class="agreement-section">
        <text class="section-title-text">8. 争议解决</text>
        <text class="section-content">如发生争议，我们建议：</text>
        <text class="section-content">• 首先通过友好协商解决</text>
        <text class="section-content">• 可通过客服渠道寻求帮助</text>
        <text class="section-content">• 适用中华人民共和国法律</text>
        <text class="section-content">• 争议由服务提供方所在地法院管辖</text>
      </view>

      <!-- 联系方式 -->
      <view class="agreement-section">
        <text class="section-title-text">9. 联系我们</text>
        <text class="section-content">如对本协议有任何疑问，请联系我们：</text>
        <view class="contact-info">
          <view class="contact-item" bindtap="copyEmail">
            <custom-icon name="message" size="16" color="#4ECDC4"></custom-icon>
            <text class="contact-text">邮箱：<EMAIL></text>
          </view>
          <view class="contact-item" bindtap="callPhone">
            <custom-icon name="phone" size="16" color="#4ECDC4"></custom-icon>
            <text class="contact-text">电话：13342852835</text>
          </view>
        </view>
      </view>

      <!-- 协议更新 -->
      <view class="agreement-footer">
        <text class="update-time">协议生效日期：2025年1月25日</text>
        <text class="version-info">版本：v1.2.0</text>
      </view>
    </view>
  </view>
</view>
