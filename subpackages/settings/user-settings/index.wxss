/* subpackages/settings/user-settings/index.wxss */

.user-settings-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  padding: 32rpx;
  padding-bottom: 60rpx;
}

/* 卡片样式 */
.user-info-card,
.account-info-card,
.other-settings-card {
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
}

/* 标题样式 */
.section-title {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-left: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 设置项样式 */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.05);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

.setting-value {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
  flex-direction: column;
  align-items: flex-end;
}

.setting-tip {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  text-align: right;
}

.setting-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-right: 16rpx;
}

/* 头像设置 */
.avatar-options {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.avatar-btn {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  background: none;
  border: none;
  padding: 0;
}

.avatar-btn.wechat-avatar {
  border: 2rpx solid rgba(78, 205, 196, 0.5);
}

.custom-avatar-btn {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.custom-avatar-btn .btn-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 60rpx;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-btn:active .avatar-overlay {
  opacity: 1;
}

/* 昵称输入 */
.nickname-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  color: white;
  font-size: 26rpx;
  min-width: 300rpx;
  text-align: right;
}

.nickname-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* 地区选择器 */
.region-picker {
  display: flex;
  align-items: center;
}

/* 账户徽章 */
.account-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
  margin-left: 16rpx;
}

.account-badge.guest {
  background: rgba(255, 107, 107, 0.2);
  color: #FF6B6B;
  border: 1rpx solid rgba(255, 107, 107, 0.3);
}

.account-badge.wechat {
  background: rgba(78, 205, 196, 0.2);
  color: #4ECDC4;
  border: 1rpx solid rgba(78, 205, 196, 0.3);
}

/* 登录按钮 */
.login-btn {
  background: linear-gradient(135deg, #4ECDC4, #45B7D1);
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  margin-left: 16rpx;
}

.login-btn:active {
  transform: scale(0.95);
  transition: transform 0.15s ease;
}

/* 性别选择器模态框 */
.gender-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.gender-picker-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.gender-picker-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f1f2f6;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3436;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 30rpx;
  background: #f8f9fa;
}

.modal-close:active {
  background: #e9ecef;
}

/* 性别选项 */
.gender-options {
  padding: 0 32rpx 32rpx;
}

.gender-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f1f2f6;
}

.gender-option:last-child {
  border-bottom: none;
}

.gender-option.selected {
  color: #4ECDC4;
}

.option-text {
  font-size: 28rpx;
  color: inherit;
}

.gender-option:active {
  background: #f8f9fa;
}

/* 联系信息卡片 */
.contact-info-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  margin: 32rpx;
  padding: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

/* 个人偏好卡片 */
.preference-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  margin: 32rpx;
  padding: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

/* 输入框样式 */
.phone-input,
.email-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
}

.phone-input::placeholder,
.email-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}