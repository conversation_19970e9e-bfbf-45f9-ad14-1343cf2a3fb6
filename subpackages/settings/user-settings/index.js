// subpackages/settings/user-settings/index.js

const app = getApp()
import userManager from '../../../utils/userManager.js'

Page({
  data: {
    userInfo: {
      avatarUrl: '',
      nickName: '',
      gender: 0, // 0-未知 1-男 2-女
      city: '',
      province: '',
      country: ''
    },
    isGuest: true,
    genderOptions: ['未设置', '男', '女'],
    languageOptions: {
      'zh_CN': '简体中文',
      'zh_TW': '繁体中文',
      'en': 'English'
    },
    themeOptions: {
      'light': '浅色',
      'dark': '深色',
      'auto': '跟随系统'
    },
    showGenderPicker: false
  },

  async onLoad() {
    // 使用用户管理器初始化页面
    await userManager.mixinPage(this)
    this.loadUserInfo()
  },

  async onShow() {
    // 刷新用户信息
    await userManager.forceRefreshUserInfo()
    await userManager.mixinPage(this)
    this.loadUserInfo()
  },

  onUnload() {
    // 清理用户管理器监听器
    userManager.cleanupPage(this)
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      // {{ AURA-X: Modify - 移除游客判断逻辑，简化用户信息加载. Approval: 寸止(ID:1738056000). }}
      // 使用用户管理器获取用户信息
      const userInfo = userManager.getUserInfo()

      if (userInfo) {
        this.setData({
          userInfo: {
            avatarUrl: userInfo.avatarUrl || '/images/user.svg',
            nickName: userInfo.nickName || '微信用户',
            gender: userInfo.gender || 0,
            city: userInfo.city || '',
            province: userInfo.province || '',
            country: userInfo.country || ''
          },
          isGuest: false // 项目只支持微信登录，无游客模式
        })
      } else {
        // 如果没有用户信息，设置默认值
        this.setData({
          userInfo: {
            avatarUrl: '/images/user.svg',
            nickName: '微信用户',
            gender: 0,
            city: '',
            province: '',
            country: ''
          },
          isGuest: false
        })
      }
    } catch (error) {
      console.error('加载用户信息失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  // 🔥 处理微信头像选择结果
  async onChooseAvatar(e) {
    try {

      if (!e || !e.detail) {
        return
      }

      const { avatarUrl } = e.detail

      if (!avatarUrl) {
        return
      }

      wx.showLoading({ title: '保存头像中...' })

      // 🔥 重要：将微信临时头像上传到云存储进行持久化
      // 微信的avatarUrl是临时地址，需要上传到云存储才能持久保存
      await this.uploadAvatar(avatarUrl)
    } catch (error) {
      console.error('头像选择处理失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '头像更新失败',
        icon: 'none'
      })
    }
  },

  // 🔥 处理头像选择失败
  onChooseAvatarFail(e) {

    if (e && e.detail && e.detail.errMsg) {
      if (e.detail.errMsg.includes('cancel')) {
        return
      }

      if (e.detail.errMsg.includes('ENOENT')) {
        wx.showModal({
          title: '头像选择失败',
          content: '微信头像选择遇到问题，是否使用自定义头像？',
          confirmText: '自定义头像',
          cancelText: '稍后再试',
          success: (res) => {
            if (res.confirm) {
              this.uploadCustomAvatar()
            }
          }
        })
        return
      }
    }

    wx.showToast({
      title: '头像选择失败',
      icon: 'none'
    })
  },

  // 选择头像 - 显示选项
  chooseAvatar() {
    wx.showActionSheet({
      itemList: ['使用微信头像', '自定义头像', '使用默认头像'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 使用微信头像 - 提示用户点击头像
          wx.showModal({
            title: '选择微信头像',
            content: '请直接点击头像来选择您的微信头像',
            showCancel: false
          })
        } else if (res.tapIndex === 1) {
          // 自定义头像
          this.uploadCustomAvatar()
        } else if (res.tapIndex === 2) {
          // 使用默认头像
          this.useDefaultAvatar()
        }
      },
      fail: (err) => {
        console.log('用户取消了头像选择:', err)
      }
    })
  },

  // 使用默认头像
  useDefaultAvatar() {
    this.setData({
      'userInfo.avatarUrl': '/images/user.svg'
    })
    this.saveUserInfo()
    wx.showToast({
      title: '已设置默认头像',
      icon: 'success'
    })
  },

  // 🔥 获取微信头像
  async getWechatAvatar() {
    try {
      wx.showLoading({ title: '获取微信头像...' })

      // 调用微信getUserProfile获取最新头像
      const userProfile = await new Promise((resolve, reject) => {
        wx.getUserProfile({
          desc: '获取您的最新微信头像',
          success: resolve,
          fail: reject
        })
      })

      if (userProfile && userProfile.userInfo && userProfile.userInfo.avatarUrl) {
        // 更新用户头像为微信头像
        this.setData({
          'userInfo.avatarUrl': userProfile.userInfo.avatarUrl
        })

        this.saveUserInfo()

        wx.hideLoading()
        wx.showToast({
          title: '微信头像更新成功',
          icon: 'success'
        })
      } else {
        throw new Error('获取微信头像失败')
      }
    } catch (error) {
      console.error('获取微信头像失败:', error)
      wx.hideLoading()

      if (error.errMsg && error.errMsg.includes('auth deny')) {
        wx.showToast({
          title: '您拒绝了授权',
          icon: 'none'
        })
      } else {
        wx.showToast({
          title: '获取微信头像失败',
          icon: 'none'
        })
      }
    }
  },

  // 🔥 上传自定义头像
  uploadCustomAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      sizeType: ['compressed'],
      success: (res) => {
        if (res.tempFiles && res.tempFiles.length > 0) {
          const tempFilePath = res.tempFiles[0].tempFilePath
          this.uploadAvatar(tempFilePath)
        }
      },
      fail: (err) => {
        console.error('选择头像失败:', err)

        // 用户取消不显示错误
        if (err.errMsg && err.errMsg.includes('cancel')) {
          return
        }

        // 权限问题
        if (err.errMsg && err.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '权限提示',
            content: '需要访问您的相册权限，请在设置中开启',
            showCancel: false
          })
          return
        }

        wx.showToast({
          title: '选择头像失败',
          icon: 'none'
        })
      }
    })
  },

  // 上传头像到云存储
  async uploadAvatar(tempFilePath) {
    wx.showLoading({ title: '上传中...' })

    try {
      // {{ AURA-X: Modify - 移除guest引用，使用用户openid. Approval: 寸止(ID:1738056000). }}
      const cloudPath = `avatars/${this.data.userInfo.openid || 'user'}_${Date.now()}.jpg`

      const uploadRes = await wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: tempFilePath
      })

      // 更新用户头像
      this.setData({
        'userInfo.avatarUrl': uploadRes.fileID
      })

      this.saveUserInfo()

      wx.hideLoading()
      wx.showToast({
        title: '头像更新成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('上传头像失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '头像上传失败',
        icon: 'none'
      })
    }
  },

  // 输入昵称
  onNickNameInput(e) {
    const nickName = e.detail.value
    this.setData({
      'userInfo.nickName': nickName
    })
  },

  // 昵称输入完成
  onNickNameBlur() {
    this.saveUserInfo()
  },

  // 显示性别选择器
  showGenderPicker() {
    this.setData({
      showGenderPicker: true
    })
  },

  // 选择性别
  onGenderChange(e) {
    const gender = parseInt(e.detail.value)
    this.setData({
      'userInfo.gender': gender,
      showGenderPicker: false
    })
    this.saveUserInfo()
  },

  // 取消性别选择
  onGenderCancel() {
    this.setData({
      showGenderPicker: false
    })
  },

  // 选择地区
  onRegionChange(e) {
    const [province, city, country] = e.detail.value
    this.setData({
      'userInfo.province': province,
      'userInfo.city': city,
      'userInfo.country': country
    })
    this.saveUserInfo()
  },

  // 手机号输入
  onPhoneInput(e) {
    this.setData({
      'userInfo.phone': e.detail.value
    })
  },

  // 手机号失焦保存
  onPhoneBlur() {
    this.saveUserInfo()
  },

  // 邮箱输入
  onEmailInput(e) {
    this.setData({
      'userInfo.email': e.detail.value
    })
  },

  // 邮箱失焦保存
  onEmailBlur() {
    this.saveUserInfo()
  },

  // 显示语言选择器
  showLanguagePicker() {
    const languages = Object.keys(this.data.languageOptions)
    const languageNames = Object.values(this.data.languageOptions)

    wx.showActionSheet({
      itemList: languageNames,
      success: (res) => {
        const selectedLanguage = languages[res.tapIndex]
        this.setData({
          'userInfo.language': selectedLanguage
        })
        this.saveUserInfo()
      }
    })
  },

  // 显示主题选择器
  showThemePicker() {
    const themes = Object.keys(this.data.themeOptions)
    const themeNames = Object.values(this.data.themeOptions)

    wx.showActionSheet({
      itemList: themeNames,
      success: (res) => {
        const selectedTheme = themes[res.tapIndex]
        this.setData({
          'userInfo.theme': selectedTheme
        })
        this.saveUserInfo()
      }
    })
  },

  // 保存用户信息
  async saveUserInfo() {
    try {
      const { userInfo } = this.data

      // 🔥 使用用户管理器的updateUserInfo方法
      const success = await userManager.updateUserInfo(userInfo)

      if (!success) {
        throw new Error('保存失败')
      }

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('保存用户信息失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  },

  // 保存到云数据库
  async saveToCloud(userInfo) {
    const db = wx.cloud.database()
    const openid = app.globalData.openid

    try {
      // 更新用户信息
      await db.collection('users').where({
        _openid: openid
      }).update({
        data: {
          userInfo: userInfo,
          updateTime: new Date()
        }
      })
    } catch (error) {
      console.error('保存到云数据库失败:', error)
      throw error
    }
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除本地缓存吗？这将删除所有本地数据。',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.clearStorageSync()
            wx.showToast({
              title: '清除成功',
              icon: 'success'
            })

            // 重新加载用户信息
            setTimeout(() => {
              this.loadUserInfo()
            }, 1000)
          } catch (error) {
            console.error('清除缓存失败:', error)
            wx.showToast({
              title: '清除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 导航到登录页面
  navigateToLogin() {
    wx.navigateTo({
      url: '/pages/login/index',
      fail: (err) => {
        console.error('导航到登录页失败:', err)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  }
})