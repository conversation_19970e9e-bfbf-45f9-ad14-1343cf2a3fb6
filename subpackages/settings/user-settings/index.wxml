<!--subpackages/settings/user-settings/index.wxml-->
<view class="user-settings-container">
  <!-- 用户信息卡片 -->
  <view class="user-info-card">
    <view class="section-title">
      <custom-icon name="user" size="20" color="#4ECDC4"></custom-icon>
      <text class="title-text">个人信息</text>
    </view>

    <!-- 头像设置 -->
    <view class="setting-item">
      <text class="setting-label">头像</text>
      <view class="setting-value">
        <view class="avatar-options">
          <!-- 微信头像选择按钮 -->
          <button
            class="avatar-btn wechat-avatar"
            open-type="chooseAvatar"
            bind:chooseavatar="onChooseAvatar"
            bind:error="onChooseAvatarFail">
            <image class="avatar-image" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
            <view class="avatar-overlay">
              <custom-icon name="camera" size="16" color="#fff"></custom-icon>
            </view>
          </button>
          <!-- 自定义头像按钮 -->
          <view class="custom-avatar-btn" bindtap="uploadCustomAvatar">
            <text class="btn-text">自定义头像</text>
          </view>
        </view>
        <text class="setting-tip">点击头像使用微信头像，或点击"自定义头像"上传图片</text>
      </view>
    </view>

    <!-- 昵称设置 -->
    <view class="setting-item">
      <text class="setting-label">昵称</text>
      <view class="setting-value">
        <input
          class="nickname-input"
          type="nickname"
          placeholder="请输入昵称"
          value="{{userInfo.nickName}}"
          bindinput="onNickNameInput"
          bindblur="onNickNameBlur"
          maxlength="20"
        />
      </view>
    </view>

    <!-- 性别设置 -->
    <view class="setting-item" bindtap="showGenderPicker">
      <text class="setting-label">性别</text>
      <view class="setting-value">
        <text class="setting-text">{{genderOptions[userInfo.gender]}}</text>
        <custom-icon name="arrow-right" size="16" color="rgba(255, 255, 255, 0.5)"></custom-icon>
      </view>
    </view>

    <!-- 地区设置 -->
    <view class="setting-item">
      <text class="setting-label">地区</text>
      <view class="setting-value">
        <picker mode="region" bindchange="onRegionChange" value="{{[userInfo.province, userInfo.city, userInfo.country]}}">
          <view class="region-picker">
            <text class="setting-text">
              {{userInfo.province && userInfo.city ? userInfo.province + ' ' + userInfo.city : '请选择地区'}}
            </text>
            <custom-icon name="arrow-right" size="16" color="rgba(255, 255, 255, 0.5)"></custom-icon>
          </view>
        </picker>
      </view>
    </view>
  </view>

  <!-- 联系信息 -->
  <view class="contact-info-card">
    <view class="section-title">
      <custom-icon name="phone" size="20" color="#FF6B6B"></custom-icon>
      <text class="title-text">联系信息</text>
    </view>

    <!-- 手机号设置 -->
    <view class="setting-item">
      <text class="setting-label">手机号</text>
      <view class="setting-value">
        <input
          class="phone-input"
          type="number"
          placeholder="请输入手机号"
          value="{{userInfo.phone}}"
          bindinput="onPhoneInput"
          bindblur="onPhoneBlur"
          maxlength="11"
        />
      </view>
    </view>

    <!-- 邮箱设置 -->
    <view class="setting-item">
      <text class="setting-label">邮箱</text>
      <view class="setting-value">
        <input
          class="email-input"
          type="text"
          placeholder="请输入邮箱地址"
          value="{{userInfo.email}}"
          bindinput="onEmailInput"
          bindblur="onEmailBlur"
        />
      </view>
    </view>
  </view>

  <!-- 个人偏好 -->
  <view class="preference-card">
    <view class="section-title">
      <custom-icon name="settings" size="20" color="#6c5ce7"></custom-icon>
      <text class="title-text">个人偏好</text>
    </view>

    <!-- 语言设置 -->
    <view class="setting-item" bindtap="showLanguagePicker">
      <text class="setting-label">语言</text>
      <view class="setting-value">
        <text class="setting-text">{{languageOptions[userInfo.language] || '简体中文'}}</text>
        <custom-icon name="arrow-right" size="16" color="rgba(255, 255, 255, 0.5)"></custom-icon>
      </view>
    </view>

    <!-- 主题设置 -->
    <view class="setting-item" bindtap="showThemePicker">
      <text class="setting-label">主题</text>
      <view class="setting-value">
        <text class="setting-text">{{themeOptions[userInfo.theme] || '浅色'}}</text>
        <custom-icon name="arrow-right" size="16" color="rgba(255, 255, 255, 0.5)"></custom-icon>
      </view>
    </view>
  </view>

  <!-- 账户信息 -->
  <view class="account-info-card">
    <view class="section-title">
      <custom-icon name="shield" size="20" color="#45B7D1"></custom-icon>
      <text class="title-text">账户信息</text>
    </view>

    <view class="setting-item">
      <text class="setting-label">账户类型</text>
      <view class="setting-value">
        <text class="setting-text">{{isGuest ? '游客账户' : '微信用户'}}</text>
        <view class="account-badge {{isGuest ? 'guest' : 'wechat'}}">
          {{isGuest ? '游客' : '正式'}}
        </view>
      </view>
    </view>

    <view class="setting-item" wx:if="{{isGuest}}">
      <text class="setting-label">数据同步</text>
      <view class="setting-value">
        <text class="setting-text">登录后可同步数据</text>
        <button class="login-btn" bindtap="navigateToLogin">
          立即登录
        </button>
      </view>
    </view>
  </view>

  <!-- 其他设置 -->
  <view class="other-settings-card">
    <view class="section-title">
      <custom-icon name="settings" size="20" color="#FF6B6B"></custom-icon>
      <text class="title-text">其他设置</text>
    </view>

    <view class="setting-item" bindtap="clearCache">
      <text class="setting-label">清除缓存</text>
      <view class="setting-value">
        <text class="setting-text">清除本地缓存数据</text>
        <custom-icon name="arrow-right" size="16" color="rgba(255, 255, 255, 0.5)"></custom-icon>
      </view>
    </view>
  </view>

  <!-- 性别选择器 -->
  <view class="gender-picker-modal {{showGenderPicker ? 'show' : ''}}" wx:if="{{showGenderPicker}}">
    <view class="modal-mask" bindtap="onGenderCancel"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择性别</text>
        <view class="modal-close" bindtap="onGenderCancel">
          <custom-icon name="x" size="20" color="#999"></custom-icon>
        </view>
      </view>
      <view class="gender-options">
        <view
          class="gender-option {{userInfo.gender === index ? 'selected' : ''}}"
          wx:for="{{genderOptions}}"
          wx:key="index"
          data-value="{{index}}"
          bindtap="onGenderChange"
        >
          <text class="option-text">{{item}}</text>
          <custom-icon
            name="check"
            size="16"
            color="#4ECDC4"
            wx:if="{{userInfo.gender === index}}"
          ></custom-icon>
        </view>
      </view>
    </view>
  </view>
</view>