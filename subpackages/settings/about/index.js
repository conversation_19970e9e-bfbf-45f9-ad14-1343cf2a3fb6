// subpackages/settings/about/index.js
import auth from '../../../utils/auth.js'
import { APP_INFO } from '../../../utils/constants.js'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    appInfo: {
      name: APP_INFO.NAME,
      version: APP_INFO.VERSION,
      description: '一款专注个人记账的微信小程序，集成旅行规划功能，让财务管理变得简单而美好。',
      features: [
        '智能记账管理',
        '旅行规划助手',
        '预算控制提醒',
        '数据统计分析',
        '多设备同步',
        '隐私安全保护'
      ]
    },
    contactInfo: {
      email: '<EMAIL>',
      phone: '13342852835'
    },
    teamInfo: {
      developer: 'wan.',
      company: '个人开发者',
      location: '中国'
    },
    legalInfo: [
      {
        title: '用户协议',
        content: '使用本应用即表示您同意遵守我们的用户协议条款。'
      },
      {
        title: '隐私政策',
        content: '我们重视您的隐私，严格保护您的个人信息安全。'
      },
      {
        title: '免责声明',
        content: '本应用仅供个人记账使用，不承担任何投资建议责任。'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查登录状态
    if (!auth.checkPageAuth(this)) {
      return
    }
  },

  /**
   * 复制联系方式
   */
  copyContact(e) {
    const { type } = e.currentTarget.dataset
    const { contactInfo } = this.data

    let text = ''
    let label = ''

    switch (type) {
      case 'email':
        text = contactInfo.email
        label = '邮箱'
        break
      case 'phone':
        text = contactInfo.phone
        label = '电话'
        break
    }

    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: `${label}已复制`,
          icon: 'success'
        })
      }
    })
  },

  /**
   * 查看详细条款
   */
  viewLegalDetail(e) {
    const { index } = e.currentTarget.dataset
    const { legalInfo } = this.data
    const item = legalInfo[index]

    let url = ''
    switch (index) {
      case 0: // 用户协议
        url = '/subpackages/settings/user-agreement/index'
        break
      case 1: // 隐私政策
        url = '/subpackages/settings/privacy/index'
        break
      case 2: // 免责声明
        wx.showModal({
          title: item.title,
          content: item.content,
          showCancel: false,
          confirmText: '我知道了'
        })
        return
    }

    if (url) {
      wx.navigateTo({
        url: url,
        fail: (err) => {
          console.error('页面跳转失败:', err)
          wx.showToast({
            title: '页面暂未开放',
            icon: 'none'
          })
        }
      })
    }
  },

  /**
   * 检查更新
   */
  checkUpdate() {
    wx.showLoading({ title: '检查中...' })

    // 模拟检查更新
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '已是最新版本',
        icon: 'success'
      })
    }, 1000)
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '爱巢小记 - 让记账变得简单而美好',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    }
  }
})