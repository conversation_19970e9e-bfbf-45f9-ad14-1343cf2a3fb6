/* subpackages/settings/privacy/index.wxss */

.privacy-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  padding: 32rpx;
  padding-bottom: 60rpx;
}

/* 隐私卡片 */
.privacy-card {
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  overflow: hidden;
}

/* 标题样式 */
.section-title {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-left: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 隐私政策样式 */
.policy-content {
  padding: 0 32rpx 32rpx;
}

.policy-section {
  margin-bottom: 32rpx;
}

.policy-title {
  font-size: 30rpx;
  font-weight: 600;
  color: white;
  display: block;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.policy-subtitle {
  font-size: 26rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  display: block;
  margin-bottom: 8rpx;
  margin-top: 16rpx;
}

.policy-item {
  margin-bottom: 24rpx;
}

.policy-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  display: block;
  margin-bottom: 8rpx;
}

.contact-info {
  margin-top: 16rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.contact-item:active {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8rpx;
  transform: scale(0.98);
  transition: all 0.15s ease;
}

.policy-footer {
  margin-top: 32rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.update-time,
.version-info {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .privacy-container {
    padding: 24rpx;
  }

  .policy-content {
    padding: 0 24rpx 24rpx;
  }

  .policy-footer {
    flex-direction: column;
    gap: 8rpx;
    align-items: flex-start;
  }
}
