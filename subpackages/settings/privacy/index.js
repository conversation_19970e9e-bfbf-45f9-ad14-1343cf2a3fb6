// subpackages/settings/privacy/index.js

Page({
  data: {
    
  },

  onLoad(options) {
    // 页面加载
  },

  onShow() {
    // 页面显示
  },

  onHide() {
    // 页面隐藏
  },

  onUnload() {
    // 页面卸载
  },

  // 复制邮箱地址
  copyEmail() {
    wx.setClipboardData({
      data: '<EMAIL>',
      success: () => {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        })
      }
    })
  },

  // 拨打电话
  callPhone() {
    wx.makePhoneCall({
      phoneNumber: '13342852835',
      success: () => {
        // 拨打成功
      },
      fail: (err) => {
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        })
      }
    })
  }
})
