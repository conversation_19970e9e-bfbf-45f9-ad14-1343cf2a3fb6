// subpackages/settings/help/index.js
import auth from '../../../utils/auth.js'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    contactInfo: {
      email: '<EMAIL>',
      phone: '13342852835',
      wechat: 'aichao-xiaoji'
    },
    feedbackText: '',
    feedbackType: 0,
    feedbackTypes: [
      { value: 'suggestion', label: '功能建议' },
      { value: 'bug', label: '问题反馈' },
      { value: 'complaint', label: '投诉建议' },
      { value: 'other', label: '其他' }
    ],
    faqList: [
      {
        question: '如何开始记账？',
        answer: '点击首页的"记一笔"按钮，选择收入或支出类型，输入金额和分类即可完成记账。'
      },
      {
        question: '如何制定旅行计划？',
        answer: '进入旅行规划页面，点击"新建计划"，填写目的地、时间、预算等信息，系统会帮您生成详细的旅行计划。'
      },
      {
        question: '游客模式和会员模式有什么区别？',
        answer: '游客模式数据仅保存在本地，会员模式数据同步到云端，支持多设备同步和数据备份。'
      },
      {
        question: '如何导出我的记账数据？',
        answer: '在记账管理页面，点击右上角菜单，选择"导出数据"，可以导出Excel或PDF格式的账单。'
      },
      {
        question: '忘记密码怎么办？',
        answer: '本应用使用微信登录，无需设置密码。如果遇到登录问题，请联系客服。'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查登录状态
    if (!auth.checkPageAuth(this)) {
      return
    }
  },

  /**
   * 选择反馈类型
   */
  onFeedbackTypeChange(e) {
    const index = e.detail.value
    this.setData({
      feedbackType: index
    })
  },

  /**
   * 输入反馈内容
   */
  onFeedbackInput(e) {
    this.setData({
      feedbackText: e.detail.value
    })
  },

  /**
   * 提交反馈
   */
  async submitFeedback() {
    const { feedbackText, feedbackType } = this.data
    
    if (!feedbackText.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      })
      return
    }

    wx.showLoading({ title: '提交中...' })

    try {
      // 这里应该调用云函数提交反馈
      // 暂时使用模拟提交
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      wx.hideLoading()
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      })
      
      // 清空表单
      this.setData({
        feedbackText: '',
        feedbackType: 'suggestion'
      })
      
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 复制联系方式
   */
  copyContact(e) {
    const { type } = e.currentTarget.dataset
    const { contactInfo } = this.data
    
    let text = ''
    let label = ''
    
    switch (type) {
      case 'email':
        text = contactInfo.email
        label = '邮箱'
        break
      case 'phone':
        text = contactInfo.phone
        label = '电话'
        break
      case 'wechat':
        text = contactInfo.wechat
        label = '微信号'
        break
    }
    
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: `${label}已复制`,
          icon: 'success'
        })
      }
    })
  },

  /**
   * 拨打电话
   */
  makePhoneCall() {
    wx.makePhoneCall({
      phoneNumber: this.data.contactInfo.phone
    })
  }
})
