/* subpackages/account/expense-detail/index.wxss */

.detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  padding: 24rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

/* 详情内容 */
.detail-content {
  padding-bottom: 160rpx;
}

/* 金额展示区域 */
.amount-section {
  text-align: center;
  padding: 48rpx 24rpx;
  margin-bottom: 32rpx;
}

.amount-container {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 24rpx;
}

.currency {
  font-size: 48rpx;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.8);
  margin-right: 8rpx;
}

.amount {
  font-size: 96rpx;
  font-weight: 700;
  line-height: 1;
}

.amount.expense {
  color: #FF6B6B;
}

.amount.income {
  color: #52c41a;
}

.type-tags {
  display: flex;
  justify-content: center;
  gap: 16rpx;
}

/* 详细信息区域 */
.info-section {
  margin-bottom: 32rpx;
}

.info-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.info-item:last-child {
  border-bottom: none;
}

.info-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-right: 24rpx;
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 400;
}

.info-value {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  word-break: break-all;
}

/* 照片展示区域 */
.photos-section {
  margin-bottom: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
}

.section-title text {
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.photos-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.photo-item {
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.photo-item:active {
  transform: scale(0.95);
}

/* 操作按钮区域 */
.actions-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 24rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.edit {
  background: linear-gradient(135deg, #4ECDC4 0%, #45B7D1 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(78, 205, 196, 0.3);
}

.action-btn.delete {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn.edit:active {
  box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.3);
}

.action-btn.delete:active {
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

/* 错误状态 */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.error-content {
  text-align: center;
  padding: 48rpx;
}

.error-text {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.7);
  margin: 32rpx 0;
  display: block;
}

.error-btn {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  color: rgba(255, 255, 255, 0.9);
  padding: 20rpx 48rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: inline-block;
  transition: all 0.3s ease;
}

.error-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}
