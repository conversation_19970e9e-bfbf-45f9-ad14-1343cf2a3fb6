<!--subpackages/account/expense-detail/index.wxml-->
<view class="detail-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <van-loading type="spinner" size="48rpx" color="white">加载中...</van-loading>
  </view>

  <!-- 详情内容 -->
  <view class="detail-content" wx:if="{{!loading && record}}">
    <!-- 金额展示区域 -->
    <view class="amount-section">
      <view class="amount-container">
        <text class="currency">¥</text>
        <text class="amount {{record.record_type === 'expense' ? 'expense' : 'income'}}">
          {{record.amount}}
        </text>
      </view>
      <view class="type-tags">
        <van-tag 
          size="medium" 
          color="{{record.record_type === 'expense' ? '#FF6B6B' : '#52c41a'}}"
          text-color="white"
        >
          {{record.record_type === 'expense' ? '支出' : '收入'}}
        </van-tag>
        <van-tag 
          size="medium" 
          color="{{record.type === 'travel' ? '#45B7D1' : '#4ECDC4'}}"
          text-color="white"
        >
          {{record.type === 'travel' ? '旅行' : '日常'}}
        </van-tag>
      </view>
    </view>

    <!-- 详细信息 -->
    <view class="info-section">
      <view class="info-card">
        <!-- 分类信息 -->
        <view class="info-item">
          <view class="info-icon">
            <custom-icon 
              name="{{record.category.icon || 'tag'}}" 
              size="32" 
              color="#FF6B6B" 
            />
          </view>
          <view class="info-content">
            <text class="info-label">分类</text>
            <text class="info-value">{{record.category.name || '其他'}}</text>
          </view>
        </view>

        <!-- 日期时间 -->
        <view class="info-item">
          <view class="info-icon">
            <custom-icon name="calendar" size="32" color="#4ECDC4" />
          </view>
          <view class="info-content">
            <text class="info-label">日期</text>
            <text class="info-value">{{record.formattedDateTime}}</text>
          </view>
        </view>

        <!-- 描述信息 -->
        <view class="info-item">
          <view class="info-icon">
            <custom-icon name="edit" size="32" color="#45B7D1" />
          </view>
          <view class="info-content">
            <text class="info-label">描述</text>
            <text class="info-value">{{record.description || '无备注'}}</text>
          </view>
        </view>

        <!-- 位置信息 -->
        <view class="info-item" wx:if="{{record.location}}">
          <view class="info-icon">
            <custom-icon name="location" size="32" color="#6c5ce7" />
          </view>
          <view class="info-content">
            <text class="info-label">位置</text>
            <text class="info-value">{{record.location.name || record.location}}</text>
          </view>
        </view>

        <!-- 旅行计划 -->
        <view class="info-item" wx:if="{{record.travel_plan_id && record.planInfo}}">
          <view class="info-icon">
            <custom-icon name="airplane" size="32" color="#e17055" />
          </view>
          <view class="info-content">
            <text class="info-label">旅行计划</text>
            <text class="info-value">{{record.planInfo.title}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 照片展示 -->
    <view class="photos-section" wx:if="{{record.images && record.images.length > 0}}">
      <view class="section-title">
        <custom-icon name="photo" size="28" color="rgba(255,255,255,0.8)" />
        <text>相关照片</text>
      </view>
      <view class="photos-grid">
        <view 
          wx:for="{{record.images}}" 
          wx:key="index"
          class="photo-item"
          bindtap="previewPhoto"
          data-index="{{index}}"
        >
          <van-image
            src="{{item}}"
            fit="cover"
            width="200rpx"
            height="200rpx"
            radius="16rpx"
            loading-icon="photo"
          />
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="actions-section">
      <view class="action-buttons">
        <view class="action-btn edit" bindtap="editRecord">
          <custom-icon name="edit" size="28" color="white" />
          <text>编辑</text>
        </view>
        <view class="action-btn delete" bindtap="deleteRecord">
          <custom-icon name="delete" size="28" color="white" />
          <text>删除</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:if="{{!loading && !record}}">
    <view class="error-content">
      <custom-icon name="alert-triangle" size="120" color="rgba(255,255,255,0.5)" />
      <text class="error-text">记录不存在或已被删除</text>
      <view class="error-btn" bindtap="navigateBack">
        <text>返回列表</text>
      </view>
    </view>
  </view>
</view>
