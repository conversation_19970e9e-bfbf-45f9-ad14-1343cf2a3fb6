// subpackages/account/expense-detail/index.js
const dataManager = require('../../../utils/data-manager.js').default
const auth = require('../../../utils/auth.js').default

Page({
  data: {
    record: null,
    loading: false,
    recordId: ''
  },

  onLoad(options) {
    // 检查登录状态
    if (!auth.checkPageAuth(this)) {
      return
    }

    if (options.id) {
      this.setData({ recordId: options.id })
      this.loadRecordDetail()
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 加载记录详情
  async loadRecordDetail() {
    this.setData({ loading: true })

    try {
      // 获取所有记录，然后找到对应的记录
      const result = await dataManager.getExpenseRecords({
        limit: 1000
      })

      if (result.success && result.data) {
        const record = result.data.find(item => item._id === this.data.recordId)
        
        if (record) {
          const processedRecord = this.processRecordData(record)
          this.setData({ record: processedRecord })
          
          // 如果有旅行计划ID，加载计划信息
          if (record.travel_plan_id) {
            this.loadPlanInfo(record.travel_plan_id)
          }
        } else {
          this.setData({ record: null })
        }
      } else {
        this.setData({ record: null })
      }
    } catch (error) {
      console.error('加载记录详情失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      this.setData({ record: null })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 处理记录数据
  processRecordData(record) {
    // 格式化日期时间
    const date = new Date(record.date || record.createTime)
    const formattedDateTime = this.formatDateTime(date)
    
    // 确保分类信息
    const category = record.category || { name: '其他', icon: 'money' }
    
    return {
      ...record,
      formattedDateTime,
      category,
      amount: Number(record.amount) || 0
    }
  },

  // 格式化日期时间
  formatDateTime(date) {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    
    return `${year}年${month}月${day}日 ${hours}:${minutes}`
  },

  // 加载旅行计划信息
  async loadPlanInfo(planId) {
    try {
      const db = wx.cloud.database()
      const result = await db.collection('travel_plans').doc(planId).get()
      
      if (result.data) {
        this.setData({
          'record.planInfo': result.data
        })
      }
    } catch (error) {
      console.error('加载旅行计划信息失败:', error)
    }
  },

  // 预览照片
  previewPhoto(e) {
    const index = e.currentTarget.dataset.index
    const images = this.data.record.images
    
    wx.previewImage({
      current: images[index],
      urls: images
    })
  },

  // 编辑记录
  editRecord() {
    const record = this.data.record
    
    // 构建编辑页面的参数
    const params = new URLSearchParams({
      mode: record.type || 'daily',
      edit: 'true',
      id: record._id,
      amount: record.amount.toString(),
      description: record.description || '',
      date: record.date || '',
      category: record.category?.id || '',
      planId: record.travel_plan_id || ''
    })
    
    wx.navigateTo({
      url: `/subpackages/account/travel-expense/index?${params.toString()}`
    })
  },

  // 删除记录
  deleteRecord() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条账单记录吗？删除后将无法恢复。',
      confirmText: '删除',
      confirmColor: '#FF6B6B',
      success: (res) => {
        if (res.confirm) {
          this.performDelete()
        }
      }
    })
  },

  // 执行删除
  async performDelete() {
    try {
      wx.showLoading({ title: '删除中...' })
      
      const result = await dataManager.deleteExpenseRecord(this.data.recordId)
      
      if (result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
        
        // 延迟返回，让用户看到成功提示
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        throw new Error(result.message || '删除失败')
      }
    } catch (error) {
      console.error('删除记录失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack({
      fail: () => {
        // 如果无法返回，则跳转到账单列表
        wx.navigateTo({
          url: '/subpackages/account/expense-list/index'
        })
      }
    })
  }
})
