/* subpackages/account/expense-list/index.wxss */

.expense-list-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  padding-bottom: 160rpx;
}

/* 筛选标签页 */
.filter-tabs {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  margin: 24rpx;
  border-radius: 32rpx;
  padding: 8rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.tab-item text {
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.tab-item.active text {
  color: #333;
  font-weight: 600;
}

.filter-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.filter-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

/* 账单列表 */
.expense-list {
  padding: 0 24rpx;
}

.month-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx 16rpx;
  margin-top: 16rpx;
}

.month-title {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.month-amount {
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
}

.month-list {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.expense-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  position: relative;
  transition: all 0.3s ease;
}

.expense-item:last-child {
  border-bottom: none;
}

.expense-item:active {
  background: rgba(255, 255, 255, 0.1);
}

/* 左侧图标 */
.item-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-right: 24rpx;
}

/* 中间内容 */
.item-content {
  flex: 1;
  min-width: 0;
}

.item-title {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.category-name {
  font-size: 32rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-right: 16rpx;
}

.item-desc {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.description {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.date {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.5);
}

/* 右侧金额 */
.item-amount {
  text-align: right;
  margin-left: 16rpx;
}

.amount {
  font-size: 32rpx;
  font-weight: 600;
}

.amount.expense {
  color: #FF6B6B;
}

.amount.income {
  color: #52c41a;
}

/* 删除按钮 */
.delete-btn {
  position: absolute;
  right: -120rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 100rpx;
  height: 80rpx;
  background: #FF6B6B;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}

/* 空状态 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

/* 底部区域 */
.bottom-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 24rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.statistics {
  text-align: center;
  margin-bottom: 16rpx;
}

.stats-text {
  font-size: 24rpx;
  color: #666;
}

.quick-action {
  display: flex;
  justify-content: center;
}

.quick-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
  color: white;
  padding: 16rpx 48rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.quick-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

/* 筛选弹窗 */
.filter-popup {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  padding: 32rpx;
  max-height: 80vh;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.popup-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
}

.filter-content {
  margin-bottom: 32rpx;
}

.filter-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.time-options,
.category-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.option-item {
  padding: 16rpx 24rpx;
  background: #f5f5f5;
  border-radius: 24rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.option-item.active {
  background: #FF6B6B;
  color: white;
}

.filter-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  text-align: center;
  padding: 24rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.reset {
  background: #f5f5f5;
  color: #666;
}

.action-btn.confirm {
  background: #FF6B6B;
  color: white;
}

.action-btn:active {
  transform: scale(0.95);
}
