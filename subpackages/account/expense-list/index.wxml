<!--subpackages/account/expense-list/index.wxml-->
<view class="expense-list-container">
  <!-- 筛选标签页 -->
  <view class="filter-tabs">
    <view 
      class="tab-item {{activeTab === 'all' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="all"
    >
      <text>全部</text>
    </view>
    <view 
      class="tab-item {{activeTab === 'expense' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="expense"
    >
      <text>支出</text>
    </view>
    <view 
      class="tab-item {{activeTab === 'income' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="income"
    >
      <text>收入</text>
    </view>
    <view class="filter-btn" bindtap="showFilterPopup">
      <custom-icon name="settings" size="32" color="#666" />
    </view>
  </view>

  <!-- 账单列表 -->
  <view class="expense-list" wx:if="{{!loading && expenseList.length > 0}}">
    <block wx:for="{{groupedExpenses}}" wx:key="month">
      <!-- 月份标题 -->
      <view class="month-header">
        <text class="month-title">{{item.month}}</text>
        <text class="month-amount">¥{{item.totalAmount}}</text>
      </view>
      
      <!-- 该月的账单列表 -->
      <view class="month-list">
        <view 
          wx:for="{{item.records}}" 
          wx:key="_id"
          wx:for-item="record"
          class="expense-item"
          bindtap="viewDetail"
          data-record="{{record}}"
          bindlongpress="showDeleteOption"
          data-id="{{record._id}}"
        >
          <!-- 左侧分类图标 -->
          <view class="item-icon">
            <custom-icon 
              name="{{record.category.icon || 'money'}}" 
              size="44" 
              color="{{record.record_type === 'expense' ? '#FF6B6B' : '#52c41a'}}" 
            />
          </view>
          
          <!-- 中间信息 -->
          <view class="item-content">
            <view class="item-title">
              <text class="category-name">{{record.category.name || '其他'}}</text>
              <van-tag 
                size="mini" 
                color="{{record.type === 'travel' ? '#45B7D1' : '#4ECDC4'}}"
                text-color="white"
              >
                {{record.type === 'travel' ? '旅行' : '日常'}}
              </van-tag>
            </view>
            <view class="item-desc">
              <text class="description">{{record.description || '无备注'}}</text>
              <text class="date">{{record.formattedDate}}</text>
            </view>
          </view>
          
          <!-- 右侧金额 -->
          <view class="item-amount">
            <text class="amount {{record.record_type === 'expense' ? 'expense' : 'income'}}">
              {{record.record_type === 'expense' ? '-' : '+'}}¥{{record.amount}}
            </text>
          </view>
          
          <!-- 左滑删除按钮 -->
          <view class="delete-btn" bindtap="deleteRecord" data-id="{{record._id}}" catchtap>
            <custom-icon name="delete" size="32" color="white" />
          </view>
        </view>
      </view>
    </block>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <van-loading type="spinner" size="48rpx" color="#FF6B6B">加载中...</van-loading>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!loading && expenseList.length === 0}}">
    <van-empty description="暂无账单记录">
      <view slot="image">
        <custom-icon name="wallet" size="120" color="#ddd" />
      </view>
    </van-empty>
  </view>

  <!-- 底部统计和快速记账 -->
  <view class="bottom-section">
    <view class="statistics">
      <text class="stats-text">本月共 {{currentMonthCount}} 笔记录，总计 ¥{{currentMonthTotal}}</text>
    </view>
    <view class="quick-action">
      <view class="quick-btn" bindtap="quickRecord">
        <custom-icon name="plus" size="40" color="white" />
        <text>快速记账</text>
      </view>
    </view>
  </view>

  <!-- 筛选弹窗 -->
  <van-popup 
    show="{{showFilter}}" 
    position="bottom" 
    round
    bind:close="hideFilterPopup"
  >
    <view class="filter-popup">
      <view class="popup-header">
        <text class="popup-title">筛选条件</text>
        <view class="popup-close" bindtap="hideFilterPopup">
          <custom-icon name="close" size="36" color="#666" />
        </view>
      </view>
      
      <view class="filter-content">
        <!-- 时间筛选 -->
        <view class="filter-section">
          <text class="section-title">时间范围</text>
          <view class="time-options">
            <view 
              class="option-item {{timeFilter === 'month' ? 'active' : ''}}"
              bindtap="setTimeFilter"
              data-filter="month"
            >
              本月
            </view>
            <view 
              class="option-item {{timeFilter === 'quarter' ? 'active' : ''}}"
              bindtap="setTimeFilter"
              data-filter="quarter"
            >
              近3个月
            </view>
            <view 
              class="option-item {{timeFilter === 'all' ? 'active' : ''}}"
              bindtap="setTimeFilter"
              data-filter="all"
            >
              全部
            </view>
          </view>
        </view>
        
        <!-- 分类筛选 -->
        <view class="filter-section">
          <text class="section-title">分类</text>
          <view class="category-options">
            <view 
              wx:for="{{categoryOptions}}" 
              wx:key="id"
              class="option-item {{categoryFilter === item.id ? 'active' : ''}}"
              bindtap="setCategoryFilter"
              data-filter="{{item.id}}"
            >
              {{item.name}}
            </view>
          </view>
        </view>
      </view>
      
      <view class="filter-actions">
        <view class="action-btn reset" bindtap="resetFilter">重置</view>
        <view class="action-btn confirm" bindtap="applyFilter">确定</view>
      </view>
    </view>
  </van-popup>
</view>
