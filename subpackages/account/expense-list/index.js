// subpackages/account/expense-list/index.js
const dataManager = require('../../../utils/data-manager.js').default
const auth = require('../../../utils/auth.js').default

Page({
  data: {
    // 基础数据
    expenseList: [],
    groupedExpenses: [],
    loading: false,
    
    // 筛选状态
    activeTab: 'all', // all, expense, income
    timeFilter: 'month', // month, quarter, all
    categoryFilter: 'all',
    showFilter: false,
    
    // 统计数据
    currentMonthCount: 0,
    currentMonthTotal: '0.00',
    
    // 筛选选项
    categoryOptions: [
      { id: 'all', name: '全部分类' },
      { id: 'food', name: '餐饮' },
      { id: 'transport', name: '交通' },
      { id: 'shopping', name: '购物' },
      { id: 'entertainment', name: '娱乐' },
      { id: 'other', name: '其他' }
    ]
  },

  onLoad() {
    // 检查登录状态
    if (!auth.checkPageAuth(this)) {
      return
    }
    
    this.loadExpenseList()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadExpenseList()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.loadExpenseList().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    // 上拉加载更多（暂时不实现分页）
  },

  // 加载账单列表
  async loadExpenseList() {
    this.setData({ loading: true })
    
    try {
      const result = await dataManager.getExpenseRecords({
        limit: 1000 // 获取所有记录
      })
      
      if (result.success && result.data) {
        const processedList = this.processExpenseList(result.data)
        this.setData({
          expenseList: processedList
        })
        
        // 应用当前筛选条件
        this.applyCurrentFilter()
        
        // 计算统计数据
        this.calculateStatistics()
      } else {
        this.setData({
          expenseList: [],
          groupedExpenses: []
        })
      }
    } catch (error) {
      console.error('加载账单列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 处理账单列表数据
  processExpenseList(rawList) {
    return rawList.map(record => {
      // 格式化日期
      const date = new Date(record.date || record.createTime)
      const formattedDate = this.formatDate(date)
      
      // 确保分类信息
      const category = record.category || { name: '其他', icon: 'money' }
      
      return {
        ...record,
        formattedDate,
        category,
        amount: Number(record.amount) || 0
      }
    }).sort((a, b) => {
      // 按日期倒序排列
      const dateA = new Date(a.date || a.createTime)
      const dateB = new Date(b.date || b.createTime)
      return dateB - dateA
    })
  },

  // 格式化日期
  formatDate(date) {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
    const recordDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
    
    if (recordDate.getTime() === today.getTime()) {
      return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    } else if (recordDate.getTime() === yesterday.getTime()) {
      return `昨天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    } else {
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      return `${month}-${day} ${hours}:${minutes}`
    }
  },

  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({ activeTab: tab })
    this.applyCurrentFilter()
  },

  // 应用当前筛选条件
  applyCurrentFilter() {
    let filteredList = [...this.data.expenseList]
    
    // 按类型筛选
    if (this.data.activeTab !== 'all') {
      filteredList = filteredList.filter(record => 
        record.record_type === this.data.activeTab
      )
    }
    
    // 按时间筛选
    if (this.data.timeFilter !== 'all') {
      const now = new Date()
      let startDate
      
      if (this.data.timeFilter === 'month') {
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
      } else if (this.data.timeFilter === 'quarter') {
        startDate = new Date(now.getFullYear(), now.getMonth() - 2, 1)
      }
      
      if (startDate) {
        filteredList = filteredList.filter(record => {
          const recordDate = new Date(record.date || record.createTime)
          return recordDate >= startDate
        })
      }
    }
    
    // 按分类筛选
    if (this.data.categoryFilter !== 'all') {
      filteredList = filteredList.filter(record => 
        record.category?.id === this.data.categoryFilter ||
        record.category?.main === this.data.categoryFilter
      )
    }
    
    // 按月分组
    const grouped = this.groupByMonth(filteredList)
    this.setData({ groupedExpenses: grouped })
  },

  // 按月分组
  groupByMonth(list) {
    const groups = {}
    
    list.forEach(record => {
      const date = new Date(record.date || record.createTime)
      const monthKey = `${date.getFullYear()}年${(date.getMonth() + 1).toString().padStart(2, '0')}月`
      
      if (!groups[monthKey]) {
        groups[monthKey] = {
          month: monthKey,
          records: [],
          totalAmount: 0
        }
      }
      
      groups[monthKey].records.push(record)
      groups[monthKey].totalAmount += record.amount
    })
    
    // 转换为数组并按月份排序
    return Object.values(groups)
      .map(group => ({
        ...group,
        totalAmount: group.totalAmount.toFixed(2)
      }))
      .sort((a, b) => {
        // 按月份倒序排列
        return b.month.localeCompare(a.month)
      })
  },

  // 计算统计数据
  calculateStatistics() {
    const now = new Date()
    const currentMonth = now.getMonth()
    const currentYear = now.getFullYear()
    
    const currentMonthRecords = this.data.expenseList.filter(record => {
      const recordDate = new Date(record.date || record.createTime)
      return recordDate.getMonth() === currentMonth && 
             recordDate.getFullYear() === currentYear
    })
    
    const total = currentMonthRecords.reduce((sum, record) => sum + record.amount, 0)
    
    this.setData({
      currentMonthCount: currentMonthRecords.length,
      currentMonthTotal: total.toFixed(2)
    })
  },

  // 查看详情
  viewDetail(e) {
    const record = e.currentTarget.dataset.record
    wx.navigateTo({
      url: `/subpackages/account/expense-detail/index?id=${record._id}`
    })
  },

  // 显示删除选项
  showDeleteOption(e) {
    const id = e.currentTarget.dataset.id
    wx.showActionSheet({
      itemList: ['删除记录'],
      itemColor: '#FF6B6B',
      success: (res) => {
        if (res.tapIndex === 0) {
          this.confirmDelete(id)
        }
      }
    })
  },

  // 确认删除
  confirmDelete(id) {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条账单记录吗？删除后将无法恢复。',
      confirmText: '删除',
      confirmColor: '#FF6B6B',
      success: (res) => {
        if (res.confirm) {
          this.deleteRecord({ currentTarget: { dataset: { id } } })
        }
      }
    })
  },

  // 删除记录
  async deleteRecord(e) {
    const id = e.currentTarget.dataset.id
    
    try {
      wx.showLoading({ title: '删除中...' })
      
      const result = await dataManager.deleteExpenseRecord(id)
      
      if (result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
        
        // 重新加载列表
        this.loadExpenseList()
      } else {
        throw new Error(result.message || '删除失败')
      }
    } catch (error) {
      console.error('删除记录失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 快速记账
  quickRecord() {
    wx.navigateTo({
      url: '/subpackages/account/travel-expense/index?mode=daily'
    })
  },

  // 显示筛选弹窗
  showFilterPopup() {
    this.setData({ showFilter: true })
  },

  // 隐藏筛选弹窗
  hideFilterPopup() {
    this.setData({ showFilter: false })
  },

  // 设置时间筛选
  setTimeFilter(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({ timeFilter: filter })
  },

  // 设置分类筛选
  setCategoryFilter(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({ categoryFilter: filter })
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      timeFilter: 'month',
      categoryFilter: 'all'
    })
  },

  // 应用筛选
  applyFilter() {
    this.hideFilterPopup()
    this.applyCurrentFilter()
  }
})
