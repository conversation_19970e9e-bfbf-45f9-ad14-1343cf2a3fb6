<!--subpackages/account/travel-expense/index.wxml-->
<view class="expense-container">
  <!-- 页面头部区域 -->
  <view class="page-header">
    <view class="header-buttons">
      <view class="mode-switch-btn" bindtap="showModeSelector">
        <custom-icon name="switch" size="48" color="white" />
        <text class="mode-text">{{recordMode === 'travel' ? '旅行模式' : '日常模式'}}</text>
      </view>
      <view class="history-btn" bindtap="navigateToHistory">
        <custom-icon name="history" size="32" color="white" />
        <text class="history-text">历史记录</text>
      </view>
    </view>
  </view>

  <!-- 记账模式切换弹窗 -->
  <view class="mode-selector {{showModePanel ? 'show' : ''}}" bindtap="hideModeSelector">
    <view class="mode-panel" catchtap="stopPropagation">
      <view class="mode-header">
        <text class="mode-title">选择记账模式</text>
        <view class="mode-close" bindtap="hideModeSelector">
          <custom-icon name="close" size="38" color="#95A5A6" />
        </view>
      </view>
      <view class="mode-options">
        <view class="mode-option {{recordMode === 'daily' ? 'active' : ''}}" bindtap="switchMode" data-mode="daily">
          <view class="mode-icon">
            <custom-icon name="wallet" size="46" color="{{recordMode === 'daily' ? '#FF6B6B' : '#95A5A6'}}" />
          </view>
          <view class="mode-info">
            <text class="mode-name">日常记账</text>
            <text class="mode-desc">记录日常生活开支</text>
          </view>
          <view class="mode-check" wx:if="{{recordMode === 'daily'}}">
            <custom-icon name="check" size="38" color="#FF6B6B" />
          </view>
        </view>
        <view class="mode-option {{recordMode === 'travel' ? 'active' : ''}}" bindtap="switchMode" data-mode="travel">
          <view class="mode-icon">
            <custom-icon name="airplane" size="46" color="{{recordMode === 'travel' ? '#FF6B6B' : '#95A5A6'}}" />
          </view>
          <view class="mode-info">
            <text class="mode-name">旅行记账</text>
            <text class="mode-desc">记录旅行相关花费</text>
          </view>
          <view class="mode-check" wx:if="{{recordMode === 'travel'}}">
            <custom-icon name="check" size="38" color="#FF6B6B" />
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 收入/支出切换器 - 旅行模式下只显示支出 -->
  <view class="type-switcher" wx:if="{{recordMode === 'daily'}}" style="width: 750rpx; height: 107rpx; display: flex; box-sizing: border-box">
    <view class="switcher-container">
      <view class="switch-item {{expenseType === 'expense' ? 'active' : ''}}" bindtap="switchType" data-type="expense">
        <text>支出</text>
      </view>
      <view class="switch-item {{expenseType === 'income' ? 'active' : ''}}" bindtap="switchType" data-type="income">
        <text>收入</text>
      </view>
      <view class="switch-indicator" style="transform: translateX({{expenseType === 'income' ? '100%' : '0'}})"></view>
    </view>
  </view>

  <!-- 旅行模式标题 -->
  <view class="travel-mode-header" wx:if="{{recordMode === 'travel'}}">
    <view class="travel-title">
      <custom-icon name="airplane" size="38" color="#FF6B6B" />
      <text class="title-text">旅行支出记录</text>
    </view>
    <text class="travel-subtitle">记录您的旅行花费，让每一笔支出都有迹可循</text>
  </view>

  <!-- 金额输入区域 -->
  <view class="amount-section">
    <view class="amount-container" style="height: 67rpx; display: flex; box-sizing: border-box; align-items: center;">
      <text class="currency-symbol">¥</text>
      <input style="height: 63rpx; display: block; box-sizing: border-box; flex: 1;"
        class="amount-input"
        type="digit"
        placeholder="0.00"
        value="{{amount}}"
        bindinput="onAmountInput"
        focus="{{amountFocus}}"
      />
      <!-- 语音记账按钮 -->
      <voice-recorder
        record-mode="{{expenseType === 'travel' ? 'travel' : 'daily'}}"
        bind:voiceResult="onVoiceResult"
      />
    </view>
    <view class="amount-tips" wx:if="{{amountTips}}">
      <text>{{amountTips}}</text>
    </view>
  </view>

  <!-- 分类选择区域 -->
  <view class="category-section">
    <view class="section-title">
      <custom-icon name="tag" size="30" color="#FF6B6B" />
      <text>选择分类</text>
      <text class="required-mark">*</text>
    </view>

    <!-- 分类标签页 -->
    <view class="category-tabs">
      <view
        wx:for="{{categoryTabs}}"
        wx:key="id"
        class="tab-item {{selectedTab === item.id ? 'active' : ''}}"
        bindtap="switchCategoryTab"
        data-id="{{item.id}}"
      >
        <text>{{item.name}}</text>
      </view>
    </view>

    <!-- 分类网格 -->
    <view class="category-grid">
      <view
        wx:for="{{currentTabCategories}}"
        wx:key="id"
        class="category-item {{selectedCategory === item.id ? 'selected' : ''}}"
        bindtap="selectCategory"
        data-id="{{item.id}}"
      >
        <view class="category-icon" style="background-color: {{item.color}}">
          <custom-icon name="{{item.icon}}" size="38" color="white" />
        </view>
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 旅行计划关联（仅旅行模式） -->
  <view class="travel-plan-section" wx:if="{{recordMode === 'travel'}}">
    <view class="section-title">
      <custom-icon name="map" size="30" color="#FF6B6B" />
      <text>关联旅行计划</text>
    </view>
    <view class="plan-selector" bindtap="selectTravelPlan">
      <view class="plan-info" wx:if="{{selectedPlan}}">
        <text class="plan-name">{{selectedPlan.title}}</text>
        <text class="plan-date">{{selectedPlan.dateRange.startDate}} - {{selectedPlan.dateRange.endDate}}</text>
      </view>
      <view class="plan-placeholder" wx:else>
        <text>选择旅行计划</text>
      </view>
      <custom-icon name="arrow-right" size="30" color="#95A5A6" />
    </view>
  </view>

  <!-- 详细信息卡片 -->
  <view class="details-section">
    <!-- 备注说明 -->
    <view class="detail-item">
      <view class="detail-label">
        <custom-icon name="edit" size="30" color="#FF6B6B" />
        <text>备注说明</text>
      </view>
      <textarea
        class="description-input"
        placeholder="{{recordMode === 'travel' ? '记录旅行中的美好时光...' : '添加备注信息...'}}"
        value="{{description}}"
        bindinput="onDescriptionInput"
        maxlength="200"
      />
      <view class="char-count">{{description.length}}/200</view>
    </view>

    <!-- 记账日期 -->
    <view class="detail-item">
      <view class="detail-label">
        <custom-icon name="calendar" size="30" color="#FF6B6B" />
        <text>记账日期</text>
      </view>
      <picker mode="date" value="{{selectedDate}}" bindchange="onDateChange">
        <view class="date-picker">
          <text>{{selectedDate}}</text>
          <custom-icon name="arrow-right" size="30" color="#95A5A6" />
        </view>
      </picker>
    </view>

    <!-- 位置信息（旅行模式） -->
    <view class="detail-item" wx:if="{{recordMode === 'travel'}}">
      <view class="detail-label">
        <custom-icon name="location" size="30" color="#FF6B6B" />
        <text>消费地点</text>
        <text class="optional-label">（可选）</text>
      </view>
      <view class="location-selector" bindtap="selectLocation">
        <view class="location-info" wx:if="{{selectedLocation}}">
          <text class="location-name">{{selectedLocation.name}}</text>
          <text class="location-address">{{selectedLocation.address}}</text>
        </view>
        <view class="location-placeholder" wx:else>
          <text>点击添加位置信息</text>
        </view>
        <custom-icon name="arrow-right" size="30" color="#95A5A6" />
      </view>
      <view class="location-tips">
        <text>添加位置信息可以帮助您更好地管理旅行支出</text>
      </view>
    </view>

    <!-- 照片上传（仅旅行模式） -->
    <view class="detail-item" wx:if="{{recordMode === 'travel'}}">
      <view class="detail-label">
        <custom-icon name="photo" size="30" color="#FF6B6B" />
        <text>添加照片</text>
        <text class="optional-label">（可选）</text>
      </view>
      <view class="photo-upload">
        <view class="photo-list">
          <view
            wx:for="{{photos}}"
            wx:key="index"
            class="photo-item"
          >
            <image src="{{item}}" mode="aspectFill" bindtap="previewPhoto" data-index="{{index}}" />
            <view class="photo-delete" bindtap="deletePhoto" data-index="{{index}}">
              <custom-icon name="close" size="26" color="white" />
            </view>
          </view>
          <view class="photo-add" bindtap="addPhoto" wx:if="{{photos.length < 9}}">
            <custom-icon name="plus" size="38" color="#95A5A6" />
          </view>
        </view>
      </view>
      <view class="photo-tips">
        <text>添加照片可以帮助您记录旅行中的美好时光</text>
      </view>
    </view>
  </view>

  <!-- 底部保存按钮 -->
  <view class="save-section">
    <button
      class="save-btn {{amount && selectedCategory ? 'active' : 'disabled'}}"
      bindtap="saveRecord"
      disabled="{{!amount || !selectedCategory}}"
      loading="{{loading}}"
    >
      <custom-icon name="check" size="38" color="white" wx:if="{{!loading}}" />
      <text>{{loading ? loadingText : '保存记录'}}</text>
    </button>
  </view>
</view>


