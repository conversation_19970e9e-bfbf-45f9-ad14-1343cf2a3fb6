/* subpackages/account/travel-expense/index.wxss - 符合微信小程序设计规范的记账页面样式 */

.expense-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  position: relative;
  overflow-x: hidden;
  padding-bottom: 160rpx;
}

/* 页面头部区域 */
.page-header {
  padding: 24rpx 32rpx 16rpx;
}

.header-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16rpx;
}

.history-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  min-height: 44rpx;
}

.history-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.history-text {
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.mode-switch-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  min-height: 44rpx;
}

.mode-switch-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.mode-text {
  font-size: 26rpx;
  color: white;
  font-weight: 500;
}

/* 记账模式选择面板 */
.mode-selector {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mode-selector.show {
  opacity: 1;
  visibility: visible;
}

.mode-panel {
  background: white;
  border-radius: 24rpx;
  margin: 0 40rpx;
  max-width: 600rpx;
  width: 100%;
  padding: 32rpx;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.mode-selector.show .mode-panel {
  transform: scale(1);
}

.mode-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.mode-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3436;
}

.mode-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.mode-close:active {
  background-color: #f5f6fa;
}

.mode-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.mode-option {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
  min-height: 88rpx;
}

.mode-option.active {
  border-color: #FF6B6B;
  background: rgba(255, 107, 107, 0.05);
}

.mode-option:active {
  transform: scale(0.98);
}

.mode-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #f5f6fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.mode-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.mode-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3436;
}

.mode-desc {
  font-size: 24rpx;
  color: #636e72;
  line-height: 1.4;
}

.mode-check {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 收入/支出切换器 */
.type-switcher {
  padding: 24rpx 48rpx 8rpx;
  display: flex;
  justify-content: center;
}

.switcher-container {
  position: relative;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  padding: 8rpx;
  display: flex;
  min-width: 280rpx;
}

.switch-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.3s ease;
  z-index: 2;
  position: relative;
  border-radius: 8rpx;
  min-height: 44rpx;
}

.switch-item.active {
  color: white;
}

.switch-indicator {
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  width: calc(50% - 4rpx);
  height: calc(100% - 8rpx);
  background: rgba(255, 255, 255, 0.3);
  border-radius: 8rpx;
  transition: transform 0.3s ease;
  backdrop-filter: blur(10rpx);
}

/* 金额输入区域 */
.amount-section {
  padding: 32rpx 48rpx 24rpx;
  text-align: center;
}

.amount-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.currency-symbol {
  font-size: 48rpx;
  font-weight: 300;
  color: white;
}

.amount-input {
  font-size: 64rpx;
  font-weight: 300;
  color: white;
  text-align: center;
  background: transparent;
  border: none;
  outline: none;
  min-width: 200rpx;
}

.amount-tips {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 16rpx;
  padding: 12rpx 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  display: inline-block;
}

/* 分类选择区域 - 使用统一毛玻璃效果 */
.category-section {
  background: rgba(255, 255, 255, 0.15);
  /* 移除重复backdrop-filter，使用统一标准 */
  margin: 24rpx 32rpx;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    0 1rpx 0 rgba(255, 255, 255, 0.3) inset,
    0 -1rpx 0 rgba(0, 0, 0, 0.1) inset;
  /* 应用统一毛玻璃效果 */
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
}

/* 分类标签页 - 使用轻量毛玻璃效果 */
.category-tabs {
  display: flex;
  margin-bottom: 24rpx;
  background: rgba(255, 255, 255, 0.3);
  /* 使用轻量毛玻璃效果避免过度模糊 */
  backdrop-filter: blur(16rpx) saturate(160%);
  -webkit-backdrop-filter: blur(16rpx) saturate(160%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  padding: 6rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12rpx 0;
  font-size: 26rpx;
  color: #636e72;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  min-height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-item.active {
  background: white;
  color: #FF6B6B;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-item:active {
  transform: scale(0.98);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5rpx;
}

.required-mark {
  width: 12rpx;
  height: 12rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  border-radius: 50%;
  display: inline-block;
  margin-left: 8rpx;
  box-shadow: 0 2rpx 4rpx rgba(255, 107, 107, 0.3);
  position: relative;
  top: -2rpx;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 12rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  position: relative;
  min-height: 120rpx;
}

.category-item:active {
  transform: scale(0.95);
}

.category-item.selected {
  background: rgba(255, 107, 107, 0.1);
}

.category-item.selected::after {
  content: '';
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 20rpx;
  height: 20rpx;
  background: #FF6B6B;
  border-radius: 50%;
}

.category-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f6fa;
}

.category-icon.more-icon {
  background: #f5f6fa;
  border: 2rpx dashed #d9d9d9;
}

.category-name {
  font-size: 24rpx;
  color: #2d3436;
  text-align: center;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 旅行计划选择区域 - 高级毛玻璃效果 */
.travel-plan-section {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx) saturate(180%);
  margin: 16rpx 32rpx;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    0 1rpx 0 rgba(255, 255, 255, 0.3) inset,
    0 -1rpx 0 rgba(0, 0, 0, 0.1) inset;
}

.plan-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  margin-top: 16rpx;
  min-height: 88rpx;
  transition: all 0.3s ease;
}

.plan-selector:active {
  transform: scale(0.98);
  background: rgba(245, 246, 250, 1);
}

.plan-info {
  flex: 1;
}

.plan-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3436;
  display: block;
  margin-bottom: 8rpx;
}

.plan-date {
  font-size: 24rpx;
  color: #636e72;
}

.plan-placeholder {
  flex: 1;
  font-size: 28rpx;
  color: #b2bec3;
}

/* 详细信息区域 - 高级毛玻璃效果 */
.details-section {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx) saturate(180%);
  margin: 16rpx 32rpx 40rpx;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    0 1rpx 0 rgba(255, 255, 255, 0.3) inset,
    0 -1rpx 0 rgba(0, 0, 0, 0.1) inset;
}

.detail-item {
  margin-bottom: 32rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2rpx 3rpx rgba(0, 0, 0, 0.25);
  letter-spacing: 0.3rpx;
}

.optional-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 400;
  margin-left: 8rpx;
  background: rgba(255, 255, 255, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.description-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  font-size: 26rpx;
  color: rgba(45, 52, 54, 0.9);
  line-height: 1.5;
  box-sizing: border-box;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.description-input::placeholder {
  color: rgba(45, 52, 54, 0.5);
  font-style: italic;
}

.char-count {
  text-align: right;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 12rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  font-weight: 400;
}

.date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  font-size: 26rpx;
  color: rgba(45, 52, 54, 0.9);
  min-height: 44rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.date-picker:active {
  transform: scale(0.98);
  background: rgba(245, 246, 250, 1);
}

/* 位置选择器 */
.location-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  margin-bottom: 12rpx;
  min-height: 44rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.location-selector:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.5);
}

.location-info {
  flex: 1;
}

.location-name {
  font-size: 26rpx;
  font-weight: 500;
  color: rgba(45, 52, 54, 0.9);
  display: block;
  margin-bottom: 4rpx;
}

.location-address {
  font-size: 22rpx;
  color: rgba(45, 52, 54, 0.6);
}

.location-placeholder {
  flex: 1;
  font-size: 26rpx;
  color: rgba(45, 52, 54, 0.5);
  font-style: italic;
}

.location-tips {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 照片上传 */
.photo-upload {
  margin-top: 16rpx;
  margin-bottom: 12rpx;
}

.photo-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.photo-item {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.photo-item image {
  width: 100%;
  height: 100%;
}

.photo-delete {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff4d4f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.photo-add {
  width: 120rpx;
  height: 120rpx;
  border: 2rpx dashed rgba(255, 255, 255, 0.4);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5rpx);
  transition: all 0.3s ease;
}

.photo-add:active {
  transform: scale(0.95);
  border-color: rgba(255, 107, 107, 0.6);
  background: rgba(255, 255, 255, 0.3);
}

.photo-tips {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

/* 底部保存按钮 */
.save-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 32rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  outline: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.save-btn::after {
  border: none;
}

.save-btn.active {
  background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.4);
}

.save-btn.active:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.save-btn.disabled {
  background: #f5f6fa;
  color: #b2bec3;
  box-shadow: none;
}

/* 旅行模式标题样式 */
.travel-mode-header {
  padding: 32rpx 24rpx;
  text-align: center;
}

.travel-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}

.travel-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}