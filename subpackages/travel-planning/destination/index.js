// subpackages/travel-planning/destination/index.js

import dataManager from '../../../utils/data-manager.js'

Page({
  data: {
    // 最近选择
    recentDestinations: [],

    // 状态
    loading: false,
    loadingText: '',

    // 页面模式
    mode: 'select' // select: 选择模式, browse: 浏览模式
  },

  onLoad(options) {
    // 设置页面模式
    if (options.mode) {
      this.setData({ mode: options.mode })
    }

    this.initPage()
  },

  onShow() {
    this.loadRecentDestinations()
  },

  onPullDownRefresh() {
    this.loadPopularDestinations().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 初始化页面
  initPage() {
    try {
      this.setData({
        loading: true,
        loadingText: '加载最近选择...'
      })

      // 只加载最近选择的目的地
      this.loadRecentDestinations()

    } catch (error) {
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },



  // 加载最近选择
  loadRecentDestinations() {
    try {
      const recent = wx.getStorageSync('recent_destinations') || []
      this.setData({ recentDestinations: recent.slice(0, 5) })
    } catch (error) {
      this.setData({ recentDestinations: [] })
    }
  },









  // 获取当前位置
  getCurrentLocation() {
    wx.showLoading({
      title: '获取位置中...',
      mask: true
    });

    // 检查权限并获取位置
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation'] === false) {
          wx.hideLoading();
          wx.showModal({
            title: '需要位置权限',
            content: '为了获取您的位置，需要开启位置权限。请在设置中开启位置权限。',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.userLocation']) {
                      this.doGetCurrentLocation();
                    }
                  }
                });
              }
            }
          });
        } else if (res.authSetting['scope.userLocation'] === undefined) {
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => {
              this.doGetCurrentLocation();
            },
            fail: () => {
              wx.hideLoading();
              wx.showToast({
                title: '需要位置权限才能使用此功能',
                icon: 'none'
              });
            }
          });
        } else {
          this.doGetCurrentLocation();
        }
      }
    });
  },

  // 执行获取当前位置 - 直接使用微信位置选择器
  doGetCurrentLocation() {
    // 直接打开微信位置选择器
    wx.chooseLocation({
      success: (res) => {
        wx.hideLoading();

        const destination = {
          id: 'current_' + Date.now(),
          name: res.name || '当前位置',
          address: res.address || '地址信息不完整',
          latitude: res.latitude,
          longitude: res.longitude
        };

        wx.showToast({
          title: '位置选择成功',
          icon: 'success',
          duration: 1000
        });

        // 自动选择并返回
        this.selectDestination({ currentTarget: { dataset: { destination } } });
      },
      fail: (err) => {
        wx.hideLoading();

        if (!err.errMsg.includes('cancel')) {
          wx.showToast({
            title: '位置选择失败，请重试',
            icon: 'none'
          });
        }
      }
    });
  },






  // 执行位置选择（优化版本 - 先获取精确位置再打开地图）
  doChooseLocation() {
    // 先获取当前精确位置
    wx.showLoading({
      title: '定位中...',
      mask: true
    })

    wx.getLocation({
      type: 'gcj02',
      altitude: true,
      isHighAccuracy: true, // 开启高精度定位
      highAccuracyExpireTime: 4000, // 高精度定位超时时间
      success: (locationRes) => {
        wx.hideLoading()

        // 使用获取到的精确位置打开地图选择
        wx.chooseLocation({
          latitude: locationRes.latitude,
          longitude: locationRes.longitude,
          success: (res) => {
            const destination = {
              id: 'current_' + Date.now(),
              name: res.name || '未知地点',
              address: res.address || '地址信息不完整',
              latitude: res.latitude,
              longitude: res.longitude
            }

            wx.showToast({
              title: '位置选择成功',
              icon: 'success'
            })

            this.selectDestination({ currentTarget: { dataset: { destination } } })
          },
          fail: (err) => {
            console.error('选择位置失败:', err)
            if (err.errMsg.includes('cancel')) {
              return
            }

            wx.showToast({
              title: '位置选择失败，请重试',
              icon: 'none'
            })
          }
        })
      },
      fail: (err) => {
        wx.hideLoading()
        console.error('获取位置失败:', err)

        // 如果获取位置失败，仍然打开地图选择（不传入坐标）
        wx.showModal({
          title: '定位失败',
          content: '无法获取精确位置，将打开地图供您手动选择位置',
          showCancel: false,
          confirmText: '确定',
          success: () => {
            wx.chooseLocation({
              success: (res) => {
                const destination = {
                  id: 'current_' + Date.now(),
                  name: res.name || '未知地点',
                  address: res.address || '地址信息不完整',
                  latitude: res.latitude,
                  longitude: res.longitude
                }

                wx.showToast({
                  title: '位置选择成功',
                  icon: 'success'
                })

                this.selectDestination({ currentTarget: { dataset: { destination } } })
              },
              fail: (err) => {
                if (!err.errMsg.includes('cancel')) {
                  wx.showToast({
                    title: '位置选择失败，请重试',
                    icon: 'none'
                  })
                }
              }
            })
          }
        })
      }
    })
  },





  // 选择目的地
  selectDestination(e) {
    const destination = e.currentTarget.dataset.destination
    
    if (!destination) return
    
    // 保存到最近选择
    this.saveToRecent(destination)
    
    // 如果是选择模式，返回结果
    if (this.data.mode === 'select') {
      const eventChannel = this.getOpenerEventChannel()
      if (eventChannel) {
        eventChannel.emit('selectDestination', {
          name: destination.name,
          coordinates: destination.latitude ? {
            latitude: destination.latitude,
            longitude: destination.longitude
          } : null
        })
      }
      wx.navigateBack()
    } else {
      // 浏览模式，显示详情
      wx.showToast({
        title: `选择了${destination.name}`,
        icon: 'success'
      })
    }
  },

  // 保存到最近选择
  saveToRecent(destination) {
    try {
      const recent = wx.getStorageSync('recent_destinations') || []
      // 移除重复项
      const filtered = recent.filter(item => item.name !== destination.name)
      // 添加到开头
      filtered.unshift(destination)
      // 只保留最近5个
      const updated = filtered.slice(0, 5)
      wx.setStorageSync('recent_destinations', updated)
      this.loadRecentDestinations()
    } catch (error) {
      // 静默处理保存失败
    }
  },

  // 清空最近选择
  clearRecent() {
    const self = this
    wx.showModal({
      title: '确认清空',
      content: '确定要清空最近选择的目的地吗？',
      success: function(res) {
        if (res.confirm) {
          travelDataService.clearRecentDestinations()
          self.setData({ recentDestinations: [] })
        }
      }
    })
  },

  // 热门目的地功能已移除

  // 分享页面
  onShareAppMessage() {
    return {
      title: '爱巢小记 - 选择目的地',
      path: '/subpackages/travel-planning/destination/index'
    }
  }
})
