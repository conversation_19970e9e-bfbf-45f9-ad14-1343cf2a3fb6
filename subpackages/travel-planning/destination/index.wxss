/* subpackages/travel-planning/destination/index.wxss */

.destination-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  position: relative;
}

/* 背景装饰 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 150rpx;
  height: 150rpx;
  bottom: 20%;
  left: 15%;
  animation-delay: 2s;
}

.orb-3 {
  width: 100rpx;
  height: 100rpx;
  top: 50%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
}

/* 内容滚动区域 */
.content-scroll {
  height: 100vh;
  position: relative;
  z-index: 1;
}





/* 通用区域样式 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx 16rpx;
  margin-top: 24rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.section-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.section-action:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

/* 快速选择 */
.quick-actions-section {
  padding: 0 32rpx 24rpx;
}

.quick-actions {
  display: flex;
  gap: 16rpx;
}

.quick-action-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.quick-action-item:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.2);
}

.action-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.action-icon.map {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
}

.action-icon.location {
  background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
}

.action-title {
  font-size: 24rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 4rpx;
  display: block;
}

.action-desc {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
  display: block;
}

/* 最近选择 */
.recent-section {
  padding: 0 32rpx 24rpx;
}

.recent-list {
  background: rgba(255, 255, 255, 0.15);
  /* 使用统一毛玻璃效果 */
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  overflow: hidden;
}

.recent-item {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  gap: 16rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.recent-item:last-child {
  border-bottom: none;
}

.recent-item:active {
  background: rgba(255, 255, 255, 0.1);
}

.recent-icon {
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recent-info {
  flex: 1;
}

.recent-name {
  font-size: 26rpx;
  color: white;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
}

.recent-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  display: block;
}

/* 热门推荐样式已移除，功能将在发现页面中实现 */

/* 热门相关样式已全部移除 */

.rating-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}



/* 加载提示 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx;
  color: #2d3436;
}



/* 底部安全距离 */
.safe-bottom {
  height: 120rpx;
}
