<!--创建旅行计划页面 - 革新设计-->
<view class="create-plan-container">
  <!-- 渐变背景 -->
  <view class="gradient-background"></view>

  <!-- 页面内容 -->
  <scroll-view class="content-scroll" scroll-y="true" enhanced="true" show-scrollbar="false">

    <!-- 简洁页面标题 -->
    <view class="page-header">
      <view class="header-content">
        <view class="header-icon">
          <custom-icon name="airplane" size="54" color="#4ECDC4" />
        </view>
        <view class="header-text">
          <text class="header-title">创建旅行计划</text>
          <text class="header-subtitle">开始规划你的精彩旅程</text>
        </view>
      </view>
    </view>

    <!-- 创建方式选择 -->
    <view wx:if="{{currentStep === 0}}" class="creation-selector">
      <view class="selector-header">
        <text class="page-title">创建旅行计划</text>
      </view>

      <view class="options-container">
        <!-- AI智能规划 -->
        <view class="option-card recommended" bindtap="showSmartPlanForm">
          <view class="card-badge">推荐</view>
          <view class="card-icon">🧠</view>
          <view class="card-content">
            <text class="card-title">AI智能规划</text>
            <text class="card-desc">输入目的地，AI为您生成完整行程</text>
          </view>
        </view>

        <!-- 手动创建 -->
        <view class="option-card" bindtap="skipToManualCreate">
          <view class="card-icon">✍️</view>
          <view class="card-content">
            <text class="card-title">手动创建</text>
            <text class="card-desc">从零开始，完全自定义您的行程</text>
          </view>
        </view>

        <!-- 魔法解析 -->
        <view class="option-card magic-card" bindtap="showMagicParse">
          <view class="card-badge magic-badge">魔法</view>
          <view class="card-icon magic-icon">✨</view>
          <view class="card-content">
            <text class="card-title">一键抄作业</text>
            <text class="card-desc">复制小红书/公众号链接，秒变专属攻略</text>
          </view>
          <view class="magic-sparkle">
            <text class="sparkle">✨</text>
            <text class="sparkle">⭐</text>
            <text class="sparkle">💫</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 智能规划表单 -->
    <view wx:if="{{showSmartForm}}" class="form-modal">
      <view class="modal-mask" bindtap="hideSmartPlanForm"></view>
      <view class="modal-dialog">
        <view class="dialog-header smart-header">
          <view class="smart-title-container">
            <text class="smart-icon">🎯</text>
            <text class="dialog-title">一键智能规划</text>
            <text class="smart-badge">AI</text>
          </view>
          <text class="dialog-close" bindtap="hideSmartPlanForm">×</text>
        </view>

        <view class="dialog-body">
          <!-- 目的地 -->
          <view class="field-group">
            <text class="field-label">目的地</text>
            <view class="destination-search">
              <input
                class="search-input"
                placeholder="输入城市或景点名称"
                value="{{smartPlanData.destination}}"
                bindinput="onDestinationInput"
                bindfocus="onDestinationFocus"
                bindblur="onDestinationBlur"
              />
              <view class="search-icon">🔍</view>
            </view>

            <!-- 搜索建议 -->
            <view wx:if="{{showDestinationSuggestions && destinationSuggestions.length > 0}}" class="suggestions-list">
              <view
                class="suggestion-item"
                wx:for="{{destinationSuggestions}}"
                wx:key="id"
                bindtap="selectDestinationSuggestion"
                data-suggestion="{{item}}"
              >
                <text class="suggestion-name">{{item.name}}</text>
                <text class="suggestion-desc">{{item.district}}</text>
              </view>
            </view>
          </view>

          <!-- 天数 -->
          <view class="field-group">
            <text class="field-label">旅行天数</text>
            <view class="duration-grid">
              <view class="duration-btn {{smartPlanData.duration === 1 ? 'active' : ''}}"
                    data-duration="1" bindtap="selectDuration">1天</view>
              <view class="duration-btn {{smartPlanData.duration === 2 ? 'active' : ''}}"
                    data-duration="2" bindtap="selectDuration">2天</view>
              <view class="duration-btn {{smartPlanData.duration === 3 ? 'active' : ''}}"
                    data-duration="3" bindtap="selectDuration">3天</view>
              <view class="duration-btn {{smartPlanData.duration === 5 ? 'active' : ''}}"
                    data-duration="5" bindtap="selectDuration">5天</view>
              <view class="duration-btn {{smartPlanData.duration === 7 ? 'active' : ''}}"
                    data-duration="7" bindtap="selectDuration">7天</view>
              <view class="duration-btn custom" bindtap="selectCustomDuration">自定义</view>
            </view>
          </view>

          <!-- 偏好 -->
          <view class="field-group">
            <text class="field-label">旅行偏好</text>
            <view class="preference-grid">
              <view class="pref-item {{smartPlanData.preferences.includes('culture') ? 'selected' : ''}}"
                    data-type="culture" bindtap="togglePreference">
                <text class="pref-icon">🏛️</text>
                <text class="pref-name">文化</text>
              </view>
              <view class="pref-item {{smartPlanData.preferences.includes('nature') ? 'selected' : ''}}"
                    data-type="nature" bindtap="togglePreference">
                <text class="pref-icon">🌿</text>
                <text class="pref-name">自然</text>
              </view>
              <view class="pref-item {{smartPlanData.preferences.includes('food') ? 'selected' : ''}}"
                    data-type="food" bindtap="togglePreference">
                <text class="pref-icon">🍜</text>
                <text class="pref-name">美食</text>
              </view>
              <view class="pref-item {{smartPlanData.preferences.includes('shopping') ? 'selected' : ''}}"
                    data-type="shopping" bindtap="togglePreference">
                <text class="pref-icon">🛍️</text>
                <text class="pref-name">购物</text>
              </view>
            </view>
          </view>
        </view>

        <view class="dialog-footer smart-footer">
          <button class="btn-cancel" bindtap="hideSmartPlanForm">取消</button>
          <button class="btn-smart" bindtap="performSmartPlanning"
                  loading="{{smartPlanning}}"
                  disabled="{{!smartPlanData.destination || smartPlanning}}">
            <text class="smart-btn-text">
              {{smartPlanning ? '🎯 AI规划中...' : '🎯 一键规划'}}
            </text>
          </button>
        </view>
      </view>
    </view>

    <!-- 魔法解析弹窗 -->
    <view wx:if="{{showMagicModal}}" class="form-modal magic-modal">
      <view class="modal-mask" bindtap="hideMagicModal"></view>
      <view class="modal-dialog magic-dialog">
        <view class="dialog-header magic-header">
          <view class="magic-title-container">
            <text class="magic-sparkle-icon">✨</text>
            <text class="dialog-title">魔法解析</text>
            <text class="magic-sparkle-icon">✨</text>
          </view>
          <text class="dialog-close" bindtap="hideMagicModal">×</text>
        </view>

        <view class="dialog-body magic-body">
          <!-- 支持平台展示 -->
          <view class="supported-platforms">
            <text class="platforms-title">支持平台</text>
            <view class="platforms-list">
              <view class="platform-item xiaohongshu">
                <text class="platform-icon">📱</text>
                <text class="platform-name">小红书</text>
              </view>
              <view class="platform-item wechat">
                <text class="platform-icon">💬</text>
                <text class="platform-name">微信公众号</text>
              </view>
              <view class="platform-item weibo">
                <text class="platform-icon">🐦</text>
                <text class="platform-name">微博</text>
              </view>
              <view class="platform-item dianping">
                <text class="platform-icon">🍽️</text>
                <text class="platform-name">大众点评</text>
              </view>
            </view>
          </view>

          <!-- 链接输入区域 -->
          <view class="magic-input-section">
            <text class="field-label">粘贴攻略链接</text>
            <view class="magic-input-container">
              <textarea
                class="magic-input"
                placeholder="粘贴小红书、微信公众号等平台的攻略链接&#10;例如：https://www.xiaohongshu.com/explore/..."
                value="{{magicUrl}}"
                bindinput="onMagicUrlInput"
                auto-height
                maxlength="500"
              />
              <view class="input-actions">
                <text class="paste-btn" bindtap="pasteFromClipboard">📋 粘贴</text>
                <text class="clear-btn" bindtap="clearMagicUrl" wx:if="{{magicUrl}}">🗑️</text>
              </view>
            </view>
          </view>

          <!-- 解析结果预览 -->
          <view wx:if="{{magicResult}}" class="magic-result-preview">
            <view class="result-header">
              <text class="result-title">解析结果</text>
              <text class="result-platform">{{magicResult.platform}}</text>
            </view>
            <view class="result-content">
              <text class="result-text">{{magicResult.title}}</text>
              <text class="result-locations">发现 {{magicResult.locations.length}} 个地点</text>
            </view>
          </view>
        </view>

        <view class="dialog-footer magic-footer">
          <button class="btn-cancel" bindtap="hideMagicModal">取消</button>
          <button class="btn-magic" bindtap="performMagicParse"
                  loading="{{magicParsing}}"
                  disabled="{{!magicUrl || magicParsing}}">
            <text class="magic-btn-text">
              {{magicParsing ? '解析中...' : '✨ 开始魔法解析'}}
            </text>
          </button>
        </view>
      </view>
    </view>

    <!-- 规划结果 -->
    <view wx:if="{{currentStep === 0 && smartPlanResult}}" class="result-container">
      <view class="result-status">
        <text class="status-icon">✅</text>
        <text class="status-text">规划完成</text>
      </view>

      <view class="result-content">
        <!-- 地图 -->
        <view class="map-section" bindtap="viewFullMap">
          <map
            id="result-map"
            class="result-map"
            latitude="{{smartPlanResult.mapData.center.latitude}}"
            longitude="{{smartPlanResult.mapData.center.longitude}}"
            scale="{{smartPlanResult.mapData.scale}}"
            markers="{{smartPlanResult.mapData.markers}}"
            polyline="{{smartPlanResult.mapData.polyline}}"
            show-location="{{false}}"
          />
          <view class="map-tip">
            <text>点击查看详细地图</text>
          </view>
        </view>

        <!-- 行程信息 -->
        <view class="trip-info">
          <text class="trip-title">{{smartPlanResult.title}}</text>
          <view class="trip-stats">
            <text class="stat-item">{{smartPlanResult.duration}}天</text>
            <text class="stat-item">{{smartPlanResult.totalDistance}}km</text>
            <text class="stat-item">¥{{smartPlanResult.estimatedCost.total}}</text>
          </view>
        </view>

        <!-- 每日安排 -->
        <view class="daily-plan">
          <scroll-view class="plan-scroll" scroll-x>
            <view class="day-item" wx:for="{{smartPlanResult.dailyPlan}}" wx:key="day">
              <text class="day-title">第{{item.day}}天</text>
              <view class="day-spots">
                <text class="spot-name" wx:for="{{item.attractions}}" wx:key="id" wx:for-item="spot">
                  {{spot.name}}
                </text>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>

      <view class="result-actions">
        <button class="btn-secondary" bindtap="editSmartPlan">调整</button>
        <button class="btn-primary" bindtap="applySmartPlan">创建计划</button>
      </view>
    </view>

    <!-- 简化的进度指示器 -->
    <view class="progress-indicator" wx:if="{{currentStep > 0}}">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{(currentStep / 3) * 100}}%"></view>
      </view>
      <text class="progress-text">第 {{currentStep}} 步，共 3 步</text>
    </view>

    <!-- 表单内容 -->
    <form bindsubmit="savePlan" class="plan-form">

      <!-- 步骤1：基本信息 -->
      <view wx:if="{{currentStep === 1}}" class="form-step">
        <view class="glass-card">
          <view class="card-header">
            <custom-icon name="edit" size="30" color="#4ECDC4" />
            <text class="card-title">基本信息</text>
          </view>

          <view class="form-content">
            <!-- 旅行标题 -->
            <view class="form-item">
              <view class="item-label">
                <text class="label-text">旅行标题</text>
                <text class="required-star">*</text>
              </view>
              <view class="input-container">
                <input
                  class="text-input"
                  placeholder="给你的旅行起个名字吧"
                  value="{{formData.title}}"
                  bindinput="onTitleInput"
                  maxlength="30"
                />
                <view class="char-counter">{{formData.title.length}}/30</view>
              </view>
            </view>

            <!-- 目的地选择 -->
            <view class="form-item">
              <view class="item-label">
                <text class="label-text">目的地</text>
                <text class="required-star">*</text>
              </view>
              <view class="selector-container" bindtap="selectDestination">
                <view class="selector-display">
                  <custom-icon name="map-pin" size="26" color="#4ECDC4" />
                  <text class="selector-text {{!formData.destination ? 'placeholder' : ''}}">
                    {{formData.destination || '点击选择目的地'}}
                  </text>
                  <custom-icon name="arrow-right" size="22" color="#b2bec3" />
                </view>
              </view>
            </view>

            <!-- 参与人数 - 使用微信官方picker -->
            <view class="form-item">
              <view class="item-label">
                <text class="label-text">参与人数</text>
              </view>
              <picker
                mode="selector"
                range="{{participantOptions}}"
                value="{{participantIndex}}"
                bindchange="onParticipantChange"
              >
                <view class="selector-container">
                  <view class="selector-display">
                    <custom-icon name="user" size="26" color="#4ECDC4" />
                    <text class="selector-text">{{participantOptions[participantIndex]}}</text>
                    <custom-icon name="arrow-right" size="22" color="#b2bec3" />
                  </view>
                </view>
              </picker>
            </view>

            <!-- {{ AURA-X: Modify - 将开关改为按钮，点击直接显示邀请弹窗. Approval: 寸止(ID:1738056000). }} -->
            <!-- 邀请好友共建 -->
            <view class="form-item invite-section">
              <view class="invite-header">
                <view class="invite-label">
                  <custom-icon name="heart" size="24" color="#ff7875" />
                  <text class="label-text">邀请好友共建</text>
                </view>
                <button
                  class="invite-btn"
                  bindtap="showInviteOption"
                  size="mini">
                  <custom-icon name="friends" size="16" color="#4ECDC4" />
                  <text>邀请协作</text>
                </button>
              </view>
              <view class="invite-description">
                <text class="description-text">创建计划后可以邀请好友一起规划旅行，共同编辑行程和记录</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 步骤2：时间和预算 -->
      <view wx:if="{{currentStep === 2}}" class="form-step">
        <!-- 时间安排卡片 -->
        <view class="glass-card">
          <view class="card-header">
            <custom-icon name="calendar" size="30" color="#FF6B6B" />
            <text class="card-title">时间安排</text>
          </view>

          <view class="form-content">
            <!-- 日期选择区域 -->
            <view class="date-selection">
              <!-- 开始日期 -->
              <view class="form-item">
                <view class="item-label">
                  <text class="label-text">开始日期</text>
                  <text class="required-star">*</text>
                </view>
                <picker
                  mode="date"
                  value="{{formData.startDate}}"
                  start="{{today}}"
                  bindchange="onStartDateChange"
                >
                  <view class="selector-container">
                    <view class="selector-display">
                      <custom-icon name="calendar" size="26" color="#4ECDC4" />
                      <text class="selector-text {{!formData.startDate ? 'placeholder' : ''}}">
                        {{formData.startDate || '选择开始日期'}}
                      </text>
                      <custom-icon name="arrow-right" size="22" color="#b2bec3" />
                    </view>
                  </view>
                </picker>
              </view>

              <!-- 结束日期 -->
              <view class="form-item">
                <view class="item-label">
                  <text class="label-text">结束日期</text>
                  <text class="required-star">*</text>
                </view>
                <picker
                  mode="date"
                  value="{{formData.endDate}}"
                  start="{{formData.startDate || today}}"
                  bindchange="onEndDateChange"
                >
                  <view class="selector-container">
                    <view class="selector-display">
                      <custom-icon name="calendar" size="26" color="#4ECDC4" />
                      <text class="selector-text {{!formData.endDate ? 'placeholder' : ''}}">
                        {{formData.endDate || '选择结束日期'}}
                      </text>
                      <custom-icon name="arrow-right" size="22" color="#b2bec3" />
                    </view>
                  </view>
                </picker>
              </view>
            </view>

            <!-- 旅行天数显示 -->
            <view wx:if="{{duration > 0}}" class="duration-highlight">
              <view class="duration-icon">
                <custom-icon name="clock" size="26" color="#52c41a" />
              </view>
              <view class="duration-info">
                <text class="duration-number">{{duration}}</text>
                <text class="duration-unit">天精彩旅程</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 预算设置卡片 -->
        <view class="glass-card">
          <view class="card-header">
            <custom-icon name="wallet" size="30" color="#faad14" />
            <text class="card-title">预算设置</text>
          </view>

          <view class="form-content">
            <!-- 总预算 -->
            <view class="form-item">
              <view class="item-label">
                <text class="label-text">总预算</text>
                <text class="optional-tag">选填</text>
              </view>
              <view class="budget-input-container">
                <text class="currency-symbol">¥</text>
                <input
                  class="budget-input"
                  type="digit"
                  placeholder="设置旅行预算"
                  value="{{formData.budget}}"
                  bindinput="onBudgetInput"
                />
              </view>
            </view>

            <!-- 备注说明 -->
            <view class="form-item">
              <view class="item-label">
                <text class="label-text">备注说明</text>
                <text class="optional-tag">选填</text>
              </view>
              <view class="textarea-container">
                <textarea
                  class="notes-textarea"
                  placeholder="添加一些备注信息，比如特殊需求、注意事项等"
                  value="{{formData.notes}}"
                  bindinput="onNotesInput"
                  maxlength="200"
                  auto-height
                />
                <view class="char-counter">{{formData.notes.length}}/200</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 步骤3：确认创建 -->
      <view wx:if="{{currentStep === 3}}" class="form-step">
        <view class="glass-card">
          <view class="card-header">
            <custom-icon name="check" size="30" color="#52c41a" />
            <text class="card-title">确认信息</text>
          </view>

          <view class="form-content">
            <!-- 计划预览卡片 -->
            <view class="plan-preview">
              <view class="preview-header">
                <view class="plan-title">{{formData.title}}</view>
                <view class="plan-destination">
                  <custom-icon name="map-pin" size="22" color="#4ECDC4" />
                  <text>{{formData.destination}}</text>
                </view>
              </view>

              <view class="preview-details">
                <view class="detail-row">
                  <view class="detail-item">
                    <custom-icon name="calendar" size="22" color="#FF6B6B" />
                    <view class="detail-text">
                      <text class="detail-label">出行时间</text>
                      <text class="detail-value">{{formData.startDate}} 至 {{formData.endDate}}</text>
                    </view>
                  </view>
                </view>

                <view class="detail-row">
                  <view class="detail-item">
                    <custom-icon name="clock" size="22" color="#52c41a" />
                    <view class="detail-text">
                      <text class="detail-label">旅行天数</text>
                      <text class="detail-value">{{duration}} 天</text>
                    </view>
                  </view>

                  <view class="detail-item">
                    <custom-icon name="user" size="22" color="#4ECDC4" />
                    <view class="detail-text">
                      <text class="detail-label">参与人数</text>
                      <text class="detail-value">{{participantOptions[participantIndex]}}</text>
                    </view>
                  </view>
                </view>

                <view wx:if="{{formData.budget > 0}}" class="detail-row">
                  <view class="detail-item">
                    <custom-icon name="wallet" size="22" color="#faad14" />
                    <view class="detail-text">
                      <text class="detail-label">预算</text>
                      <text class="detail-value">¥{{formData.budget}}</text>
                    </view>
                  </view>
                </view>

                <view wx:if="{{formData.notes}}" class="detail-row full-width">
                  <view class="detail-item">
                    <custom-icon name="edit" size="22" color="#636e72" />
                    <view class="detail-text">
                      <text class="detail-label">备注</text>
                      <text class="detail-value notes">{{formData.notes}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <view class="action-buttons">
        <button wx:if="{{currentStep > 1}}" class="btn-secondary" bindtap="prevStep">
          <custom-icon name="arrow-left" size="22" color="#636e72" />
          <text>上一步</text>
        </button>

        <button wx:if="{{currentStep < 3}}" class="btn-primary" bindtap="nextStep" disabled="{{!canNextStep}}">
          <text>下一步</text>
          <custom-icon name="arrow-right" size="22" color="#fff" />
        </button>

        <button wx:if="{{currentStep === 3}}" class="btn-primary create-btn" form-type="submit" disabled="{{saving}}" loading="{{saving}}">
          <custom-icon name="check" size="22" color="#fff" wx:if="{{!saving}}" />
          <text>{{saving ? '创建中...' : '创建计划'}}</text>
        </button>
      </view>

    </form>

    <!-- 底部安全距离 -->
    <view class="safe-area-bottom"></view>

  </scroll-view>

  <!-- 加载遮罩 -->
  <view wx:if="{{loading}}" class="loading-mask">
    <view class="loading-container">
      <view class="loading-spinner">
        <view class="spinner-dot"></view>
        <view class="spinner-dot"></view>
        <view class="spinner-dot"></view>
      </view>
      <text class="loading-text">{{loadingText}}</text>
    </view>
  </view>

  <!-- Toast提示 -->
  <van-toast id="van-toast" />

</view>