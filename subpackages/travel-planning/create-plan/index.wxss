/* 创建旅行计划页面样式 - 革新设计 */

/* 页面容器 */
.create-plan-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 创建方式选择 */
.creation-selector {
  padding: 60rpx 40rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

.selector-header {
  text-align: center;
  margin-bottom: 80rpx;
}

.page-title {
  font-size: 56rpx;
  font-weight: 700;
  color: white;
  text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}

.options-container {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.option-card {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.15);
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  transition: all 0.3s ease;
}

.option-card:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.15);
}

.option-card.recommended {
  border-color: var(--brand-primary);
  background: rgba(255, 107, 107, 0.1);
}

.card-badge {
  position: absolute;
  top: -8rpx;
  right: 24rpx;
  background: var(--brand-primary);
  color: white;
  font-size: 20rpx;
  font-weight: 600;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
}

.card-icon {
  font-size: 48rpx;
  text-align: center;
  margin-bottom: 24rpx;
}

.card-content {
  text-align: center;
}

.card-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 12rpx;
}

.card-desc {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* 魔法解析卡片特殊样式 */
.option-card.magic-card {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.15) 0%, rgba(78, 205, 196, 0.15) 100%);
  border: 2rpx solid transparent;
  background-clip: padding-box;
  overflow: hidden;
}

.option-card.magic-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #FF6B6B, #4ECDC4, #45B7D1);
  border-radius: 24rpx;
  padding: 2rpx;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  z-index: -1;
}

.magic-badge {
  background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
  animation: magic-glow 2s ease-in-out infinite alternate;
}

@keyframes magic-glow {
  0% { box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4); }
  100% { box-shadow: 0 4rpx 20rpx rgba(78, 205, 196, 0.6); }
}

.magic-icon {
  animation: magic-bounce 1.5s ease-in-out infinite;
}

@keyframes magic-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8rpx); }
}

.magic-sparkle {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  display: flex;
  gap: 8rpx;
}

.sparkle {
  font-size: 20rpx;
  animation: sparkle-twinkle 1s ease-in-out infinite;
}

.sparkle:nth-child(2) {
  animation-delay: 0.3s;
}

.sparkle:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes sparkle-twinkle {
  0%, 100% { opacity: 0.3; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.2); }
}

/* 表单弹窗 */
.form-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  box-sizing: border-box;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
}

.modal-dialog {
  position: relative;
  width: 100%;
  max-width: 560rpx;
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.dialog-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #999;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.dialog-close:active {
  background: #f5f5f5;
}

.dialog-body {
  padding: 32rpx;
}

.field-group {
  margin-bottom: 32rpx;
}

.field-group:last-child {
  margin-bottom: 0;
}

.field-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.field-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.field-input:active {
  background: #e9ecef;
}

.input-value {
  font-size: 28rpx;
  color: #333;
}

.input-value.placeholder {
  color: #999;
}

.input-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 目的地搜索 */
.destination-search {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 24rpx 60rpx 24rpx 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  border: none;
  outline: none;
}

.search-input:focus {
  background: #e9ecef;
}

.search-icon {
  position: absolute;
  right: 24rpx;
  font-size: 24rpx;
  color: #999;
  pointer-events: none;
}

/* 搜索建议 */
.suggestions-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  z-index: 10;
  max-height: 400rpx;
  overflow-y: auto;
}

.suggestion-item {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background: #f8f9fa;
}

.suggestion-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.suggestion-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.duration-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.duration-btn {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  text-align: center;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.duration-btn:active {
  transform: scale(0.98);
}

.duration-btn.active {
  background: var(--brand-primary);
  color: white;
}

.duration-btn.custom {
  border: 2rpx dashed #ddd;
  background: transparent;
}

.preference-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.pref-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.pref-item:active {
  transform: scale(0.98);
}

.pref-item.selected {
  background: rgba(255, 107, 107, 0.1);
  border: 2rpx solid var(--brand-primary);
}

.pref-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.pref-name {
  font-size: 24rpx;
  color: #666;
}

.pref-item.selected .pref-name {
  color: var(--brand-primary);
  font-weight: 600;
}

.dialog-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.btn-cancel,
.btn-confirm {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
}

.btn-cancel {
  background: #f8f9fa;
  color: #666;
}

.btn-cancel:active {
  background: #e9ecef;
}

.btn-confirm {
  background: var(--brand-primary);
  color: white;
}

.btn-confirm:active {
  background: #FF8E8E;
}

.btn-confirm:disabled {
  opacity: 0.6;
}

/* 魔法解析弹窗样式 */
.magic-modal .modal-dialog {
  max-width: 640rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
}

.magic-header {
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
  color: white;
  border-bottom: none;
}

.magic-title-container {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.magic-sparkle-icon {
  font-size: 24rpx;
  animation: sparkle-rotate 2s linear infinite;
}

@keyframes sparkle-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.magic-header .dialog-title {
  color: white;
  font-weight: 700;
}

.magic-header .dialog-close {
  color: rgba(255, 255, 255, 0.8);
}

.magic-header .dialog-close:active {
  background: rgba(255, 255, 255, 0.1);
}

.magic-body {
  padding: 40rpx 32rpx;
}

/* 支持平台展示 */
.supported-platforms {
  margin-bottom: 32rpx;
}

.platforms-title {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
  text-align: center;
}

.platforms-list {
  display: flex;
  justify-content: space-around;
  gap: 16rpx;
}

.platform-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.6);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  flex: 1;
  transition: all 0.3s ease;
}

.platform-item.xiaohongshu {
  background: rgba(255, 107, 107, 0.1);
}

.platform-item.wechat {
  background: rgba(78, 205, 196, 0.1);
}

.platform-item.weibo {
  background: rgba(69, 183, 209, 0.1);
}

.platform-item.dianping {
  background: rgba(255, 193, 7, 0.1);
}

.platform-icon {
  font-size: 32rpx;
}

.platform-name {
  font-size: 20rpx;
  color: #666;
  text-align: center;
}

/* 魔法输入区域 */
.magic-input-section {
  margin-bottom: 32rpx;
}

.magic-input-container {
  position: relative;
  background: white;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
  overflow: hidden;
  transition: all 0.3s ease;
}

.magic-input-container:focus-within {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 4rpx rgba(255, 107, 107, 0.1);
}

.magic-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-top: 1rpx solid #e9ecef;
}

.paste-btn,
.clear-btn {
  font-size: 24rpx;
  color: var(--brand-primary);
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background: rgba(255, 107, 107, 0.1);
  transition: all 0.3s ease;
}

.paste-btn:active,
.clear-btn:active {
  background: rgba(255, 107, 107, 0.2);
  transform: scale(0.95);
}

/* 魔法解析结果预览 */
.magic-result-preview {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.05) 0%, rgba(78, 205, 196, 0.05) 100%);
  border: 1rpx solid rgba(255, 107, 107, 0.2);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
  animation: magic-result-appear 0.5s ease-out;
}

@keyframes magic-result-appear {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.result-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.result-platform {
  font-size: 20rpx;
  color: var(--brand-primary);
  background: rgba(255, 107, 107, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.result-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.result-locations {
  font-size: 24rpx;
  color: var(--brand-primary);
  font-weight: 500;
}

/* 魔法按钮样式 */
.magic-footer {
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

.btn-magic {
  flex: 1;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 28rpx;
  font-size: 28rpx;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-magic::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.btn-magic:active::before {
  left: 100%;
}

.btn-magic:active {
  transform: scale(0.98);
}

.btn-magic:disabled {
  opacity: 0.7;
  transform: none;
}

.magic-btn-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

/* 智能规划弹窗样式 */
.smart-header {
  background: linear-gradient(135deg, #4ECDC4 0%, #45B7D1 100%);
  color: white;
}

.smart-title-container {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.smart-icon {
  font-size: 24rpx;
  animation: smart-pulse 2s ease-in-out infinite;
}

@keyframes smart-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.smart-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 智能规划按钮样式 */
.smart-footer {
  background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);
}

.btn-smart {
  flex: 1;
  background: linear-gradient(135deg, #4ECDC4 0%, #45B7D1 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 28rpx;
  font-size: 28rpx;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-smart::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.btn-smart:active::before {
  left: 100%;
}

.btn-smart:active {
  transform: scale(0.98);
}

.btn-smart:disabled {
  opacity: 0.7;
  transform: none;
}

.smart-btn-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

/* 规划结果 */
.result-container {
  padding: 40rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

.result-status {
  text-align: center;
  margin-bottom: 40rpx;
}

.status-icon {
  font-size: 48rpx;
  display: block;
  margin-bottom: 16rpx;
}

.status-text {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
}

.result-content {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.map-section {
  position: relative;
  height: 300rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
}

.result-map {
  width: 100%;
  height: 100%;
}

.map-tip {
  position: absolute;
  bottom: 16rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
}

.trip-info {
  text-align: center;
  margin-bottom: 32rpx;
}

.trip-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 16rpx;
}

.trip-stats {
  display: flex;
  justify-content: center;
  gap: 24rpx;
}

.stat-item {
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

.daily-plan {
  margin-bottom: 32rpx;
}

.plan-scroll {
  white-space: nowrap;
}

.day-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 16rpx;
  padding: 24rpx 16rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 16rpx;
  vertical-align: top;
}

.day-title {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: var(--brand-accent);
  margin-bottom: 16rpx;
  text-align: center;
}

.day-spots {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.spot-name {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.result-actions {
  display: flex;
  gap: 16rpx;
}

.btn-secondary,
.btn-primary {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:active {
  background: rgba(255, 255, 255, 0.15);
}

.btn-primary {
  background: var(--brand-primary);
  color: white;
}

.btn-primary:active {
  background: #FF8E8E;
}

/* 背景装饰 */
.smart-bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.smart-orb {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(30rpx);
  animation: smartFloat 8s ease-in-out infinite;
}

.smart-orb-1 {
  width: 300rpx;
  height: 300rpx;
  top: 5%;
  right: -10%;
  animation-delay: 0s;
}

.smart-orb-2 {
  width: 200rpx;
  height: 200rpx;
  bottom: 10%;
  left: -5%;
  animation-delay: 3s;
}

.smart-orb-3 {
  width: 150rpx;
  height: 150rpx;
  top: 40%;
  right: 10%;
  animation-delay: 6s;
}

@keyframes smartFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-30rpx) rotate(120deg); }
  66% { transform: translateY(-15rpx) rotate(240deg); }
}

.smart-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: particleFloat 4s infinite;
}

.particle-1 { top: 20%; left: 10%; animation-delay: 0s; }
.particle-2 { top: 60%; right: 20%; animation-delay: 1s; }
.particle-3 { bottom: 30%; left: 30%; animation-delay: 2s; }
.particle-4 { top: 80%; right: 40%; animation-delay: 3s; }
.particle-5 { top: 40%; left: 60%; animation-delay: 4s; }

@keyframes particleFloat {
  0%, 100% {
    opacity: 0;
    transform: translateY(0px) scale(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-40rpx) scale(1);
  }
}

/* 主标题区域 */
.hero-section {
  position: relative;
  z-index: 1;
  text-align: center;
  padding: 80rpx 32rpx 60rpx;
}

.hero-icon-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 40rpx;
}

.hero-icon-bg {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.icon-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: iconGlow 3s ease-in-out infinite;
}

@keyframes iconGlow {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

.hero-icon {
  font-size: 60rpx;
  z-index: 1;
  animation: iconBounce 4s ease-in-out infinite;
}

@keyframes iconBounce {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-8rpx) rotate(5deg); }
  75% { transform: translateY(-4rpx) rotate(-3deg); }
}

.pulse-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.pulse-ring {
  position: absolute;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulseRing 3s infinite;
}

.ring-1 {
  width: 140rpx;
  height: 140rpx;
  margin: -70rpx 0 0 -70rpx;
  animation-delay: 0s;
}

.ring-2 {
  width: 180rpx;
  height: 180rpx;
  margin: -90rpx 0 0 -90rpx;
  animation-delay: 1s;
}

.ring-3 {
  width: 220rpx;
  height: 220rpx;
  margin: -110rpx 0 0 -110rpx;
  animation-delay: 2s;
}

@keyframes pulseRing {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.hero-text {
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: 64rpx;
  font-weight: 800;
  color: white;
  text-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.4);
  letter-spacing: 3rpx;
  margin-bottom: 16rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.3);
  font-weight: 400;
  margin-bottom: 32rpx;
  line-height: 1.4;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: 16rpx;
  flex-wrap: wrap;
}

.feature-tag {
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  font-weight: 500;
}

/* 智能表单容器 */
.smart-form-container {
  position: relative;
  z-index: 1;
  padding: 0 32rpx 40rpx;
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.form-card {
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(30rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  padding: 40rpx 32rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

.form-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.5) 50%, transparent 100%);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.header-icon {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.15);
}

.header-text {
  flex: 1;
}

.header-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 6rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.header-subtitle {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.3;
}

.smart-form-item {
  margin-bottom: var(--space-lg);
}

.smart-form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-bottom: var(--space-md);
}

/* 目的地选择器 */
.destination-selector {
  background: rgba(255, 255, 255, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  transition: all 0.3s ease;
}

.destination-selector:active {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.selector-content {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.destination-icon {
  font-size: 32rpx;
}

.destination-text {
  flex: 1;
  font-size: 28rpx;
  color: white;
}

.destination-text.placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.selector-arrow {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.4);
}

/* 天数选择器 */
.duration-options {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
}

.duration-option {
  flex: 1;
  min-width: 100rpx;
  padding: var(--space-md) var(--space-sm);
  background: rgba(255, 255, 255, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.duration-option:active {
  transform: scale(0.98);
}

.duration-option.active {
  background: var(--brand-accent);
  border-color: var(--brand-accent);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(69, 183, 209, 0.3);
}

.duration-option.custom {
  background: rgba(255, 255, 255, 0.1);
  border-style: dashed;
}

/* 偏好网格 */
.preference-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-md);
}

.preference-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-lg) var(--space-md);
  background: rgba(255, 255, 255, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  text-align: center;
}

.preference-card:active {
  transform: scale(0.98);
}

.preference-card.active {
  background: rgba(255, 107, 107, 0.2);
  border-color: var(--brand-primary);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.preference-icon {
  font-size: 40rpx;
  margin-bottom: var(--space-xs);
}

.preference-name {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.preference-card.active .preference-name {
  color: white;
}

/* 智能规划按钮 */
.smart-actions {
  margin-top: var(--space-lg);
}

.smart-plan-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-lg);
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.4);
  transition: all 0.3s ease;
}

.smart-plan-button:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.6);
}

.smart-plan-button:disabled {
  opacity: 0.6;
  transform: none;
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-size: 32rpx;
}

/* 智能规划结果区域 */
.smart-result-section {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20rpx) saturate(180%);
  border: 1rpx solid rgba(82, 196, 26, 0.3);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
}

.result-header {
  text-align: center;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.result-icon {
  font-size: 48rpx;
  display: block;
  margin-bottom: var(--space-sm);
}

.result-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: var(--space-xs);
}

.result-subtitle {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 地图预览 */
.map-preview-container {
  position: relative;
  height: 300rpx;
  border-radius: var(--radius-lg);
  overflow: hidden;
  margin-bottom: var(--space-lg);
}

.preview-map {
  width: 100%;
  height: 100%;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  transition: all 0.3s ease;
}

.map-overlay:active {
  background: rgba(0, 0, 0, 0.5);
}

.overlay-text {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

.overlay-icon {
  font-size: 32rpx;
}

/* 行程概览 */
.itinerary-summary {
  margin-bottom: var(--space-lg);
}

.summary-header {
  margin-bottom: var(--space-md);
}

.summary-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-bottom: var(--space-sm);
}

.summary-tags {
  display: flex;
  gap: var(--space-sm);
}

.tag {
  padding: var(--space-xs) var(--space-sm);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-sm);
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 每日预览滚动 */
.daily-scroll {
  white-space: nowrap;
}

.day-preview {
  display: inline-block;
  width: 200rpx;
  margin-right: var(--space-md);
  padding: var(--space-md);
  background: rgba(255, 255, 255, 0.06);
  border-radius: var(--radius-md);
  vertical-align: top;
}

.day-number {
  font-size: 24rpx;
  font-weight: 600;
  color: var(--brand-accent);
  margin-bottom: var(--space-sm);
  text-align: center;
}

.day-attractions {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.attraction-dot {
  padding: var(--space-xs);
  background: rgba(255, 255, 255, 0.08);
  border-radius: var(--radius-xs);
}

.dot-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 结果操作按钮 */
.result-actions {
  display: flex;
  gap: var(--space-md);
}

.action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  padding: var(--space-lg) var(--space-md);
  border-radius: var(--radius-md);
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.action-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.action-button.secondary:active {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(0.98);
}

.action-button.primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.action-button.primary:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(255, 107, 107, 0.4);
}

/* 传统创建入口 */
.traditional-entry {
  margin-top: var(--space-xl);
}

.entry-divider {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.divider-line {
  flex: 1;
  height: 1rpx;
  background: rgba(255, 255, 255, 0.2);
}

.divider-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.5);
  padding: 0 var(--space-sm);
}

.traditional-button {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-xl);
  background: rgba(255, 255, 255, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
}

.traditional-button:active {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(0.98);
}

.traditional-button .btn-icon {
  font-size: 40rpx;
}

.traditional-button .btn-text {
  font-size: 28rpx;
  font-weight: 500;
  color: white;
}

.btn-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 渐变背景 */
.gradient-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  z-index: -1;
}

/* 内容滚动区域 */
.content-scroll {
  height: 100vh;
  padding: 0 24rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  padding: 88rpx 0 48rpx;
  text-align: center;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.header-icon {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
}

.header-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.header-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

.weui-label_optional {
  margin-left: 8rpx;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  backdrop-filter: blur(10rpx);
}

.weui-input {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
}

.weui-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.weui-form__title {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 600;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
  margin: 48rpx 24rpx 16rpx;
  font-size: 32rpx;
}

.weui-form__desc {
  color: rgba(255, 255, 255, 0.7);
  margin: 0 24rpx 16rpx;
  font-size: 24rpx;
}

.weui-btn-area {
  margin: 32rpx 24rpx;
  display: flex;
  gap: 16rpx;
}

.weui-btn {
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.weui-btn_primary {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.3);
}

.weui-btn_default {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 背景装饰 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(40rpx);
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.orb-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.orb-3 {
  width: 120rpx;
  height: 120rpx;
  top: 80%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 内容滚动区域 */
.content-scroll {
  height: 100vh;
  position: relative;
  z-index: 1;
}

/* 页面头部 */
.page-header {
  padding: 40rpx 48rpx 32rpx;
  text-align: center;
}

.header-icon {
  margin-bottom: 16rpx;
}

.header-title {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  margin-bottom: 8rpx;
  letter-spacing: 2rpx;
}

.header-subtitle {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 表单卡片 */
.form-card {
  margin: 0 24rpx 120rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  box-shadow:
    0 16rpx 64rpx rgba(0, 0, 0, 0.1),
    0 2rpx 0 rgba(255, 255, 255, 0.3) inset,
    0 -2rpx 0 rgba(0, 0, 0, 0.1) inset;
}

/* 表单分组 */
.form-section {
  margin-bottom: 48rpx;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.section-title text {
  margin-left: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 表单项 */
.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.item-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 26rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

.required-mark {
  margin-left: 8rpx;
  width: 12rpx;
  height: 12rpx;
  background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
  border-radius: 50%;
  font-size: 0;
  box-shadow: 0 2rpx 4rpx rgba(255, 107, 107, 0.3);
}

.optional-mark {
  margin-left: 8rpx;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  backdrop-filter: blur(10rpx);
}

/* 输入框样式 */
.form-input {
  width: 100%;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #2d3436;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.05),
    0 1rpx 0 rgba(255, 255, 255, 0.5) inset;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #4ECDC4;
  box-shadow:
    0 4rpx 16rpx rgba(78, 205, 196, 0.2),
    0 1rpx 0 rgba(255, 255, 255, 0.5) inset;
}

.form-input::placeholder {
  color: #b2bec3;
  font-style: italic;
}

.input-counter {
  text-align: right;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 8rpx;
}

/* 目的地选择器 - WeUI适配 */
.destination-selector {
  width: 100%;
}

.destination-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.destination-display:active {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(0.98);
}

.destination-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  flex: 1;
}

.destination-text:empty::before {
  content: '点击选择目的地';
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

/* WeUI按钮样式覆盖 */
.destination-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: rgba(87, 107, 149, 0.2) !important;
  border: 1rpx solid rgba(87, 107, 149, 0.3) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

.destination-btn:active {
  background: rgba(87, 107, 149, 0.3) !important;
}

/* 预算输入样式 */
.budget-input-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.currency-symbol {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  margin-right: 8rpx;
}

.budget-input {
  flex: 1;
}

/* 预算分配区域 */
.budget-allocation-section {
  margin-top: 32rpx;
}

.budget-item-wrapper {
  margin-bottom: 16rpx;
}

.budget-item-title {
  width: 100%;
}

.item-name-input {
  width: 100%;
}

.budget-item-footer {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.budget-amount-input {
  display: flex;
  align-items: center;
  flex: 1;
}

.amount-input {
  flex: 1;
}

.remove-btn {
  width: 48rpx !important;
  height: 48rpx !important;
  min-width: 48rpx !important;
  padding: 0 !important;
  background: rgba(255, 77, 79, 0.1) !important;
  border: 1rpx solid rgba(255, 77, 79, 0.2) !important;
}

.add-budget-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: rgba(87, 107, 149, 0.1) !important;
  border: 1rpx dashed rgba(87, 107, 149, 0.3) !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

/* 预算统计 */
.budget-summary {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1rpx solid rgba(255, 255, 255, 0.1) !important;
}

.summary-amount {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.summary-amount.negative {
  color: #ff4d4f;
}

/* 参与人员步进器 */
.participant-stepper {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.participant-unit {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 时间显示 */
.duration-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.duration-text {
  font-size: 24rpx;
  color: #52c41a;
  font-weight: 500;
}

/* 备注文本域 */
.notes-textarea {
  width: 100%;
  min-height: 120rpx;
}



/* 日期选择器 */
.date-selector {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.date-item {
  flex: 1;
}

.date-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 0 24rpx;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.05),
    0 1rpx 0 rgba(255, 255, 255, 0.5) inset;
}

.date-text {
  font-size: 28rpx;
  color: #2d3436;
}

.date-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
}

.duration-display {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
  padding: 12rpx 20rpx;
  background: rgba(82, 196, 26, 0.1);
  border: 1rpx solid rgba(82, 196, 26, 0.2);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.duration-display text {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #52c41a;
  font-weight: 500;
}

/* 预算输入 */
.budget-input {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 0 24rpx;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.05),
    0 1rpx 0 rgba(255, 255, 255, 0.5) inset;
}

.currency-symbol {
  font-size: 28rpx;
  color: #45B7D1;
  font-weight: 600;
  margin-right: 8rpx;
}

.budget-number {
  flex: 1;
  border: none;
  background: transparent;
  padding: 0;
  height: 88rpx;
  line-height: 88rpx;
}

/* 自定义预算分配 */
.budget-allocation {
  margin-top: 24rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

.allocation-title {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 16rpx;
  font-weight: 500;
}

.allocation-items {
  margin-bottom: 16rpx;
}

.allocation-item {
  margin-bottom: 16rpx;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.item-name-input {
  flex: 1;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 24rpx;
  color: #2d3436;
}

.remove-item-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 77, 79, 0.1);
  border: 1rpx solid rgba(255, 77, 79, 0.2);
  border-radius: 50%;
  margin-left: 12rpx;
  padding: 0;
  font-size: 0;
}

.item-amount {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 8rpx;
  padding: 0 16rpx;
}

.amount-input {
  flex: 1;
  height: 60rpx;
  border: none;
  background: transparent;
  font-size: 24rpx;
  color: #2d3436;
  padding: 0;
}

.add-budget-item-btn {
  width: 100%;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: rgba(69, 183, 209, 0.1);
  border: 1rpx dashed rgba(69, 183, 209, 0.3);
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #45B7D1;
  font-weight: 500;
  margin: 0 0 16rpx 0;
  padding: 0;
}

.budget-summary {
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.summary-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.summary-amount {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

.summary-amount.negative {
  color: #ff4d4f;
}

/* 参与人员选择器 */
.participant-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.participant-counter {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.counter-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  padding: 0;
  margin: 0;
  font-size: 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.counter-btn:not([disabled]):active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.counter-btn[disabled] {
  opacity: 0.5;
}

.counter-number {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
  min-width: 60rpx;
  text-align: center;
}

.participant-unit {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}



/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  font-size: 26rpx;
  color: #2d3436;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.05),
    0 1rpx 0 rgba(255, 255, 255, 0.5) inset;
}

.textarea-counter {
  text-align: right;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 8rpx;
}

/* WeUI底部按钮区域 */
.cancel-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.2) !important;
  color: rgba(255, 255, 255, 0.8) !important;
  border: 1rpx solid rgba(255, 255, 255, 0.3) !important;
}

.save-btn {
  flex: 2;
  background: linear-gradient(135deg, #52c41a, #73d13d) !important;
  border: none !important;
  box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.3);
}

.save-btn[disabled] {
  background: rgba(255, 255, 255, 0.2) !important;
  color: rgba(255, 255, 255, 0.5) !important;
  box-shadow: none !important;
}

.safe-bottom {
  height: calc(120rpx + env(safe-area-inset-bottom));
}

/* WeUI加载和提示组件样式覆盖 */
.weui-loading {
  backdrop-filter: blur(20rpx);
}

.weui-toast {
  backdrop-filter: blur(20rpx);
}

/* ==================== 革新设计新增样式 ==================== */

/* 进度指示器 */
.progress-indicator {
  margin: 0 0 48rpx;
  padding: 0 32rpx;
}

.progress-bar {
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ECDC4 0%, #45B7D1 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  display: block;
}

/* 毛玻璃卡片 - 重影修复 */
.glass-card {
  background: rgba(255, 255, 255, 0.15);
  /* 使用统一毛玻璃效果 */
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  box-shadow:
    0 16rpx 64rpx rgba(0, 0, 0, 0.1),
    0 2rpx 0 rgba(255, 255, 255, 0.3) inset;
  overflow: hidden;
  margin-bottom: 32rpx;
  /* 防止重影 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* 卡片标题 */
.card-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  /* 减轻text-shadow避免重影 */
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.15);
  /* 优化字体渲染 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 表单内容 */
.form-content {
  padding: 32rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

/* 标签 */
.item-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

.required-star {
  color: #FF6B6B;
  font-weight: bold;
  font-size: 28rpx;
}

.optional-tag {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  backdrop-filter: blur(10rpx);
}

/* 输入框容器 */
.input-container {
  position: relative;
}

.text-input {
  width: 100%;
  min-height: 80rpx;
  background: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 24rpx 80rpx 24rpx 20rpx;
  font-size: 28rpx;
  color: #2d3436;
  backdrop-filter: blur(10rpx);
  box-sizing: border-box;
  line-height: 1.4;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.text-input::placeholder {
  color: #b2bec3;
}

.char-counter {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20rpx;
  color: #636e72;
  pointer-events: none;
  background: rgba(255, 255, 255, 0.8);
  padding: 2rpx 6rpx;
  border-radius: 8rpx;
}

/* 选择器容器 */
.selector-container {
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
  overflow: hidden;
}

.selector-display {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 20rpx;
}

.selector-text {
  flex: 1;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

.selector-text.placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 16rpx;
  padding: 48rpx 32rpx 32rpx;
}

.btn-secondary {
  flex: 1;
  background: rgba(255, 255, 255, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  font-weight: 500;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  backdrop-filter: blur(10rpx);
}

.btn-secondary:active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(0.98);
}

.btn-primary {
  flex: 2;
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  border: none;
  border-radius: 16rpx;
  color: white;
  font-size: 28rpx;
  font-weight: 500;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(78, 205, 196, 0.3);
}

.btn-primary:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.4);
}

.btn-primary.create-btn {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.3);
}

.btn-primary.create-btn:active {
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.4);
}

/* 邀请好友共建样式 */
.invite-section {
  background: rgba(255, 120, 117, 0.1);
  border: 1rpx solid rgba(255, 120, 117, 0.2);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-top: 24rpx;
}

.invite-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.invite-label {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.invite-description {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(255, 120, 117, 0.2);
}

/* {{ AURA-X: Add - 邀请按钮样式. Approval: 寸止(ID:1738056000). }} */
.invite-btn {
  background: rgba(78, 205, 196, 0.1);
  border: 2rpx solid #4ECDC4;
  border-radius: 48rpx;
  padding: 12rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #4ECDC4;
  transition: all 0.3s ease;
}

.invite-btn:active {
  background: rgba(78, 205, 196, 0.2);
  transform: scale(0.95);
}

.description-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

