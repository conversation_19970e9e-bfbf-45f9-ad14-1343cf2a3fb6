<!-- subpackages/travel-planning/expense-list/index.wxml -->
<view class="page-container">
  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-card">
      <view class="stats-item">
        <text class="stats-label">总支出</text>
        <text class="stats-value">¥{{totalAmount}}</text>
      </view>
      <view class="stats-item">
        <text class="stats-label">记录数</text>
        <text class="stats-value">{{recordCount}}笔</text>
      </view>
    </view>
  </view>

  <!-- 费用记录列表 -->
  <view class="content-section">

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <van-loading size="24px" color="#ffffff">加载中...</van-loading>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{isEmpty}}" class="empty-container">
      <van-empty description="暂无费用记录">
        <view slot="image" class="empty-icon">
          <custom-icon name="wallet" size="120" color="rgba(255,255,255,0.3)" />
        </view>
      </van-empty>
      <view class="empty-action">
        <van-button type="primary" size="normal" bind:click="addExpense" custom-class="add-first-btn">
          添加第一笔费用
        </van-button>
      </view>
    </view>

    <!-- 费用记录列表 -->
    <view wx:else class="expense-list">
      <view wx:for="{{expenseRecords}}" wx:key="id" class="expense-item-wrapper">
        <view class="expense-item" bind:tap="viewExpenseDetail" data-record="{{item}}">
          <view class="expense-info">
            <text class="expense-desc">{{item.description}}</text>
            <view class="expense-meta">
              <text class="expense-category">{{item.category}}</text>
              <text class="expense-date">{{item.date}}</text>
              <text wx:if="{{item.location}}" class="expense-location">{{item.location}}</text>
            </view>
          </view>
          <view class="expense-amount">
            <text class="amount">¥{{item.amount}}</text>
          </view>
        </view>
        <view class="expense-actions">
          <view class="delete-btn" catch:tap="deleteExpenseRecord" data-record="{{item}}">
            <custom-icon name="delete" size="72" color="#ffffff" />
          </view>
        </view>
      </view>
    </view>
  </view>

</view>
