<!-- 旅行规划页面骨架屏 -->
<view class="skeleton-container" wx:if="{{loading}}">
  <!-- 统计数据骨架 -->
  <view class="skeleton-section">
    <view class="skeleton-title"></view>
    <view class="skeleton-stats">
      <view class="skeleton-stat-item" wx:for="{{4}}" wx:key="index">
        <view class="skeleton-number"></view>
        <view class="skeleton-label"></view>
      </view>
    </view>
  </view>

  <!-- 当前计划骨架 -->
  <view class="skeleton-section">
    <view class="skeleton-title"></view>
    <view class="skeleton-card">
      <view class="skeleton-card-header">
        <view class="skeleton-avatar"></view>
        <view class="skeleton-info">
          <view class="skeleton-name"></view>
          <view class="skeleton-date"></view>
        </view>
      </view>
      <view class="skeleton-card-content">
        <view class="skeleton-line"></view>
        <view class="skeleton-line short"></view>
      </view>
    </view>
  </view>

  <!-- 进行中计划骨架 -->
  <view class="skeleton-section">
    <view class="skeleton-title"></view>
    <view class="skeleton-list">
      <view class="skeleton-list-item" wx:for="{{3}}" wx:key="index">
        <view class="skeleton-dot"></view>
        <view class="skeleton-content">
          <view class="skeleton-line"></view>
          <view class="skeleton-line short"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 计划列表骨架 -->
  <view class="skeleton-section">
    <view class="skeleton-title"></view>
    <view class="skeleton-grid">
      <view class="skeleton-grid-item" wx:for="{{4}}" wx:key="index">
        <view class="skeleton-image"></view>
        <view class="skeleton-text"></view>
      </view>
    </view>
  </view>
</view>

<style>
.skeleton-container {
  padding: 20rpx;
  background: #f5f5f5;
}

.skeleton-section {
  margin-bottom: 40rpx;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
}

.skeleton-title {
  width: 200rpx;
  height: 40rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
}

.skeleton-stats {
  display: flex;
  justify-content: space-between;
}

.skeleton-stat-item {
  flex: 1;
  text-align: center;
}

.skeleton-number {
  width: 80rpx;
  height: 50rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8rpx;
  margin: 0 auto 15rpx;
}

.skeleton-label {
  width: 100rpx;
  height: 30rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6rpx;
  margin: 0 auto;
}

.skeleton-card {
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  padding: 30rpx;
}

.skeleton-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.skeleton-avatar {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 50%;
  margin-right: 20rpx;
}

.skeleton-info {
  flex: 1;
}

.skeleton-name {
  width: 150rpx;
  height: 35rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6rpx;
  margin-bottom: 10rpx;
}

.skeleton-date {
  width: 200rpx;
  height: 25rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6rpx;
}

.skeleton-line {
  height: 30rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6rpx;
  margin-bottom: 15rpx;
}

.skeleton-line.short {
  width: 60%;
}

.skeleton-list-item {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
}

.skeleton-dot {
  width: 20rpx;
  height: 20rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 50%;
  margin-right: 20rpx;
}

.skeleton-content {
  flex: 1;
}

.skeleton-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.skeleton-grid-item {
  width: calc(50% - 10rpx);
}

.skeleton-image {
  width: 100%;
  height: 200rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
}

.skeleton-text {
  width: 80%;
  height: 30rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6rpx;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
