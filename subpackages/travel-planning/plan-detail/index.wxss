/* subpackages/travel-planning/plan-detail/index.wxss */
@import './collaboration.wxss';

/* 页面容器 */
.plan-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  padding: 32rpx 24rpx;
  box-sizing: border-box;
}

/* 加载和错误状态 */
.van-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.error-empty {
  margin-top: 200rpx;
}

/* 毛玻璃卡片基础样式 - 重影修复 */
.glass-card {
  background: rgba(255, 255, 255, 0.15);
  /* 使用统一毛玻璃效果，避免重复定义 */
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
  /* 防止重影 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  overflow: hidden;
}

/* 计划概览卡片 */
.plan-overview {
  margin-bottom: 32rpx;
}

.plan-header {
  margin-bottom: 32rpx;
}

.plan-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.plan-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #2d3436;
  flex: 1;
  margin-right: 16rpx;
}

.status-tag {
  border-radius: 16rpx !important;
  font-size: 24rpx !important;
  padding: 8rpx 16rpx !important;
}

.plan-subtitle {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.destination {
  font-size: 28rpx;
  color: #636e72;
}

.plan-info {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.info-label {
  font-size: 24rpx;
  color: #636e72;
}

.info-value {
  font-size: 28rpx;
  color: #2d3436;
  font-weight: 500;
}

.info-extra {
  font-size: 24rpx;
  color: #b2bec3;
}

/* 预算管理卡片 */
.budget-card {
  margin-bottom: 32rpx;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3436;
}

.budget-overview {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
}

.budget-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.budget-label {
  font-size: 24rpx;
  color: #636e72;
}

.budget-value {
  font-size: 32rpx;
  font-weight: 600;
}

.budget-value.total {
  color: #1890ff;
}

.budget-value.spent {
  color: #ff4d4f;
}

.budget-value.remaining {
  color: #52c41a;
}

.budget-progress {
  margin-bottom: 24rpx;
}

.progress-bar {
  margin-bottom: 12rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #636e72;
  text-align: center;
  display: block;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3436;
  margin-bottom: 16rpx;
  display: block;
}

.budget-item-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.budget-item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
}

.item-name {
  font-size: 28rpx;
  color: #2d3436;
}

.item-amount {
  font-size: 28rpx;
  color: #1890ff;
  font-weight: 500;
}

/* 快捷操作 */
.quick-actions {
  margin-bottom: 32rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
  margin-top: 24rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx 16rpx;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 20rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10rpx);
}

.action-item:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.8);
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.25),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
}

.action-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  border: 3rpx solid rgba(255, 255, 255, 0.8);
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
}

.action-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 700;
  text-align: center;
  text-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.5),
    0 1rpx 2rpx rgba(0, 0, 0, 0.8);
}

/* 费用记录 */
.expense-records {
  margin-bottom: 32rpx;
}

.card-header .view-all {
  margin-left: auto;
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 700;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.4);
  border: 3rpx solid rgba(255, 255, 255, 0.7);
  border-radius: 20rpx;
  text-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.5),
    0 1rpx 2rpx rgba(0, 0, 0, 0.8);
  transition: all 0.3s ease;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10rpx);
}

.card-header .view-all:active {
  background: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.8);
  transform: scale(0.95);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
}

.expense-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-top: 24rpx;
}

.expense-item-wrapper {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.expense-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10rpx);
}

.expense-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.7);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
}

/* 费用记录操作按钮 */
.expense-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.delete-btn {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 77, 79, 0.9);
  border-radius: 50%;
  border: 3rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  box-shadow:
    0 6rpx 24rpx rgba(255, 77, 79, 0.4),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10rpx);
}

.delete-btn:active {
  transform: scale(0.85);
  background: rgba(255, 77, 79, 1);
  border-color: rgba(255, 255, 255, 1);
  box-shadow:
    0 3rpx 12rpx rgba(255, 77, 79, 0.6),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
}

.expense-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.expense-desc {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

.expense-category {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.expense-date {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.expense-amount {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.expense-amount .amount {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 700;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.expense-empty {
  margin: 40rpx 0;
}

/* 操作菜单样式 */
.action-sheet {
  border-radius: 32rpx 32rpx 0 0 !important;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .action-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
  }

  .budget-overview {
    flex-direction: column;
    gap: 16rpx;
  }

  .budget-item {
    flex-direction: row;
    justify-content: space-between;
  }
}

/* 动画效果 */
.plan-content {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Vant组件样式覆盖 */
.van-progress__portion {
  border-radius: 8rpx !important;
}

.van-tag {
  border: none !important;
  font-weight: 500 !important;
}

.van-button--primary {
  background: linear-gradient(135deg, #4ECDC4 0%, #45B7D1 100%) !important;
  border: none !important;
  border-radius: 20rpx !important;
}

.van-empty__description {
  color: #636e72 !important;
  font-size: 28rpx !important;
}

/* Dialog 弹窗按钮样式增强 */
.van-dialog {
  border-radius: 24rpx !important;
  overflow: hidden !important;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.3) !important;
}

.van-dialog__header {
  padding: 32rpx 32rpx 16rpx !important;
  background: #ffffff !important;
}

.van-dialog__content {
  padding: 16rpx 32rpx 32rpx !important;
  background: #ffffff !important;
}

.van-dialog__footer {
  background: #ffffff !important;
  border-top: 1rpx solid #f0f0f0 !important;
}

.van-dialog__footer .van-button {
  height: 88rpx !important;
  border-radius: 0 !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  border: none !important;
}

.van-dialog__footer .van-button--default {
  background: #f8f9fa !important;
  color: #495057 !important;
}

.van-dialog__footer .van-button--default:active {
  background: #e9ecef !important;
  color: #343a40 !important;
}

.van-dialog__footer .van-button:not(.van-button--default) {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%) !important;
  color: #ffffff !important;
}

.van-dialog__footer .van-button:not(.van-button--default):active {
  background: linear-gradient(135deg, #d9363e 0%, #ff595f 100%) !important;
}

/* 预算管理相关样式 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.section-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.4);
  border: 3rpx solid rgba(255, 255, 255, 0.7);
  border-radius: 20rpx;
  transition: all 0.3s ease;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10rpx);
}

.section-action:active {
  background: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.8);
  transform: scale(0.95);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
}

.action-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 700;
  text-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.5),
    0 1rpx 2rpx rgba(0, 0, 0, 0.8);
}

.budget-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 40rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #636e72;
}

.empty-desc {
  font-size: 24rpx;
  color: #b2bec3;
}

/* 预算管理弹窗 */
.budget-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.budget-modal.show {
  opacity: 1;
  visibility: visible;
}

.budget-modal-content {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.budget-modal.show .budget-modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3436;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f6fa;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.total-budget-section {
  margin-bottom: 32rpx;
}

.budget-label {
  font-size: 28rpx;
  color: #2d3436;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}

.budget-input-row {
  display: flex;
  align-items: center;
  background: #f5f6fa;
  border-radius: 12rpx;
  padding: 0 20rpx;
}

.currency {
  font-size: 28rpx;
  color: #636e72;
  margin-right: 8rpx;
}

.budget-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #2d3436;
}

.budget-items-section {
  margin-bottom: 32rpx;
}

.items-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.items-title {
  font-size: 28rpx;
  color: #2d3436;
  font-weight: 500;
}

.add-item-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(82, 196, 26, 0.1);
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #52c41a;
}

.budget-items-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.budget-item-edit {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx;
  background: #f5f6fa;
  border-radius: 12rpx;
}

.item-name-input {
  flex: 1;
  height: 60rpx;
  font-size: 26rpx;
  color: #2d3436;
}

.item-amount-input {
  display: flex;
  align-items: center;
  width: 160rpx;
}

.item-amount-input input {
  flex: 1;
  height: 60rpx;
  font-size: 26rpx;
  color: #2d3436;
  text-align: right;
}

.item-delete {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 77, 79, 0.1);
}

.no-items {
  text-align: center;
  padding: 40rpx 20rpx;
  color: #b2bec3;
  font-size: 26rpx;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
}

.modal-btn.cancel {
  color: #636e72;
  background: #f5f6fa;
}

.modal-btn.confirm {
  color: white;
  background: linear-gradient(135deg, #4ECDC4 0%, #45B7D1 100%);
}

/* 费用详情弹窗 */
.expense-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.expense-detail-modal.show {
  opacity: 1;
  visibility: visible;
}

.expense-detail-content {
  width: 85%;
  max-width: 500rpx;
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.expense-detail-modal.show .expense-detail-content {
  transform: scale(1);
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3436;
}

.detail-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f6fa;
}

.detail-body {
  padding: 32rpx;
}

.expense-amount-display {
  text-align: center;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
  border-radius: 16rpx;
}

.amount-label {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

.amount-value {
  font-size: 48rpx;
  font-weight: 600;
  color: white;
}

.expense-info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f6fa;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #636e72;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #2d3436;
  text-align: right;
  flex: 1;
  margin-left: 32rpx;
}

/* 状态容器 */
.status-container {
  display: flex;
  align-items: center;
  gap: 8rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.status-container:active {
  transform: scale(0.95);
}

.status-tag.clickable {
  cursor: pointer;
}

.status-arrow {
  transition: transform 0.3s ease;
}

/* 状态选择器弹窗 */
.status-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 32rpx;
  box-sizing: border-box;
}

.status-selector-modal {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
}

.status-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.status-option:last-child {
  border-bottom: none;
}

.status-option:active {
  background: rgba(0, 0, 0, 0.05);
}

.status-option.active {
  background: rgba(82, 196, 26, 0.1);
}

.status-option-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.status-option-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-option-text {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.status-option-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3436;
}

.status-option-desc {
  font-size: 24rpx;
  color: #636e72;
}

.status-option-check {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}