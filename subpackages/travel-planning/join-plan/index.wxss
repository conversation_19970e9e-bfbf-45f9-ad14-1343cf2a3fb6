/* 加入协作计划页面样式 */

.join-plan-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 渐变背景 */
.gradient-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  z-index: -1;
}

/* 内容滚动区域 */
.content-scroll {
  height: 100vh;
  padding: 40rpx 32rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.header-icon {
  width: 108rpx;
  height: 108rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.header-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: 700;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.header-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 毛玻璃卡片 */
.glass-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 48rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3436;
}

/* 邀请码输入 */
.input-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.invite-code-input {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.code-input {
  height: 88rpx;
  background: rgba(245, 245, 245, 0.8);
  border-radius: 24rpx;
  padding: 0 32rpx;
  font-size: 36rpx;
  font-weight: 600;
  text-align: center;
  letter-spacing: 8rpx;
  color: #2d3436;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.code-input:focus {
  background: rgba(78, 205, 196, 0.1);
  border-color: #4ECDC4;
}

.input-hint {
  display: flex;
  align-items: center;
  gap: 12rpx;
  justify-content: center;
}

.hint-text {
  font-size: 24rpx;
  color: #999;
}

/* {{ AURA-X: Remove - 移除剪切板按钮样式，改为自动检测. Approval: 寸止(ID:1738056000). }} */

/* 计划预览 */
.preview-card {
  animation: fadeInUp 0.5s ease-out;
}

.plan-preview {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.plan-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #2d3436;
  flex: 1;
}

.plan-status {
  margin-left: 16rpx;
}

.preview-info {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.info-text {
  font-size: 28rpx;
  color: #636e72;
}

/* 协作者信息 */
.collaborators-info {
  background: rgba(245, 245, 245, 0.5);
  border-radius: 24rpx;
  padding: 32rpx;
}

.collaborators-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.collaborators-title {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.collaborators-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.collaborator-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  position: relative;
}

.collaborator-item.creator {
  border: 2rpx solid rgba(255, 77, 79, 0.3);
  background: rgba(255, 77, 79, 0.05);
}

.avatar-container {
  position: relative;
  width: 64rpx;
  height: 64rpx;
}

.collaborator-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
}

.creator-badge {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 24rpx;
  height: 24rpx;
  background: #faad14;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid white;
}

.collaborator-name {
  font-size: 28rpx;
  color: #2d3436;
  font-weight: 500;
  flex: 1;
}

.collaborator-role {
  font-size: 24rpx;
  color: #999;
  padding: 4rpx 12rpx;
  background: rgba(245, 245, 245, 0.8);
  border-radius: 12rpx;
}

/* {{ AURA-X: Modify - 优化按钮居中显示. Approval: 寸止(ID:1738056000). }} */
/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  margin-top: 40rpx;
  padding: 0 32rpx;
}

/* {{ AURA-X: Add - 添加预览按钮样式. Approval: 寸止(ID:1738056000). }} */
.join-button, .back-button, .preview-button {
  height: 88rpx !important;
  border-radius: 24rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
}

/* {{ AURA-X: Modify - 半透明白色背景+跑马灯光效. Approval: 寸止(ID:1738056000). }} */
.preview-button {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 2rpx solid rgba(255, 255, 255, 0.8) !important;
  color: #2d3436 !important;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10rpx) !important;
  position: relative !important;
  overflow: hidden !important;
  width: 280rpx !important;
}

/* 跑马灯光效 */
.preview-button::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(45deg,
    transparent,
    rgba(78, 205, 196, 0.6),
    rgba(69, 183, 209, 0.6),
    transparent,
    rgba(255, 107, 107, 0.6),
    transparent
  );
  background-size: 400% 400%;
  border-radius: 26rpx;
  z-index: -1;
  animation: rainbow-border 3s ease-in-out infinite;
}

@keyframes rainbow-border {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.join-button {
  background: linear-gradient(135deg, #4ECDC4 0%, #45B7D1 100%) !important;
  box-shadow: 0 8rpx 24rpx rgba(78, 205, 196, 0.3) !important;
  width: 280rpx !important;
}

.back-button {
  background: rgba(255, 255, 255, 0.8) !important;
  color: #666 !important;
  border: 1rpx solid rgba(255, 255, 255, 0.3) !important;
}

/* 加载状态 */
.van-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
