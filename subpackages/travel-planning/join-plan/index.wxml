<!--subpackages/travel-planning/join-plan/index.wxml-->
<view class="join-plan-container">
  <!-- 渐变背景 -->
  <view class="gradient-background"></view>

  <!-- 页面内容 -->
  <scroll-view class="content-scroll" scroll-y="true" enhanced="true" show-scrollbar="false">
    
    <!-- 页面标题 -->
    <view class="page-header">
      <view class="header-content">
        <view class="header-icon">
          <custom-icon name="friends" size="54" color="#4ECDC4" />
        </view>
        <view class="header-text">
          <text class="header-title">加入协作计划</text>
          <text class="header-subtitle">与好友一起规划精彩旅程</text>
        </view>
      </view>
    </view>

    <!-- 邀请码输入 -->
    <view class="glass-card input-card">
      <view class="card-header">
        <custom-icon name="link" size="32" color="#1890ff" />
        <text class="card-title">输入邀请码</text>
      </view>

      <view class="input-section">
        <view class="invite-code-input">
          <input
            class="code-input"
            placeholder="请输入6位邀请码"
            value="{{formData.inviteCode}}"
            bindinput="onInviteCodeInput"
            maxlength="6"
            type="text"
            style="text-transform: uppercase;"
          />
          <view class="input-hint">
            <custom-icon name="info" size="24" color="#999" />
            <text class="hint-text">邀请码由计划创建者提供，点击页面时自动检测剪切板</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 计划预览 -->
    <view wx:if="{{planPreview}}" class="glass-card preview-card">
      <view class="card-header">
        <custom-icon name="airplane" size="32" color="#4ECDC4" />
        <text class="card-title">计划预览</text>
      </view>

      <view class="plan-preview">
        <view class="preview-header">
          <text class="plan-title">{{planPreview.title}}</text>
          <view class="plan-status">
            <van-tag type="primary" color="#4ECDC4">
              {{planPreview.status === 'planning' ? '规划中' : planPreview.status === 'ongoing' ? '进行中' : '已完成'}}
            </van-tag>
          </view>
        </view>

        <view class="preview-info">
          <view class="info-item">
            <custom-icon name="location" size="28" color="#666" />
            <text class="info-text">{{planPreview.destination}}</text>
          </view>
          
          <view class="info-item">
            <custom-icon name="calendar" size="28" color="#666" />
            <text class="info-text">{{planPreview.startDate}} 至 {{planPreview.endDate}}</text>
          </view>
          
          <view class="info-item">
            <custom-icon name="wallet" size="28" color="#666" />
            <text class="info-text">预算 ¥{{planPreview.budget || planPreview.budgetDetail.total}}</text>
          </view>
        </view>

        <!-- 协作者信息 -->
        <view class="collaborators-info">
          <view class="collaborators-header">
            <custom-icon name="user" size="24" color="#999" />
            <text class="collaborators-title">当前协作者 ({{planPreview.collaboration.collaborators.length + 1}}/{{planPreview.collaboration.maxCollaborators}})</text>
          </view>
          
          <view class="collaborators-list">
            <!-- 创建者 -->
            <view class="collaborator-item creator">
              <view class="avatar-container">
                <image class="collaborator-avatar" src="{{planPreview.creatorInfo.avatar}}" mode="aspectFill" />
                <view class="creator-badge">
                  <custom-icon name="crown" size="16" color="#faad14" />
                </view>
              </view>
              <text class="collaborator-name">{{planPreview.creatorInfo.nickname}}</text>
              <text class="collaborator-role">创建者</text>
            </view>
            
            <!-- 协作者 -->
            <view wx:for="{{planPreview.collaboration.collaborators}}" wx:key="openid" class="collaborator-item">
              <image class="collaborator-avatar" src="{{item.avatar}}" mode="aspectFill" />
              <text class="collaborator-name">{{item.nickname}}</text>
              <text class="collaborator-role">协作者</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- {{ AURA-X: Add - 添加确定按钮预览计划. Approval: 寸止(ID:1738056000). }} -->
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <!-- {{ AURA-X: Modify - 优化按钮显示条件. Approval: 寸止(ID:1738056000). }} -->
      <!-- 预览计划按钮 -->
      <van-button
        wx:if="{{!planPreview && formData.inviteCode && formData.inviteCode.length >= 6}}"
        type="default"
        size="large"
        bind:click="previewPlanByCode"
        custom-class="preview-button">
        确定
      </van-button>

      <!-- 加入计划按钮 -->
      <van-button
        wx:if="{{planPreview}}"
        type="primary"
        size="large"
        color="#4ECDC4"
        loading="{{loading}}"
        bind:click="joinPlan"
        custom-class="join-button">
        加入协作计划
      </van-button>
    </view>

  </scroll-view>

  <!-- 加载状态 -->
  <van-loading wx:if="{{loading && !planPreview}}" type="spinner" color="#4ECDC4" size="48rpx" vertical>
    加载中...
  </van-loading>
</view>
