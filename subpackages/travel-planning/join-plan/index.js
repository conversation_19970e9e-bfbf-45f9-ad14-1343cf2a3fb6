// subpackages/travel-planning/join-plan/index.js
// 加入旅行计划页面

import userManager from '../../../utils/userManager.js'

Page({
  data: {
    // 页面状态
    loading: false,
    
    // 邀请信息
    inviteCode: '',
    planPreview: null,
    
    // 表单数据
    formData: {
      inviteCode: ''
    },
    
    // 用户信息
    userInfo: null
  },

  async onLoad(options) {
    // {{ AURA-X: Modify - 确保用户信息包含正确的头像数据. Approval: 寸止(ID:1738056000). }}
    // 获取用户信息
    const userInfo = userManager.getUserInfo()
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    // 确保头像字段正确
    const processedUserInfo = {
      ...userInfo,
      avatar: userInfo.avatar || userInfo.avatarUrl || '/images/user.svg',
      nickname: userInfo.nickname || userInfo.nickName || '协作者'
    }

    this.setData({ userInfo: processedUserInfo })
    
    // 如果通过分享链接进入，直接处理邀请
    if (options.planId && options.invite) {
      this.handleDirectInvite(options.planId)
    } else if (options.inviteCode) {
      // 如果有邀请码参数，自动填入
      this.setData({
        'formData.inviteCode': options.inviteCode
      })
      this.previewPlan(options.inviteCode)
    } else {
      // {{ AURA-X: Add - 页面加载时自动检测剪切板. Approval: 寸止(ID:1738056000). }}
      // 自动检测剪切板
      setTimeout(() => {
        this.checkClipboard()
      }, 500)
    }
  },

  // 处理直接邀请（通过分享链接）
  async handleDirectInvite(planId) {
    try {
      this.setData({ loading: true })
      
      // 获取计划信息
      const result = await wx.cloud.callFunction({
        name: 'travel',
        data: {
          action: 'getTravelPlan',
          data: { planId }
        }
      })
      
      if (result.result.success) {
        const planData = result.result.data
        
        // 检查是否开启协作
        if (!planData.collaboration?.enabled) {
          wx.showToast({
            title: '该计划未开启协作',
            icon: 'none'
          })
          setTimeout(() => wx.navigateBack(), 1500)
          return
        }
        
        // 显示计划预览
        this.setData({
          planPreview: planData,
          inviteCode: planData.collaboration.inviteCode,
          'formData.inviteCode': planData.collaboration.inviteCode
        })
      } else {
        wx.showToast({
          title: result.result.message || '获取计划信息失败',
          icon: 'none'
        })
      }
      
    } catch (error) {
      console.error('处理直接邀请失败:', error)
      wx.showToast({
        title: '处理邀请失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 邀请码输入
  onInviteCodeInput(e) {
    const inviteCode = e.detail.value.toUpperCase()
    this.setData({
      'formData.inviteCode': inviteCode
    })
    
    // 自动预览（当输入6位时）
    if (inviteCode.length === 6) {
      this.previewPlan(inviteCode)
    }
  },

  // {{ AURA-X: Modify - 清理调试信息，优化预览逻辑. Approval: 寸止(ID:1738056000). }}
  // 预览计划
  async previewPlan(inviteCode) {
    if (!inviteCode || inviteCode.length !== 6) {
      return
    }

    try {
      this.setData({ loading: true })

      const result = await wx.cloud.callFunction({
        name: 'travel',
        data: {
          action: 'previewPlanByCode',
          data: { inviteCode }
        }
      })

      if (result.result && result.result.success) {
        this.setData({
          planPreview: result.result.data
        })
        wx.showToast({
          title: '计划预览加载成功',
          icon: 'success'
        })
      } else {
        this.setData({
          planPreview: null
        })
        wx.showToast({
          title: result.result?.message || '预览失败',
          icon: 'none'
        })
      }

    } catch (error) {
      console.error('预览计划异常:', error)
      this.setData({
        planPreview: null
      })
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加入计划
  async joinPlan() {
    const { formData, userInfo, planPreview } = this.data
    
    if (!formData.inviteCode) {
      wx.showToast({
        title: '请输入邀请码',
        icon: 'none'
      })
      return
    }
    
    if (!planPreview) {
      wx.showToast({
        title: '请先预览计划',
        icon: 'none'
      })
      return
    }
    
    try {
      this.setData({ loading: true })

      // 如果用户头像是临时文件，先上传到云存储
      let processedUserInfo = { ...userInfo }
      if (userInfo?.avatar && userInfo.avatar.startsWith('wxfile://')) {
        try {
          const uploadResult = await wx.cloud.callFunction({
            name: 'travel',
            data: {
              action: 'uploadAvatar',
              data: { tempFilePath: userInfo.avatar }
            }
          })

          if (uploadResult.result.success) {
            processedUserInfo.avatar = uploadResult.result.data.avatarUrl
          }
        } catch (error) {
          console.error('上传头像失败:', error)
        }
      }

      const result = await wx.cloud.callFunction({
        name: 'travel',
        data: {
          action: 'joinPlanByCode',
          data: {
            inviteCode: formData.inviteCode,
            userInfo: {
              nickname: processedUserInfo.nickname || processedUserInfo.nickName || '协作者',
              avatar: processedUserInfo.avatar || processedUserInfo.avatarUrl || '/images/user.svg'
            }
          }
        }
      })
      
      if (result.result.success) {
        wx.showToast({
          title: '成功加入计划',
          icon: 'success'
        })
        
        // 跳转到计划详情页
        setTimeout(() => {
          wx.redirectTo({
            url: `/subpackages/travel-planning/plan-detail/index?id=${result.result.data.planId}`
          })
        }, 1500)
      } else {
        wx.showToast({
          title: result.result.message || '加入失败',
          icon: 'none'
        })
      }
      
    } catch (error) {
      console.error('加入计划失败:', error)
      wx.showToast({
        title: '加入失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // {{ AURA-X: Modify - 静默检测剪切板，只在发现邀请码时提示. Approval: 寸止(ID:1738056000). }}
  // 检测剪切板
  async checkClipboard() {
    try {
      const clipboardData = await wx.getClipboardData()
      const clipboardText = clipboardData.data

      // 检查是否包含邀请内容
      const invitePattern = /📋 邀请码：([A-Z0-9]{6})/
      const match = clipboardText.match(invitePattern)

      if (match && match[1]) {
        const inviteCode = match[1]

        // 自动填入邀请码
        this.setData({
          'formData.inviteCode': inviteCode
        })

        // 自动预览计划
        this.previewPlan(inviteCode)

        wx.showToast({
          title: '已自动填入邀请码',
          icon: 'success'
        })
      }
      // 静默处理未发现邀请码的情况
    } catch (error) {
      // 静默处理剪切板读取失败
    }
  },

  // {{ AURA-X: Modify - 清理调试信息. Approval: 寸止(ID:1738056000). }}
  // 预览计划（通过确定按钮触发）
  previewPlanByCode() {
    const { formData } = this.data

    if (!formData.inviteCode) {
      wx.showToast({
        title: '请输入邀请码',
        icon: 'none'
      })
      return
    }

    if (formData.inviteCode.length !== 6) {
      wx.showToast({
        title: '邀请码必须是6位',
        icon: 'none'
      })
      return
    }

    this.previewPlan(formData.inviteCode)
  },

  // 返回
  goBack() {
    wx.navigateBack()
  }
})
