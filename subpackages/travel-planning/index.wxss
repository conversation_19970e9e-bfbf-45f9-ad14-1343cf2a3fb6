/* subpackages/travel-planning/index.wxss */

.travel-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  position: relative;
}

/* 背景装饰 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 150rpx;
  height: 150rpx;
  bottom: 20%;
  left: 15%;
  animation-delay: 2s;
}

.orb-3 {
  width: 100rpx;
  height: 100rpx;
  top: 50%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
}

/* 内容滚动区域 */
.content-scroll {
  height: 100vh;
  position: relative;
  z-index: 1;
}

/* 顶部横幅 - 圆周旅记风格 */
.hero-section {
  position: relative;
  padding: 60rpx 32rpx 40rpx;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(78, 205, 196, 0.1) 100%);
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.float-item {
  position: absolute;
  font-size: 32rpx;
  opacity: 0.3;
  animation: float-around 8s ease-in-out infinite;
}

.item-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.item-2 {
  top: 30%;
  right: 15%;
  animation-delay: 2s;
}

.item-3 {
  bottom: 40%;
  left: 20%;
  animation-delay: 4s;
}

.item-4 {
  bottom: 20%;
  right: 25%;
  animation-delay: 6s;
}

@keyframes float-around {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  25% { transform: translateY(-20rpx) rotate(90deg); }
  50% { transform: translateY(-10rpx) rotate(180deg); }
  75% { transform: translateY(-30rpx) rotate(270deg); }
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  margin-bottom: 40rpx;
}

.hero-title-container {
  margin-bottom: 40rpx;
}

.hero-title {
  display: block;
  font-size: 48rpx;
  font-weight: 800;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
}

.quick-create-container {
  display: flex;
  justify-content: center;
}

.quick-create-btn {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  backdrop-filter: blur(20rpx);
  overflow: hidden;
  transition: all 0.3s ease;
}

.quick-create-btn:active {
  transform: scale(0.95);
}

.btn-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
  position: relative;
  z-index: 2;
}

.btn-icon {
  font-size: 24rpx;
  animation: sparkle-pulse 2s ease-in-out infinite;
}

@keyframes sparkle-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.btn-text {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
}

.btn-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.quick-create-btn:active .btn-shine {
  left: 100%;
}

/* 统计数据卡片 */
.stats-cards {
  display: flex;
  gap: 16rpx;
  position: relative;
  z-index: 2;
}

.stats-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 24rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  transition: all 0.3s ease;
}

.stats-card:active {
  transform: translateY(-4rpx);
  background: rgba(255, 255, 255, 0.2);
}

.card-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.card-content {
  text-align: center;
}

.card-number {
  display: block;
  font-size: 28rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 4rpx;
}

.card-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.refresh-indicator {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 现代化区域标题样式 */
.section-header-modern {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.section-icon-container {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-emoji {
  font-size: 24rpx;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.section-title-modern {
  font-size: 32rpx;
  font-weight: 700;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.section-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.header-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.header-action:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.action-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 当前旅程卡片 */
.current-journey-section {
  margin-bottom: 40rpx;
}

.journey-card {
  margin: 0 32rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  padding: 32rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.journey-card:active {
  transform: translateY(-4rpx);
  background: rgba(255, 255, 255, 0.15);
}

.card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
}

.circle-1 {
  width: 120rpx;
  height: 120rpx;
  top: -60rpx;
  right: -60rpx;
}

.circle-2 {
  width: 80rpx;
  height: 80rpx;
  bottom: -40rpx;
  left: -40rpx;
}

.decoration-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.journey-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.journey-info {
  flex: 1;
}

.journey-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.journey-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.location-icon {
  font-size: 20rpx;
}

.location-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.journey-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: #4ECDC4;
  animation: status-pulse 2s ease-in-out infinite;
}

@keyframes status-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 进度环形图 */
.progress-rings {
  display: flex;
  justify-content: center;
  gap: 48rpx;
  margin-bottom: 32rpx;
}

.ring-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.progress-ring {
  width: 120rpx;
  height: 120rpx;
  position: relative;
  margin-bottom: 16rpx;
}

.ring-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 8rpx solid rgba(255, 255, 255, 0.1);
}

.ring-progress {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 8rpx solid transparent;
  border-top-color: #4ECDC4;
  transform: rotate(-90deg);
  transition: all 0.3s ease;
  background: conic-gradient(from 0deg, #4ECDC4 0%, #4ECDC4 var(--progress, 0%), transparent var(--progress, 0%));
  -webkit-mask: radial-gradient(circle at center, transparent 44rpx, black 52rpx);
  mask: radial-gradient(circle at center, transparent 44rpx, black 52rpx);
}

.budget-ring .ring-progress {
  border-top-color: #FF6B6B;
  background: conic-gradient(from 0deg, #FF6B6B 0%, #FF6B6B var(--progress, 0%), transparent var(--progress, 0%));
}

.ring-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.ring-number {
  display: block;
  font-size: 28rpx;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.ring-total,
.ring-unit {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

.ring-label {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 8rpx;
}

/* 旅程快速操作 */
.journey-actions {
  display: flex;
  justify-content: space-around;
  gap: 24rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 16rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
  flex: 1;
}

.action-item:active {
  transform: translateY(-2rpx);
  background: rgba(255, 255, 255, 0.15);
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
}

.expense-icon {
  background: rgba(255, 193, 7, 0.2);
}

.itinerary-icon {
  background: rgba(78, 205, 196, 0.2);
}

.map-icon {
  background: rgba(69, 183, 209, 0.2);
}

.icon-emoji {
  font-size: 24rpx;
}

.action-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  text-align: center;
}

/* 创建旅程区域 */
.create-journey-section {
  margin-bottom: 40rpx;
}

.create-options {
  padding: 0 32rpx;
}

.create-option-main {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.15), rgba(78, 205, 196, 0.15));
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.create-option-main:active {
  transform: translateY(-4rpx);
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(78, 205, 196, 0.2));
}

.option-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.magic-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.particle {
  position: absolute;
  font-size: 20rpx;
  opacity: 0.4;
  animation: particle-float 4s ease-in-out infinite;
}

.particle-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.particle-2 {
  top: 60%;
  right: 15%;
  animation-delay: 1s;
}

.particle-3 {
  bottom: 30%;
  left: 20%;
  animation-delay: 2s;
}

.particle-4 {
  top: 40%;
  right: 30%;
  animation-delay: 3s;
}

@keyframes particle-float {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-20rpx) scale(1.2); }
}

.option-content {
  flex: 1;
  position: relative;
  z-index: 2;
}

.option-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.option-badge {
  background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
  color: white;
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.option-title {
  font-size: 32rpx;
  font-weight: 700;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.option-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.option-features {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.feature-tag {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  backdrop-filter: blur(10rpx);
}

.option-arrow {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

/* 小选项网格 */
.create-options-grid {
  display: flex;
  gap: 16rpx;
}

.create-option-small {
  flex: 1;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 24rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  transition: all 0.3s ease;
}

.create-option-small:active {
  transform: translateY(-2rpx);
  background: rgba(255, 255, 255, 0.12);
}

.small-option-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-icon {
  background: rgba(78, 205, 196, 0.2);
}

.manual-icon {
  background: rgba(69, 183, 209, 0.2);
}

.small-option-content {
  text-align: center;
}

.small-option-title {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
}

.small-option-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.3;
}

/* 协作功能区域 */
.collaboration-section {
  margin-bottom: 40rpx;
}

.collaboration-options {
  padding: 0 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.collab-option {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.collab-option:active {
  transform: translateY(-2rpx);
  background: rgba(255, 255, 255, 0.12);
}

.collab-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.join-icon {
  background: rgba(78, 205, 196, 0.2);
}

.invite-icon {
  background: rgba(255, 107, 107, 0.2);
}

.collab-content {
  flex: 1;
}

.collab-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
}

.collab-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

/* 通用区域样式 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx 16rpx;
  margin-top: 24rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.section-title text {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.section-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.section-action:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

/* 当前计划卡片 */
.current-plan-section {
  padding: 0 32rpx 24rpx;
}

.current-plan-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.plan-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.plan-info {
  flex: 1;
}

.plan-title {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
  display: block;
}

.plan-meta {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.plan-destination {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.plan-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.status-planning {
  background: rgba(250, 173, 20, 0.2);
  color: #faad14;
  border: 1rpx solid rgba(250, 173, 20, 0.3);
}

.status-ongoing {
  background: rgba(82, 196, 26, 0.2);
  color: #52c41a;
  border: 1rpx solid rgba(82, 196, 26, 0.3);
}

.status-completed {
  background: rgba(24, 144, 255, 0.2);
  color: #1890ff;
  border: 1rpx solid rgba(24, 144, 255, 0.3);
}

.plan-progress {
  margin-bottom: 20rpx;
}

.progress-item {
  margin-bottom: 16rpx;
}

.progress-item:last-child {
  margin-bottom: 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.progress-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.progress-value {
  font-size: 22rpx;
  font-weight: 500;
  color: white;
}

.progress-bar {
  height: 6rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a, #73d13d);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.budget-fill {
  background: linear-gradient(90deg, #1890ff, #40a9ff);
}

.plan-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
  color: white;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn:active {
  transform: scale(0.95);
}

/* 快速操作网格 */
.quick-actions-section {
  padding: 0 32rpx 24rpx;
}

/* {{ AURA-X: Modify - 改为3列布局，更加对称美观. Approval: 寸止(ID:1738056000). }} */
.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12rpx;
}

.quick-action-item {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.quick-action-item:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.2);
}

/* {{ AURA-X: Modify - 第二行按钮不跨列但要对齐，创建计划与探索目的地对齐，快速记录与邀请协作对齐. Approval: 寸止(ID:1738056000). }} */
.quick-action-item.large-item {
  padding: 32rpx 24rpx;
  min-height: 140rpx;
}

.quick-action-item.large-item:nth-child(4) {
  grid-column: 1;
  grid-column-start: 1;
}

.quick-action-item.large-item:nth-child(5) {
  grid-column: 3;
  grid-column-start: 3;
}

.action-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.action-icon.create {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.action-icon.join {
  background: linear-gradient(135deg, #4ECDC4, #45B7D1);
}

.action-icon.invite {
  background: linear-gradient(135deg, #ff7875, #ff9c6e);
}

.action-icon.record {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.action-icon.explore {
  background: linear-gradient(135deg, #722ed1, #b37feb);
}

.action-icon.stats {
  background: linear-gradient(135deg, #faad14, #ffd666);
}

.action-title {
  font-size: 24rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 4rpx;
  display: block;
}

.action-desc {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
  display: block;
}

/* 我的计划列表 */
.my-plans-section {
  padding: 0 32rpx 24rpx;
}

.empty-state {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 48rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 28rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 8rpx;
  display: block;
}

.empty-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 24rpx;
  display: block;
}

.empty-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
}

.plans-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.plan-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.plan-card:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.2);
}

.plan-card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.plan-card-info {
  flex: 1;
}

.plan-card-title {
  font-size: 26rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 8rpx;
  display: block;
}

.plan-card-meta {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-wrap: wrap;
}

.plan-card-destination,
.plan-card-date {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* {{ AURA-X: Add - 添加角色标识样式. Approval: 寸止(ID:1738056000). }} */
.plan-card-badges {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6rpx;
}

.plan-card-status {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  font-weight: 500;
}

.plan-card-role {
  padding: 3rpx 6rpx;
  border-radius: 6rpx;
  font-size: 16rpx;
  font-weight: 400;
}

.role-collaborator {
  background: rgba(255, 107, 107, 0.2);
  color: #FF6B6B;
  border: 1rpx solid rgba(255, 107, 107, 0.3);
}

.plan-card-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.plan-card-stats {
  display: flex;
  gap: 16rpx;
}

.card-stat-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

.plan-card-progress {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.card-progress-bar {
  flex: 1;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2rpx;
  overflow: hidden;
}

.card-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a, #73d13d);
  border-radius: 2rpx;
  transition: width 0.3s ease;
}

.card-progress-text {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
}



/* 底部安全距离 */
.safe-bottom {
  height: 120rpx;
}

/* 计划选择弹窗 */
.plan-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.plan-selector-modal {
  width: 600rpx;
  max-height: 70vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3436;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
}

.modal-content {
  max-height: 400rpx;
  overflow-y: auto;
}

.plan-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: background 0.3s ease;
}

.plan-option:last-child {
  border-bottom: none;
}

.plan-option:active {
  background: rgba(78, 205, 196, 0.1);
}

.plan-option-info {
  flex: 1;
}

.plan-option-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3436;
  display: block;
  margin-bottom: 8rpx;
}

.plan-option-meta {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.plan-option-destination {
  font-size: 24rpx;
  color: #636e72;
}

.plan-option-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
