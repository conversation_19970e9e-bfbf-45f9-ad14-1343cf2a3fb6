<!--subpackages/travel-planning/map-view/index.wxml-->
<view class="map-view-container">
  <!-- 顶部导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-left" bindtap="navigateBack">
      <custom-icon name="arrow-left" size="20" color="#333" />
    </view>
    
    <view class="navbar-center">
      <text class="navbar-title">旅行地图</text>
    </view>
    
    <view class="navbar-right">
      <view class="nav-action" bindtap="shareMap">
        <custom-icon name="share" size="18" color="#666" />
      </view>
    </view>
  </view>

  <!-- 视图模式切换 -->
  <view class="view-mode-switcher">
    <view class="mode-tabs">
      <view class="mode-tab {{viewMode === 'map' ? 'active' : ''}}" 
            data-mode="map" bindtap="switchViewMode">
        <custom-icon name="map" size="16" color="{{viewMode === 'map' ? '#FF6B6B' : '#999'}}" />
        <text class="tab-text">地图</text>
      </view>
      <view class="mode-tab {{viewMode === 'list' ? 'active' : ''}}" 
            data-mode="list" bindtap="switchViewMode">
        <custom-icon name="list" size="16" color="{{viewMode === 'list' ? '#FF6B6B' : '#999'}}" />
        <text class="tab-text">列表</text>
      </view>
    </view>
    
    <!-- 路线统计 -->
    <view class="route-stats">
      <text class="stats-text">{{attractions.length}}个地点</text>
      <text class="stats-divider">·</text>
      <text class="stats-text">{{routeInfo.totalDistance}}km</text>
      <text wx:if="{{routeInfo.totalTime > 0}}" class="stats-divider">·</text>
      <text wx:if="{{routeInfo.totalTime > 0}}" class="stats-text">{{routeInfo.totalTime}}分钟</text>
    </view>
  </view>

  <!-- 地图模式 -->
  <view wx:if="{{viewMode === 'map'}}" class="map-mode">
    <map
      id="travel-map"
      class="travel-map"
      latitude="{{latitude}}"
      longitude="{{longitude}}"
      scale="{{scale}}"
      markers="{{markers}}"
      polyline="{{polyline}}"
      show-location="{{true}}"
      bindmarkertap="onMarkerTap"
    />
    
    <!-- 地图控制按钮 -->
    <view class="map-controls">
      <view class="control-btn" bindtap="viewFullRoute">
        <custom-icon name="eye" size="16" color="#666" />
        <text class="control-text">全览</text>
      </view>
      <view class="control-btn" bindtap="replanRoute">
        <custom-icon name="refresh" size="16" color="#666" />
        <text class="control-text">优化</text>
      </view>
    </view>
  </view>

  <!-- 列表模式 -->
  <view wx:if="{{viewMode === 'list'}}" class="list-mode">
    <scroll-view class="attractions-list" scroll-y="true">
      <view class="list-header">
        <text class="header-title">行程安排</text>
        <text class="header-subtitle">按游玩顺序排列</text>
      </view>
      
      <view class="attraction-items">
        <view class="attraction-item" 
              wx:for="{{attractions}}" 
              wx:key="id"
              data-attraction="{{item}}"
              bindtap="selectAttraction">
          <view class="item-order">
            <text class="order-number">{{item.order}}</text>
          </view>
          
          <view class="item-content">
            <view class="item-header">
              <text class="attraction-name">{{item.name}}</text>
              <view class="item-actions">
                <custom-icon name="navigation" size="14" color="#4ECDC4" />
              </view>
            </view>
            <text class="attraction-address">{{item.address}}</text>
            <text wx:if="{{item.description}}" class="attraction-desc">{{item.description}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 选中地点详情弹窗 -->
  <view wx:if="{{selectedAttraction}}" class="attraction-detail-modal">
    <view class="modal-mask" bindtap="closeAttractionDetail"></view>
    <view class="detail-dialog">
      <view class="detail-header">
        <view class="header-content">
          <text class="attraction-title">{{selectedAttraction.name}}</text>
          <text class="attraction-order">第{{selectedAttraction.order}}站</text>
        </view>
        <view class="header-close" bindtap="closeAttractionDetail">
          <custom-icon name="close" size="16" color="#999" />
        </view>
      </view>
      
      <view class="detail-body">
        <view class="detail-info">
          <view class="info-row">
            <custom-icon name="location" size="16" color="#666" />
            <text class="info-text">{{selectedAttraction.address}}</text>
          </view>
          <view wx:if="{{selectedAttraction.description}}" class="info-row">
            <custom-icon name="info" size="16" color="#666" />
            <text class="info-text">{{selectedAttraction.description}}</text>
          </view>
        </view>
      </view>
      
      <view class="detail-footer">
        <button class="btn-navigate" bindtap="navigateToLocation">
          <custom-icon name="navigation" size="16" color="#fff" />
          <text class="btn-text">导航前往</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <van-loading type="spinner" color="#FF6B6B" size="32rpx" />
      <text class="loading-text">优化路线中...</text>
    </view>
  </view>
</view>
