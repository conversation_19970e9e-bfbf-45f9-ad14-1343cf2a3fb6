/* subpackages/travel-planning/map-view/index.wxss */
/* 圆周旅记式地图详情页面样式 */

.map-view-container {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.custom-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  z-index: 100;
}

.navbar-left,
.navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
}

.navbar-left {
  justify-content: flex-start;
}

.navbar-right {
  justify-content: flex-end;
}

.navbar-center {
  flex: 1;
  text-align: center;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.nav-action {
  padding: 12rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.nav-action:active {
  background: #f0f0f0;
}

/* 视图模式切换器 */
.view-mode-switcher {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.mode-tabs {
  display: flex;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 4rpx;
}

.mode-tab {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.mode-tab.active {
  background: white;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.15);
}

.tab-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #999;
}

.mode-tab.active .tab-text {
  color: #FF6B6B;
}

.route-stats {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stats-text {
  font-size: 24rpx;
  color: #666;
}

.stats-divider {
  font-size: 20rpx;
  color: #ccc;
}

/* 地图模式 */
.map-mode {
  flex: 1;
  position: relative;
}

.travel-map {
  width: 100%;
  height: 100%;
}

/* 地图控制按钮 */
.map-controls {
  position: absolute;
  bottom: 40rpx;
  right: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.control-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.8);
}

.control-text {
  font-size: 20rpx;
  color: #666;
}

/* 列表模式 */
.list-mode {
  flex: 1;
  background: white;
}

.attractions-list {
  height: 100%;
}

.list-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  color: #999;
}

.attraction-items {
  padding: 0 32rpx;
}

.attraction-item {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f8f9fa;
  transition: all 0.3s ease;
}

.attraction-item:active {
  background: #f8f9fa;
}

.item-order {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 8rpx;
}

.order-number {
  font-size: 24rpx;
  font-weight: 600;
  color: white;
}

.item-content {
  flex: 1;
}

.item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.attraction-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.item-actions {
  padding: 8rpx;
  border-radius: 8rpx;
  background: rgba(78, 205, 196, 0.1);
}

.attraction-address {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.attraction-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 地点详情弹窗 */
.attraction-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.detail-dialog {
  width: 100%;
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  animation: slide-up 0.3s ease-out;
  position: relative;
  z-index: 1;
}

@keyframes slide-up {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-content {
  flex: 1;
}

.attraction-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.attraction-order {
  font-size: 24rpx;
  color: #FF6B6B;
  background: rgba(255, 107, 107, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.header-close {
  padding: 12rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.header-close:active {
  background: #f0f0f0;
}

.detail-body {
  padding: 32rpx;
}

.detail-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.info-text {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.detail-footer {
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.btn-navigate {
  width: 100%;
  background: linear-gradient(135deg, #4ECDC4, #45B7D1);
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 28rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.3s ease;
}

.btn-navigate:active {
  transform: scale(0.98);
}

.btn-text {
  color: white;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}
