// subpackages/travel-planning/map-view/index.js
// 圆周旅记式地图详情页面

const locationService = require('../../../utils/location-service')

Page({
  data: {
    // 地图数据
    mapData: null,
    
    // 地图配置
    latitude: 39.908823,
    longitude: 116.397470,
    scale: 12,
    markers: [],
    polyline: [],
    
    // 视图模式
    viewMode: 'map', // map: 地图模式, list: 列表模式
    
    // 地点列表
    attractions: [],
    
    // 当前选中的地点
    selectedAttraction: null,
    
    // 地图控制
    showControls: true,
    
    // 路线信息
    routeInfo: {
      totalDistance: 0,
      totalTime: 0,
      routeCount: 0
    },
    
    // 加载状态
    loading: false
  },

  onLoad(options) {
    console.log('地图页面加载参数:', options)
    
    if (options.data) {
      try {
        const mapData = JSON.parse(decodeURIComponent(options.data))
        this.initMapData(mapData)
      } catch (error) {
        console.error('解析地图数据失败:', error)
        wx.showToast({
          title: '地图数据错误',
          icon: 'none'
        })
      }
    }
  },

  onReady() {
    // 获取地图上下文
    this.mapCtx = wx.createMapContext('travel-map', this)
  },

  /**
   * 初始化地图数据
   */
  initMapData(mapData) {
    console.log('初始化地图数据:', mapData)
    
    // 处理标记点数据
    const markers = mapData.markers || []
    const processedMarkers = markers.map((marker, index) => ({
      ...marker,
      id: marker.id || index,
      callout: {
        content: marker.title || `地点${index + 1}`,
        color: '#333',
        fontSize: 14,
        borderRadius: 8,
        bgColor: '#fff',
        padding: 8,
        display: 'ALWAYS'
      }
    }))

    // 处理路线数据
    const polyline = mapData.polyline || []
    
    // 提取景点信息
    const attractions = markers.map((marker, index) => ({
      id: marker.id || index,
      name: marker.title || `地点${index + 1}`,
      address: marker.address || '地址未知',
      latitude: marker.latitude,
      longitude: marker.longitude,
      description: marker.description || '',
      order: index + 1
    }))

    // 计算路线统计信息
    const routeInfo = this.calculateRouteInfo(polyline, attractions)

    this.setData({
      mapData: mapData,
      latitude: mapData.center?.latitude || 39.908823,
      longitude: mapData.center?.longitude || 116.397470,
      scale: mapData.scale || 12,
      markers: processedMarkers,
      polyline: polyline,
      attractions: attractions,
      routeInfo: routeInfo
    })
  },

  /**
   * 计算路线统计信息
   */
  calculateRouteInfo(polyline, attractions) {
    let totalDistance = 0
    let totalTime = 0
    const routeCount = polyline.length

    // 简单估算距离和时间
    if (attractions.length > 1) {
      for (let i = 0; i < attractions.length - 1; i++) {
        const distance = this.calculateDistance(
          attractions[i].latitude,
          attractions[i].longitude,
          attractions[i + 1].latitude,
          attractions[i + 1].longitude
        )
        totalDistance += distance
      }
      
      // 估算时间（假设平均速度30km/h）
      totalTime = Math.round(totalDistance / 30 * 60) // 分钟
    }

    return {
      totalDistance: Math.round(totalDistance * 100) / 100, // 保留两位小数
      totalTime: totalTime,
      routeCount: routeCount
    }
  },

  /**
   * 计算两点间距离（公里）
   */
  calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371 // 地球半径（公里）
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLng = (lng2 - lng1) * Math.PI / 180
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  },

  /**
   * 切换视图模式
   */
  switchViewMode(e) {
    const mode = e.currentTarget.dataset.mode
    this.setData({
      viewMode: mode
    })
    
    // 触觉反馈
    wx.vibrateShort({ type: 'light' })
  },

  /**
   * 地图标记点击事件
   */
  onMarkerTap(e) {
    const markerId = e.detail.markerId
    const attraction = this.data.attractions.find(item => item.id == markerId)
    
    if (attraction) {
      this.setData({
        selectedAttraction: attraction
      })
      
      // 移动地图中心到选中的地点
      this.mapCtx.moveToLocation({
        latitude: attraction.latitude,
        longitude: attraction.longitude
      })
    }
  },

  /**
   * 选择景点
   */
  selectAttraction(e) {
    const attraction = e.currentTarget.dataset.attraction
    this.setData({
      selectedAttraction: attraction
    })
    
    // 移动地图中心
    this.mapCtx.moveToLocation({
      latitude: attraction.latitude,
      longitude: attraction.longitude
    })
    
    // 如果在列表模式，切换到地图模式
    if (this.data.viewMode === 'list') {
      this.setData({
        viewMode: 'map'
      })
    }
  },

  /**
   * 导航到选中地点
   */
  navigateToLocation() {
    const { selectedAttraction } = this.data
    if (!selectedAttraction) {
      wx.showToast({
        title: '请先选择地点',
        icon: 'none'
      })
      return
    }

    // 使用腾讯地图导航
    locationService.openRoutePlan({
      endPoint: {
        name: selectedAttraction.name,
        latitude: selectedAttraction.latitude,
        longitude: selectedAttraction.longitude
      },
      navigation: 1
    }).catch(error => {
      console.error('导航失败:', error)
      wx.showToast({
        title: '导航失败',
        icon: 'none'
      })
    })
  },

  /**
   * 查看全部路线
   */
  viewFullRoute() {
    if (this.data.attractions.length < 2) {
      wx.showToast({
        title: '至少需要2个地点才能规划路线',
        icon: 'none'
      })
      return
    }

    // 显示包含所有地点的地图区域
    this.mapCtx.includePoints({
      points: this.data.attractions.map(item => ({
        latitude: item.latitude,
        longitude: item.longitude
      })),
      padding: [50, 50, 50, 50]
    })
  },

  /**
   * 重新规划路线
   */
  replanRoute() {
    wx.showModal({
      title: '重新规划',
      content: '是否要重新规划最优路线？',
      success: (res) => {
        if (res.confirm) {
          this.optimizeRoute()
        }
      }
    })
  },

  /**
   * 优化路线（简单的最近邻算法）
   */
  optimizeRoute() {
    const { attractions } = this.data
    if (attractions.length < 3) return

    this.setData({ loading: true })

    // 简单的路线优化逻辑
    setTimeout(() => {
      // 这里可以调用AI服务进行路线优化
      wx.showToast({
        title: '路线优化完成',
        icon: 'success'
      })
      this.setData({ loading: false })
    }, 2000)
  },

  /**
   * 分享地图
   */
  shareMap() {
    wx.showActionSheet({
      itemList: ['生成长图', '分享给好友', '保存到相册'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.generateMapImage()
            break
          case 1:
            this.shareToFriend()
            break
          case 2:
            this.saveToAlbum()
            break
        }
      }
    })
  },

  /**
   * 生成地图长图
   */
  generateMapImage() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 分享给好友
   */
  shareToFriend() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 保存到相册
   */
  saveToAlbum() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 关闭地点详情
   */
  closeAttractionDetail() {
    this.setData({
      selectedAttraction: null
    })
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack({
      fail: () => {
        // 如果无法返回，则跳转到首页
        wx.switchTab({
          url: '/pages/index/index'
        })
      }
    })
  }
})
