<!--subpackages/travel-planning/index.wxml-->
<view class="travel-container">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="gradient-orb orb-1"></view>
    <view class="gradient-orb orb-2"></view>
    <view class="gradient-orb orb-3"></view>
  </view>

  <!-- 页面内容 -->
  <scroll-view class="content-scroll" scroll-y="true" enhanced="true" show-scrollbar="false">
    
    <!-- 顶部横幅 - 圆周旅记风格 -->
    <view class="hero-section">
      <view class="hero-background">
        <view class="hero-gradient"></view>
        <view class="floating-elements">
          <view class="float-item item-1">✈️</view>
          <view class="float-item item-2">🗺️</view>
          <view class="float-item item-3">📍</view>
          <view class="float-item item-4">🎒</view>
        </view>
      </view>

      <view class="hero-content">
        <view class="hero-title-container">
          <text class="hero-title">发现世界</text>
          <text class="hero-subtitle">每一次旅行都是一个故事</text>
        </view>

        <!-- 快速创建按钮 -->
        <view class="quick-create-container">
          <button class="quick-create-btn" bindtap="quickCreatePlan">
            <view class="btn-content">
              <text class="btn-icon">✨</text>
              <text class="btn-text">开始新旅程</text>
            </view>
            <view class="btn-shine"></view>
          </button>
        </view>
      </view>

      <!-- 统计数据卡片 -->
      <view class="stats-cards" bindlongpress="onLongPressStats" bindtap="manualRefresh">
        <view class="stats-card">
          <view class="card-icon">🎯</view>
          <view class="card-content">
            <text class="card-number">{{analytics.overview.totalPlans}}</text>
            <text class="card-label">旅行计划</text>
          </view>
        </view>
        <view class="stats-card">
          <view class="card-icon">💰</view>
          <view class="card-content">
            <text class="card-number">¥{{analytics.overview.totalExpenses}}</text>
            <text class="card-label">总花费</text>
          </view>
        </view>
        <view class="stats-card">
          <view class="card-icon">📊</view>
          <view class="card-content">
            <text class="card-number">{{analytics.overview.budgetUsage}}%</text>
            <text class="card-label">预算使用</text>
          </view>
        </view>
        <!-- 刷新指示器 -->
        <view class="refresh-indicator" wx:if="{{loading}}">
          <custom-icon name="loading" size="16" color="#4ECDC4" />
        </view>
      </view>
    </view>

    <!-- 当前旅行 - 圆周旅记风格 -->
    <view wx:if="{{currentPlan}}" class="current-journey-section">
      <view class="section-header-modern">
        <view class="header-left">
          <view class="section-icon-container">
            <text class="section-emoji">🗺️</text>
          </view>
          <view class="header-text">
            <text class="section-title-modern">当前旅程</text>
            <text class="section-subtitle">正在进行的精彩之旅</text>
          </view>
        </view>
        <view class="header-action" bindtap="viewCurrentPlan">
          <text class="action-text">详情</text>
          <custom-icon name="arrow-right" size="14" color="#999" />
        </view>
      </view>

      <view class="journey-card" bindtap="viewCurrentPlan">
        <!-- 卡片背景装饰 -->
        <view class="card-decoration">
          <view class="decoration-circle circle-1"></view>
          <view class="decoration-circle circle-2"></view>
          <view class="decoration-line"></view>
        </view>

        <view class="journey-header">
          <view class="journey-info">
            <text class="journey-title">{{currentPlan.title}}</text>
            <view class="journey-location">
              <text class="location-icon">📍</text>
              <text class="location-text">{{currentPlan.destination}}</text>
            </view>
          </view>
          <view class="journey-status status-{{currentPlan.status}}">
            <view class="status-dot"></view>
            <text class="status-text">{{currentPlan.statusText}}</text>
          </view>
        </view>

        <!-- 进度环形图 -->
        <view class="progress-rings">
          <view class="ring-container">
            <view class="progress-ring">
              <view class="ring-background"></view>
              <view class="ring-progress" style="--progress: {{currentPlan.progressPercent}}%"></view>
            </view>
            <view class="ring-content">
              <text class="ring-number">{{currentPlan.dayProgress}}</text>
              <text class="ring-total">/{{currentPlan.totalDays}}</text>
              <text class="ring-label">天</text>
            </view>
          </view>

          <view class="ring-container">
            <view class="progress-ring budget-ring">
              <view class="ring-background"></view>
              <view class="ring-progress" style="--progress: {{currentPlan.budgetPercent}}%"></view>
            </view>
            <view class="ring-content">
              <text class="ring-number">{{currentPlan.budgetPercent}}</text>
              <text class="ring-unit">%</text>
              <text class="ring-label">预算</text>
            </view>
          </view>
        </view>

        <!-- 快速操作 -->
        <view class="journey-actions">
          <view class="action-item" bindtap="recordExpense" data-plan="{{currentPlan}}">
            <view class="action-icon expense-icon">
              <text class="icon-emoji">💰</text>
            </view>
            <text class="action-label">记账</text>
          </view>
          <view class="action-item" bindtap="viewItinerary" data-plan="{{currentPlan}}">
            <view class="action-icon itinerary-icon">
              <text class="icon-emoji">📋</text>
            </view>
            <text class="action-label">行程</text>
          </view>
          <view class="action-item" bindtap="viewMap" data-plan="{{currentPlan}}">
            <view class="action-icon map-icon">
              <text class="icon-emoji">🗺️</text>
            </view>
            <text class="action-label">地图</text>
          </view>
        </view>
      </view>
    </view>


    <!-- 创建旅程 - 圆周旅记风格 -->
    <view class="create-journey-section">
      <view class="section-header-modern">
        <view class="header-left">
          <view class="section-icon-container">
            <text class="section-emoji">✨</text>
          </view>
          <view class="header-text">
            <text class="section-title-modern">创建旅程</text>
            <text class="section-subtitle">选择你的规划方式</text>
          </view>
        </view>
      </view>

      <view class="create-options">
        <!-- 魔法解析 - 主推功能 -->
        <view class="create-option-main" bindtap="createPlanWithMagic">
          <view class="option-background">
            <view class="magic-particles">
              <view class="particle particle-1">✨</view>
              <view class="particle particle-2">⭐</view>
              <view class="particle particle-3">💫</view>
              <view class="particle particle-4">🌟</view>
            </view>
          </view>
          <view class="option-content">
            <view class="option-header">
              <text class="option-badge">推荐</text>
              <text class="option-title">魔法解析</text>
            </view>
            <text class="option-desc">复制小红书链接，一键生成专属攻略</text>
            <view class="option-features">
              <text class="feature-tag">小红书</text>
              <text class="feature-tag">微信公众号</text>
              <text class="feature-tag">AI智能</text>
            </view>
          </view>
          <view class="option-arrow">
            <custom-icon name="arrow-right" size="16" color="#fff" />
          </view>
        </view>

        <!-- 其他创建方式 -->
        <view class="create-options-grid">
          <view class="create-option-small" bindtap="createPlanWithAI">
            <view class="small-option-icon ai-icon">
              <text class="icon-emoji">🎯</text>
            </view>
            <view class="small-option-content">
              <text class="small-option-title">AI规划</text>
              <text class="small-option-desc">智能生成行程</text>
            </view>
          </view>

          <view class="create-option-small" bindtap="createPlanManual">
            <view class="small-option-icon manual-icon">
              <text class="icon-emoji">✏️</text>
            </view>
            <view class="small-option-content">
              <text class="small-option-title">手动创建</text>
              <text class="small-option-desc">自由规划行程</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 协作功能 -->
    <view class="collaboration-section">
      <view class="section-header-modern">
        <view class="header-left">
          <view class="section-icon-container">
            <text class="section-emoji">👥</text>
          </view>
          <view class="header-text">
            <text class="section-title-modern">协作旅行</text>
            <text class="section-subtitle">与朋友一起规划</text>
          </view>
        </view>
      </view>

      <view class="collaboration-options">
        <view class="collab-option" bindtap="joinPlan">
          <view class="collab-icon join-icon">
            <text class="icon-emoji">🔗</text>
          </view>
          <view class="collab-content">
            <text class="collab-title">加入计划</text>
            <text class="collab-desc">输入邀请码加入朋友的旅行</text>
          </view>
          <custom-icon name="arrow-right" size="14" color="#999" />
        </view>

        <view class="collab-option" bindtap="inviteCollaboration">
          <view class="collab-icon invite-icon">
            <text class="icon-emoji">💌</text>
          </view>
          <view class="collab-content">
            <text class="collab-title">邀请协作</text>
            <text class="collab-desc">邀请好友一起规划旅程</text>
          </view>
          <custom-icon name="arrow-right" size="14" color="#999" />
        </view>
      </view>
    </view>

    <!-- 我的计划 -->
    <view class="my-plans-section">
      <view class="section-header">
        <view class="section-title">
          <custom-icon name="bookmark" size="20" color="#722ed1" />
          <text>我的计划</text>
        </view>
        <view class="section-action" bindtap="viewAllPlans">
          <text>查看全部</text>
          <custom-icon name="arrow-right" size="16" color="#636e72" />
        </view>
      </view>
      
      <view wx:if="{{plans.length === 0}}" class="empty-state">
        <view class="empty-icon">
          <custom-icon name="suitcase" size="64" color="#b2bec3" />
        </view>
        <text class="empty-title">还没有旅行计划</text>
        <text class="empty-desc">创建你的第一个旅行计划吧</text>
        <button class="empty-action" bindtap="createNewPlan">
          <custom-icon name="plus" size="16" color="#fff" />
          <text>创建计划</text>
        </button>
      </view>
      
      <view wx:else class="plans-list">
        <view 
          wx:for="{{plans}}" 
          wx:key="id" 
          class="plan-card" 
          bindtap="viewPlanDetail" 
          data-plan="{{item}}"
        >
          <view class="plan-card-header">
            <view class="plan-card-info">
              <text class="plan-card-title">{{item.title}}</text>
              <view class="plan-card-meta">
                <custom-icon name="map-pin" size="12" color="#4ECDC4" />
                <text class="plan-card-destination">{{item.destination}}</text>
                <text class="plan-card-date">{{item.dateRange}}</text>
              </view>
            </view>
            <!-- {{ AURA-X: Add - 添加用户角色显示. Approval: 寸止(ID:1738056000). }} -->
            <view class="plan-card-badges">
              <view class="plan-card-status status-{{item.status}}">
                <text>{{item.statusText}}</text>
              </view>
              <view wx:if="{{item.userRole === 'collaborator'}}" class="plan-card-role role-collaborator">
                <text>协作者</text>
              </view>
            </view>
          </view>
          
          <view class="plan-card-content">
            <view class="plan-card-stats">
              <view class="card-stat-item">
                <custom-icon name="calendar" size="14" color="#636e72" />
                <text>{{item.duration}}天</text>
              </view>
              <view class="card-stat-item">
                <custom-icon name="wallet" size="14" color="#636e72" />
                <text>¥{{item.budget}}</text>
              </view>
              <view class="card-stat-item">
                <custom-icon name="user" size="14" color="#636e72" />
                <text>{{item.participants}}人</text>
              </view>
            </view>
            
            <view wx:if="{{item.status === 'ongoing'}}" class="plan-card-progress">
              <view class="card-progress-bar">
                <view class="card-progress-fill" style="width: {{item.progressPercent}}%"></view>
              </view>
              <text class="card-progress-text">进度 {{item.progressPercent}}%</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部安全距离 -->
    <view class="safe-bottom"></view>
  </scroll-view>

  <!-- 计划选择弹窗 -->
  <view wx:if="{{showPlanSelector}}" class="plan-selector-overlay" bindtap="closePlanSelector">
    <view class="plan-selector-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">选择旅行计划</text>
        <view class="modal-close" bindtap="closePlanSelector">
          <custom-icon name="close" size="20" color="#636e72" />
        </view>
      </view>

      <view class="modal-content">
        <view
          wx:for="{{availablePlans}}"
          wx:key="id"
          class="plan-option"
          bindtap="selectPlanForRecord"
          data-plan-id="{{item.id}}"
        >
          <view class="plan-option-info">
            <text class="plan-option-title">{{item.title}}</text>
            <view class="plan-option-meta">
              <custom-icon name="map-pin" size="14" color="#4ECDC4" />
              <text class="plan-option-destination">{{item.destination}}</text>
            </view>
          </view>
          <view class="plan-option-arrow">
            <custom-icon name="arrow-right" size="16" color="#b2bec3" />
          </view>
        </view>
      </view>
    </view>
  </view>

</view>
