
import cacheManager, { CACHE_KEYS, CACHE_TTL } from './cache-manager.js'
import storage from './storage.js'

class SyncManager {
  constructor() {
    // 同步状态
    this.syncStatus = {
      isOnline: true,
      lastSyncTime: 0,
      syncInProgress: false,
      pendingOperations: []
    }
    
    // 数据版本管理
    this.dataVersions = {
      financial: 0,
      travel: 0,
      user: 0
    }
    
    // 离线数据队列
    this.offlineQueue = []

    // 并发控制锁
    this.syncLock = false

    // 初始化
    this.init()
  }

  /**
   * 初始化同步管理器
   */
  async init() {
    // 检查网络状态
    this.checkNetworkStatus()
    
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      this.syncStatus.isOnline = res.isConnected
      if (res.isConnected) {
        this.processOfflineQueue()
      }
    })
    
    // 加载本地数据版本
    await this.loadDataVersions()
  }

  /**
   * 检查网络状态
   */
  checkNetworkStatus() {
    wx.getNetworkType({
      success: (res) => {
        this.syncStatus.isOnline = res.networkType !== 'none'
      }
    })
  }

  /**
   * 批量数据同步 - 核心优化方法
   * @param {Array} syncTasks 同步任务列表
   * @returns {Promise} 同步结果
   */
  async batchSync(syncTasks) {
    // 使用简单的锁机制防止并发
    if (this.syncLock) {
      console.warn('同步正在进行中，等待完成')
      await this.waitForSyncComplete()
    }

    this.syncLock = true
    this.syncStatus.syncInProgress = true

    try {
      // 检查网络状态
      if (!this.syncStatus.isOnline) {
        return this.handleOfflineSync(syncTasks)
      }

      // 并行执行同步任务
      const results = await Promise.allSettled(
        syncTasks.map(task => this.executeSyncTask(task))
      )

      // 处理同步结果
      const successCount = results.filter(r => r.status === 'fulfilled').length
      const totalCount = results.length

      this.syncStatus.lastSyncTime = Date.now()

      console.log(`批量同步完成: ${successCount}/${totalCount}`)

      return {
        success: successCount === totalCount,
        successCount,
        totalCount,
        results
      }

    } catch (error) {
      console.error('批量同步失败:', error)
      return { success: false, error: error.message }
    } finally {
      this.syncStatus.syncInProgress = false
      this.syncLock = false
    }
  }

  // 添加等待同步完成的方法
  async waitForSyncComplete() {
    let attempts = 0
    const maxAttempts = 30 // 最多等待30秒

    while (this.syncLock && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 1000))
      attempts++
    }

    if (attempts >= maxAttempts) {
      console.warn('等待同步完成超时')
    }
  }

  /**
   * 执行单个同步任务
   * @param {Object} task 同步任务
   */
  async executeSyncTask(task) {
    const { type, action, data, cacheKey, ttl, cloudFunction } = task

    try {
      // 检查是否需要增量同步
      const taskData = { ...data }
      if (task.incremental) {
        const lastVersion = this.dataVersions[type] || 0
        taskData.lastVersion = lastVersion
      }

      // 执行云函数调用
      const result = await wx.cloud.callFunction({
        name: cloudFunction || type,
        data: {
          action,
          data: taskData
        }
      })

      if (result.result && result.result.success) {
        // 更新缓存
        if (cacheKey && result.result.data) {
          cacheManager.set(cacheKey, result.result.data, ttl || CACHE_TTL.MEDIUM)
        }

        // 更新数据版本
        if (result.result.version) {
          this.dataVersions[type] = result.result.version
          this.saveDataVersions()
        }

        return result.result
      } else {
        const errorMsg = result.result?.message || '云函数返回失败'
        throw new Error(errorMsg)
      }

    } catch (error) {
      throw error
    }
  }

  /**
   * 处理离线同步
   * @param {Array} syncTasks 同步任务
   */
  handleOfflineSync(syncTasks) {
    // 将任务添加到离线队列
    this.offlineQueue.push(...syncTasks)
    
    // 尝试从缓存获取数据
    const cachedResults = syncTasks.map(task => {
      if (task.cacheKey) {
        const cachedData = cacheManager.get(task.cacheKey)
        if (cachedData) {
          return { success: true, data: cachedData, fromCache: true }
        }
      }
      return { success: false, message: '离线状态，无缓存数据' }
    })

    return {
      success: false,
      offline: true,
      results: cachedResults,
      message: '当前离线，已缓存操作'
    }
  }

  /**
   * 处理离线队列
   */
  async processOfflineQueue() {
    if (this.offlineQueue.length === 0) return

    
    const tasks = [...this.offlineQueue]
    this.offlineQueue = []
    
    try {
      await this.batchSync(tasks)
    } catch (error) {
      console.error('离线队列处理失败:', error)
      // 重新加入队列
      this.offlineQueue.unshift(...tasks)
    }
  }

  /**
   * 数据预加载
   * @param {Array} preloadTasks 预加载任务
   */
  async preloadData(preloadTasks) {
    
    // 后台执行，不阻塞主流程
    setTimeout(async () => {
      try {
        await this.batchSync(preloadTasks)
      } catch (error) {
        console.error('数据预加载失败:', error)
      }
    }, 100)
  }

  /**
   * 智能同步策略
   * @param {string} dataType 数据类型
   * @param {Object} options 选项
   */
  async smartSync(dataType, options = {}) {
    try {
      const { forceRefresh = false, priority = 'normal' } = options

      // 创建同步任务获取正确的缓存键
      const syncTask = this.createSyncTask(dataType, options)
      const cacheKey = syncTask.cacheKey

      if (!forceRefresh && cacheKey && cacheManager.has(cacheKey)) {
        return { success: true, fromCache: true, data: cacheManager.get(cacheKey) }
      }

      // 如果同步正在进行中，等待完成而不是直接拒绝
      if (this.syncStatus.syncInProgress) {
        await this.waitForSyncComplete()

        // 再次检查缓存
        if (cacheKey && cacheManager.has(cacheKey)) {
          return { success: true, fromCache: true, data: cacheManager.get(cacheKey) }
        }
      }

      if (priority === 'high') {
        // 高优先级立即同步
        const result = await this.batchSync([syncTask])

        if (result.success && result.results && result.results[0]) {
          const taskResult = result.results[0]
          if (taskResult.status === 'fulfilled') {
            return taskResult.value
          } else {
            throw new Error(taskResult.reason?.message || '同步任务失败')
          }
        } else {
          throw new Error(result.message || '批量同步失败')
        }
      } else {
        // 普通优先级加入批量队列
        this.addToBatchQueue(syncTask)
        return { success: true, queued: true }
      }

    } catch (error) {
      console.error(`智能同步失败 [${dataType}]:`, error)
      return {
        success: false,
        message: error.message || '智能同步失败'
      }
    }
  }

  /**
   * 等待同步完成
   */
  async waitForSyncComplete() {
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (!this.syncStatus.syncInProgress) {
          clearInterval(checkInterval)
          resolve()
        }
      }, 100) // 每100ms检查一次

      // 最多等待5秒
      setTimeout(() => {
        clearInterval(checkInterval)
        resolve()
      }, 5000)
    })
  }

  /**
   * 创建同步任务
   * @param {string} dataType 数据类型
   * @param {Object} options 选项
   */
  createSyncTask(dataType, options) {
    const taskMap = {
      financial: {
        type: 'expense',
        cloudFunction: 'expense',
        action: 'getFinancialOverview',
        cacheKey: CACHE_KEYS.FINANCIAL_OVERVIEW,
        ttl: CACHE_TTL.MEDIUM,
        incremental: true
      },
      travel: {
        type: 'travel',
        cloudFunction: 'travel',
        action: 'getTravelData',
        cacheKey: CACHE_KEYS.TRAVEL_STATISTICS,
        ttl: CACHE_TTL.MEDIUM,
        incremental: true
      },
      user: {
        type: 'user',
        cloudFunction: 'user',
        action: 'getUserStats',
        cacheKey: CACHE_KEYS.USER_STATS,
        ttl: CACHE_TTL.LONG,
        incremental: false
      }
    }

    const baseTask = taskMap[dataType]
    if (!baseTask) {
      throw new Error(`未知的数据类型: ${dataType}`)
    }

    return {
      ...baseTask,
      ...options
    }
  }

  /**
   * 加载数据版本
   */
  async loadDataVersions() {
    try {
      const versions = await storage.get('dataVersions')
      if (versions) {
        this.dataVersions = { ...this.dataVersions, ...versions }
      }
    } catch (error) {
      console.error('加载数据版本失败:', error)
    }
  }

  /**
   * 保存数据版本
   */
  async saveDataVersions() {
    try {
      storage.set('dataVersions', this.dataVersions)
    } catch (error) {
      console.error('保存数据版本失败:', error)
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      ...this.syncStatus,
      offlineQueueSize: this.offlineQueue.length,
      dataVersions: this.dataVersions
    }
  }

  /**
   * 清理过期数据
   */
  cleanup() {
    // 清理过期的离线队列
    const now = Date.now()
    this.offlineQueue = this.offlineQueue.filter(task => {
      return !task.timestamp || (now - task.timestamp) < 24 * 60 * 60 * 1000 // 24小时
    })
  }
}

// 创建全局同步管理器实例
const syncManager = new SyncManager()

export default syncManager
