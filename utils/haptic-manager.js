/**
 * 统一触觉反馈管理器 v2.0
 * 基于用户体验最佳实践的触觉反馈系统
 */

class HapticManager {
  static instance = null;
  
  constructor() {
    this.isEnabled = true;
    this.lastFeedbackTime = 0;
    this.minInterval = 50; // 最小间隔50ms，防止过度反馈
    
    // 检查设备支持
    this.checkSupport();
  }
  
  static getInstance() {
    if (!this.instance) {
      this.instance = new HapticManager();
    }
    return this.instance;
  }
  
  // 检查设备支持
  checkSupport() {
    this.isSupported = wx.canIUse('vibrateShort');
    if (!this.isSupported) {
      console.warn('当前设备不支持触觉反馈');
    }
  }
  
  // 防抖处理
  shouldTrigger() {
    const now = Date.now();
    if (now - this.lastFeedbackTime < this.minInterval) {
      return false;
    }
    this.lastFeedbackTime = now;
    return true;
  }
  
  // 轻触反馈 - 用于一般点击、滑动
  light() {
    if (!this.isEnabled || !this.isSupported || !this.shouldTrigger()) {
      return;
    }
    
    wx.vibrateShort({ 
      type: 'light',
      success: () => {},
      fail: () => {}
    });
  }
  
  // 中等反馈 - 用于重要操作、切换
  medium() {
    if (!this.isEnabled || !this.isSupported || !this.shouldTrigger()) {
      return;
    }
    
    wx.vibrateShort({ 
      type: 'medium',
      success: () => {},
      fail: () => {}
    });
  }
  
  // 重触反馈 - 用于关键操作、错误
  heavy() {
    if (!this.isEnabled || !this.isSupported || !this.shouldTrigger()) {
      return;
    }
    
    wx.vibrateShort({ 
      type: 'heavy',
      success: () => {},
      fail: () => {}
    });
  }
  
  // 成功反馈 - 操作成功
  success() {
    this.medium();
  }
  
  // 错误反馈 - 操作失败
  error() {
    this.heavy();
  }
  
  // 警告反馈 - 警告提示
  warning() {
    this.medium();
  }
  
  // 选择反馈 - 选择操作
  selection() {
    this.light();
  }
  
  // 长按反馈 - 长按操作
  longPress() {
    this.medium();
  }
  
  // 刷新反馈 - 下拉刷新
  refresh() {
    this.light();
  }
  
  // 导航反馈 - 页面跳转
  navigation() {
    this.light();
  }
  
  // 启用/禁用触觉反馈
  setEnabled(enabled) {
    this.isEnabled = enabled;
  }
  
  // 获取启用状态
  getEnabled() {
    return this.isEnabled && this.isSupported;
  }
  
  // 批量触发反馈（用于复杂交互）
  sequence(feedbacks, interval = 100) {
    if (!this.isEnabled || !this.isSupported) {
      return;
    }
    
    feedbacks.forEach((feedback, index) => {
      setTimeout(() => {
        if (typeof this[feedback] === 'function') {
          this[feedback]();
        }
      }, index * interval);
    });
  }
  
  // 自定义强度反馈
  custom(type = 'light', duration = 15) {
    if (!this.isEnabled || !this.isSupported || !this.shouldTrigger()) {
      return;
    }
    
    // 对于支持自定义时长的设备
    if (wx.canIUse('vibrateLong')) {
      wx.vibrate({
        duration: duration,
        success: () => {},
        fail: () => {
          // 降级到标准反馈
          this[type] && this[type]();
        }
      });
    } else {
      // 降级到标准反馈
      this[type] && this[type]();
    }
  }
  
  // 获取反馈统计信息
  getStats() {
    return {
      isEnabled: this.isEnabled,
      isSupported: this.isSupported,
      lastFeedbackTime: this.lastFeedbackTime,
      minInterval: this.minInterval
    };
  }
  
  // 重置统计信息
  resetStats() {
    this.lastFeedbackTime = 0;
  }
}

// 导出单例实例
export default HapticManager.getInstance();
