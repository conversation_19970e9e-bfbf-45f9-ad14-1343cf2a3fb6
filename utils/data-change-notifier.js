/**
 * 数据变更通知管理器
 * 实现首页数据实时更新机制
 */

import cacheManager, { CACHE_KEYS } from './cache-manager.js'

class DataChangeNotifier {
  constructor() {
    // 监听器注册表
    this.listeners = new Map()
    
    // 数据变更队列
    this.changeQueue = []
    
    // 防抖定时器
    this.debounceTimer = null
    
    // 配置
    this.config = {
      debounceDelay: 500, // 防抖延迟500ms
      batchSize: 10       // 批量处理大小
    }
  }

  /**
   * 注册数据变更监听器
   * @param {string} dataType 数据类型
   * @param {function} callback 回调函数
   * @param {object} context 上下文对象
   */
  addListener(dataType, callback, context = null) {
    if (!this.listeners.has(dataType)) {
      this.listeners.set(dataType, [])
    }
    
    this.listeners.get(dataType).push({
      callback,
      context,
      id: Date.now() + Math.random()
    })
  }

  /**
   * 移除数据变更监听器
   * @param {string} dataType 数据类型
   * @param {object} context 上下文对象
   */
  removeListener(dataType, context) {
    if (!this.listeners.has(dataType)) return
    
    const listeners = this.listeners.get(dataType)
    const filteredListeners = listeners.filter(listener => listener.context !== context)
    
    if (filteredListeners.length === 0) {
      this.listeners.delete(dataType)
    } else {
      this.listeners.set(dataType, filteredListeners)
    }
  }

  /**
   * 通知数据变更
   * @param {string} dataType 数据类型
   * @param {object} changeData 变更数据
   * @param {object} options 选项
   */
  notifyChange(dataType, changeData = {}, options = {}) {
    const { immediate = false, invalidateCache = true } = options
    
    
    // 添加到变更队列
    this.changeQueue.push({
      dataType,
      changeData,
      timestamp: Date.now(),
      immediate
    })
    
    // 清除相关缓存
    if (invalidateCache) {
      this.invalidateRelatedCache(dataType)
    }
    
    // 立即处理或防抖处理
    if (immediate) {
      this.processChangeQueue()
    } else {
      this.debouncedProcess()
    }
  }

  /**
   * 防抖处理变更队列
   */
  debouncedProcess() {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
    }
    
    this.debounceTimer = setTimeout(() => {
      this.processChangeQueue()
    }, this.config.debounceDelay)
  }

  /**
   * 处理变更队列
   */
  processChangeQueue() {
    if (this.changeQueue.length === 0) return
    
    
    // 按数据类型分组
    const groupedChanges = this.groupChangesByType()
    
    // 处理每种数据类型的变更
    for (const [dataType, changes] of groupedChanges.entries()) {
      this.notifyListeners(dataType, changes)
    }
    
    // 清空队列
    this.changeQueue = []
    this.debounceTimer = null
  }

  /**
   * 按数据类型分组变更
   */
  groupChangesByType() {
    const grouped = new Map()
    
    this.changeQueue.forEach(change => {
      if (!grouped.has(change.dataType)) {
        grouped.set(change.dataType, [])
      }
      grouped.get(change.dataType).push(change)
    })
    
    return grouped
  }

  /**
   * 通知监听器
   * @param {string} dataType 数据类型
   * @param {Array} changes 变更列表
   */
  notifyListeners(dataType, changes) {
    const listeners = this.listeners.get(dataType)
    if (!listeners || listeners.length === 0) return
    
    
    listeners.forEach(listener => {
      try {
        if (listener.context && listener.context.setData) {
          // 页面上下文，使用setData
          listener.callback.call(listener.context, changes)
        } else {
          // 普通回调
          listener.callback(changes)
        }
      } catch (error) {
        console.error(`监听器回调执行失败 [${dataType}]:`, error)
      }
    })
  }

  /**
   * 清除相关缓存
   * @param {string} dataType 数据类型
   */
  invalidateRelatedCache(dataType) {
    const cacheInvalidationMap = {
      'expense': [CACHE_KEYS.FINANCIAL_OVERVIEW, CACHE_KEYS.EXPENSE_RECORDS],
      'budget': [CACHE_KEYS.FINANCIAL_OVERVIEW, CACHE_KEYS.USER_BUDGET],
      'travel': [CACHE_KEYS.TRAVEL_STATISTICS, CACHE_KEYS.TRAVEL_PLANS, CACHE_KEYS.CURRENT_PLAN],
      'user': [CACHE_KEYS.USER_STATS, CACHE_KEYS.USER_INFO]
    }
    
    const keysToInvalidate = cacheInvalidationMap[dataType] || []
    
    keysToInvalidate.forEach(key => {
      cacheManager.invalidateByPrefix(key)
    })
    
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      listenersCount: Array.from(this.listeners.values()).reduce((sum, listeners) => sum + listeners.length, 0),
      queueSize: this.changeQueue.length,
      dataTypes: Array.from(this.listeners.keys())
    }
  }

  /**
   * 清理所有监听器
   */
  cleanup() {
    this.listeners.clear()
    this.changeQueue = []
    
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
      this.debounceTimer = null
    }
  }

  /**
   * 页面卸载时的清理
   * @param {object} pageInstance 页面实例
   */
  cleanupPage(pageInstance) {
    // 移除该页面的所有监听器
    for (const [dataType, listeners] of this.listeners.entries()) {
      this.removeListener(dataType, pageInstance)
    }
  }
}

// 创建全局实例
const dataChangeNotifier = new DataChangeNotifier()

// 预定义的数据变更类型
export const DATA_CHANGE_TYPES = {
  EXPENSE_ADDED: 'expense',
  EXPENSE_UPDATED: 'expense',
  EXPENSE_DELETED: 'expense',
  BUDGET_UPDATED: 'budget',
  TRAVEL_PLAN_ADDED: 'travel',
  TRAVEL_PLAN_UPDATED: 'travel',
  TRAVEL_PLAN_DELETED: 'travel',
  USER_PROFILE_UPDATED: 'user'
}

// 便捷方法
export const notifyExpenseChange = (changeData, options) => {
  dataChangeNotifier.notifyChange(DATA_CHANGE_TYPES.EXPENSE_ADDED, changeData, options)
}

export const notifyBudgetChange = (changeData, options) => {
  dataChangeNotifier.notifyChange(DATA_CHANGE_TYPES.BUDGET_UPDATED, changeData, options)
}

export const notifyTravelChange = (changeData, options) => {
  dataChangeNotifier.notifyChange(DATA_CHANGE_TYPES.TRAVEL_PLAN_ADDED, changeData, options)
}

export const notifyUserChange = (changeData, options) => {
  dataChangeNotifier.notifyChange(DATA_CHANGE_TYPES.USER_PROFILE_UPDATED, changeData, options)
}

export default dataChangeNotifier
