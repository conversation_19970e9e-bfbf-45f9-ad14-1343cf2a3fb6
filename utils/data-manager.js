/**
 * 统一数据管理器
 * 所有数据访问的唯一入口，提供统一的缓存、验证和同步策略
 */

import auth from './auth.js'

// 数据库集合定义
const COLLECTIONS = {
  USERS: 'users',
  RECORDS: 'expense_records',
  TRAVEL_PLANS: 'travel_plans',
  CATEGORIES: 'categories',
  USER_BUDGETS: 'user_budgets'
}

// 缓存键定义
const CACHE_KEYS = {
  USER_INFO: 'user_info',
  USER_BUDGET: 'user_budget',
  FINANCIAL_OVERVIEW: 'financial_overview',
  TRAVEL_STATISTICS: 'travel_statistics',
  TRAVEL_PLANS: 'travel_plans',
  CATEGORIES: 'categories'
}

// 缓存TTL定义（毫秒）
const CACHE_TTL = {
  SHORT: 2 * 60 * 1000,    // 2分钟
  MEDIUM: 10 * 60 * 1000,  // 10分钟
  LONG: 30 * 60 * 1000     // 30分钟
}

class DataManager {
  constructor() {
    this.db = null
    this.cache = new Map()
    this.initialized = false
  }

  /**
   * 初始化数据管理器
   */
  async initialize() {
    if (this.initialized) return true

    try {
      // 初始化云数据库
      if (wx.cloud) {
        this.db = wx.cloud.database()
        this._ = this.db.command
      }

      this.initialized = true
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 验证用户身份
   */
  validateUser() {
    if (!auth.isLoggedIn()) {
      throw new Error('用户未登录')
    }
    return auth.getUserInfo()
  }

  // ==================== 缓存管理 ====================

  /**
   * 设置缓存
   */
  setCache(key, data, ttl = CACHE_TTL.MEDIUM) {
    try {
      this.cache.set(key, {
        data,
        timestamp: Date.now(),
        ttl
      })
      console.log(`缓存设置成功: ${key}`)
    } catch (error) {
      console.error('缓存设置失败:', error, { key, dataType: typeof data })
      // 尝试清理部分缓存后重试
      if (this.cache.size > 50) {
        this.clearExpiredCache()
        try {
          this.cache.set(key, { data, timestamp: Date.now(), ttl })
          console.log(`缓存重试成功: ${key}`)
        } catch (retryError) {
          console.error('缓存重试仍失败:', retryError)
        }
      }
    }
  }

  /**
   * 清理过期缓存
   */
  clearExpiredCache() {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * 获取缓存
   */
  getCache(key) {
    try {
      const item = this.cache.get(key)
      if (!item) return null

      // 检查是否过期
      if (Date.now() - item.timestamp > item.ttl) {
        this.cache.delete(key)
        return null
      }

      return item.data
    } catch (error) {
      return null
    }
  }

  /**
   * 清除缓存（统一缓存清理入口）
   */
  clearCache(pattern = null) {
    try {
      if (pattern) {
        // 清除匹配模式的缓存
        for (const key of this.cache.keys()) {
          if (key.includes(pattern)) {
            this.cache.delete(key)
          }
        }
      } else {
        // 清除所有缓存
        this.cache.clear()
      }

      // 同步清理其他缓存系统
      this.syncClearOtherCaches(pattern)
    } catch (error) {
      console.error('缓存清理失败:', error)
    }
  }

  /**
   * 同步清理其他缓存系统
   */
  syncClearOtherCaches(pattern = null) {
    try {
      // 清理cache-manager的缓存
      const cacheManager = require('./cache-manager.js').default
      if (cacheManager) {
        if (pattern) {
          cacheManager.clearByPattern(pattern)
        } else {
          cacheManager.clear()
        }
      }

      // 清理homepage-travel-sync的缓存
      const homepageTravelSync = require('./homepage-travel-sync.js').default
      if (homepageTravelSync) {
        homepageTravelSync.clearCache(pattern)
      }

      // 清理本地存储中的相关数据
      if (pattern) {
        const keysToClean = [
          `${pattern}_data`,
          `${pattern}_statistics`,
          `${pattern}_overview`
        ]
        keysToClean.forEach(key => {
          try {
            wx.removeStorageSync(key)
          } catch (error) {
            // 静默处理
          }
        })
      }
    } catch (error) {
      console.error('同步清理其他缓存失败:', error)
    }
  }

  // ==================== 云函数调用 ====================

  /**
   * 统一云函数调用
   */
  async callFunction(name, action, data = {}, options = {}) {
    try {
      const { useCache = true, cacheKey, cacheTTL = CACHE_TTL.MEDIUM, forceRefresh = false } = options

      // 检查缓存
      if (useCache && cacheKey && !forceRefresh) {
        const cached = this.getCache(cacheKey)
        if (cached) {
          return { success: true, data: cached, fromCache: true }
        }
      }

      // 调用云函数
      const result = await wx.cloud.callFunction({
        name,
        data: { action, data }
      })

      if (result.result && result.result.success) {
        // 缓存结果
        if (useCache && cacheKey) {
          this.setCache(cacheKey, result.result.data, cacheTTL)
        }

        return result.result
      } else {
        throw new Error(result.result?.message || '云函数调用失败')
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || '数据获取失败'
      }
    }
  }

  // ==================== 用户数据 ====================

  /**
   * 获取用户信息
   */
  async getUserInfo(forceRefresh = false) {
    return await this.callFunction('user', 'getUserInfo', {}, {
      cacheKey: CACHE_KEYS.USER_INFO,
      cacheTTL: CACHE_TTL.LONG,
      forceRefresh
    })
  }

  /**
   * 更新用户信息
   */
  async updateUserInfo(userData) {
    const result = await this.callFunction('user', 'updateUserInfo', userData, {
      useCache: false
    })

    if (result.success) {
      // 清除用户相关缓存
      this.clearCache('user')
    }

    return result
  }

  // ==================== 财务数据 ====================

  /**
   * 获取财务概览 - 使用云函数获取真实预算数据（本月数据）
   */
  async getFinancialOverview(forceRefresh = false) {
    try {
      // 直接调用云函数获取财务概览，包含真实预算数据
      const result = await this.callFunction('expense', 'getFinancialOverview', {}, {
        cacheKey: CACHE_KEYS.FINANCIAL_OVERVIEW,
        cacheTTL: CACHE_TTL.SHORT,
        forceRefresh
      })

      return result

    } catch (error) {
      return {
        success: false,
        message: error.message || '获取财务数据失败'
      }
    }
  }

  /**
   * 获取历史财务概览 - 支持不同时间范围
   */
  async getHistoricalFinancialOverview(timeRange = 'all', forceRefresh = false) {
    try {
      const cacheKey = `${CACHE_KEYS.FINANCIAL_OVERVIEW}_historical_${timeRange}`

      const result = await this.callFunction('expense', 'getHistoricalFinancialOverview', { timeRange }, {
        cacheKey: cacheKey,
        cacheTTL: CACHE_TTL.MEDIUM, // 历史数据可以缓存更久
        forceRefresh
      })

      return result

    } catch (error) {
      return {
        success: false,
        message: error.message || '获取历史财务数据失败'
      }
    }
  }

  /**
   * 添加支出记录
   */
  async addExpenseRecord(recordData) {
    const result = await this.callFunction('expense', 'addExpenseRecord', recordData, {
      useCache: false
    })

    if (result.success) {
      // 清除当前用户的财务相关缓存
      this.clearCache('financial')
      this.clearCache('travel')
      // 清除旅行规划页面的本地缓存
      this.clearTravelPageCache()

      // 如果是协作支出，异步清理所有协作者的缓存
      if (recordData.planId) {
        this.clearCollaboratorsCaches(recordData.planId).catch(error => {
          console.error('清理协作者缓存失败:', error)
        })
      }
    }

    return result
  }

  /**
   * 清理协作者缓存
   */
  async clearCollaboratorsCaches(planId) {
    try {
      // 获取协作计划信息
      const planResult = await this.callFunction('travel', 'getTravelPlan', { planId })
      if (planResult.success && planResult.data?.collaboration?.enabled) {
        const plan = planResult.data

        // 收集所有参与者的openid
        const participantOpenids = [plan._openid] // 创建者
        if (plan.collaboration.collaborators) {
          participantOpenids.push(...plan.collaboration.collaborators.map(c => c.openid))
        }

        // 通知所有参与者清理缓存
        const dataChangeNotifier = require('./data-change-notifier.js').default
        participantOpenids.forEach(openid => {
          dataChangeNotifier.notifyChange('expense', {
            planId,
            affectedUser: openid
          }, {
            immediate: true,
            invalidateCache: true
          })
        })
      }
    } catch (error) {
      console.error('获取协作计划信息失败:', error)
      throw error
    }
  }

  /**
   * 获取支出记录
   */
  async getExpenseRecords(options = {}) {
    return await this.callFunction('expense', 'getExpenseRecords', options, {
      useCache: false // 支出记录不缓存，确保实时性
    })
  }

  /**
   * 删除支出记录
   */
  async deleteExpenseRecord(recordId) {
    // 先获取记录信息，用于后续缓存清理
    let recordInfo = null
    try {
      const recordResult = await this.callFunction('expense', 'getExpenseRecords', {
        limit: 1,
        skip: 0
      })
      if (recordResult.success && recordResult.data) {
        recordInfo = recordResult.data.find(record => record._id === recordId)
      }
    } catch (error) {
      console.warn('获取记录信息失败:', error)
    }

    const result = await this.callFunction('expense', 'deleteExpenseRecord', { recordId }, {
      useCache: false
    })

    if (result.success) {
      // 清除当前用户的相关缓存
      this.clearCache('financial')
      this.clearCache('travel')
      // 清除旅行规划页面的本地缓存
      this.clearTravelPageCache()

      // 如果是协作支出，异步清理所有协作者的缓存
      if (recordInfo?.travel_plan_id) {
        this.clearCollaboratorsCaches(recordInfo.travel_plan_id).catch(error => {
          console.error('清理协作者缓存失败:', error)
        })
      }
    }

    return result
  }

  /**
   * 获取用户预算设置
   */
  async getUserBudget(forceRefresh = false) {
    return await this.callFunction('expense', 'getUserBudget', {}, {
      cacheKey: CACHE_KEYS.USER_BUDGET,
      cacheTTL: CACHE_TTL.LONG,
      forceRefresh
    })
  }

  /**
   * 更新用户预算设置
   */
  async updateUserBudget(budgetData) {
    const result = await this.callFunction('expense', 'updateUserBudget', budgetData, {
      useCache: false
    })

    if (result.success) {
      // 清除预算相关缓存
      this.clearCache('budget')
      this.clearCache('financial')
    }

    return result
  }

  // ==================== 旅行数据 ====================

  /**
   * 获取旅行统计
   */
  async getTravelStatistics(forceRefresh = false) {
    return await this.callFunction('travel', 'getTravelStatistics', {}, {
      cacheKey: CACHE_KEYS.TRAVEL_STATISTICS,
      cacheTTL: CACHE_TTL.SHORT, // 改为短缓存，确保数据及时更新
      forceRefresh
    })
  }

  /**
   * 获取旅行计划
   */
  async getTravelPlans(options = {}) {
    const { forceRefresh, ...queryOptions } = options
    const cacheKey = `${CACHE_KEYS.TRAVEL_PLANS}_${JSON.stringify(queryOptions)}`
    return await this.callFunction('travel', 'getTravelPlans', queryOptions, {
      cacheKey,
      cacheTTL: CACHE_TTL.SHORT, // 改为短缓存，确保计划数据及时更新
      forceRefresh
    })
  }

  /**
   * 创建旅行计划
   */
  async createTravelPlan(planData) {
    const result = await this.callFunction('travel', 'addTravelPlan', planData, {
      useCache: false
    })

    if (result.success) {
      // 清除旅行相关缓存
      this.clearCache('travel')
      // 清除旅行规划页面的本地缓存
      this.clearTravelPageCache()
    }

    return result
  }

  /**
   * 更新旅行计划
   */
  async updateTravelPlan(planId, updateData) {
    const result = await this.callFunction('travel', 'updateTravelPlan', { planId, ...updateData }, {
      useCache: false
    })

    if (result.success) {
      // 清除旅行相关缓存
      this.clearCache('travel')
      // 清除旅行规划页面的本地缓存
      this.clearTravelPageCache()
    }

    return result
  }

  // ==================== 数据验证 ====================

  /**
   * 验证财务数据 - 增强逻辑一致性检查
   */
  validateFinancialData(data) {
    if (!data || typeof data !== 'object') return false

    // 基础类型验证
    const typeChecks = [
      typeof data.totalExpense === 'number' && data.totalExpense >= 0,
      typeof data.dailyExpense === 'number' && data.dailyExpense >= 0,
      typeof data.travelExpense === 'number' && data.travelExpense >= 0,
      typeof data.totalBudget === 'number' && data.totalBudget >= 0,
      typeof data.remainingBudget === 'number' && data.remainingBudget >= 0,
      typeof data.usagePercentage === 'number' && data.usagePercentage >= 0 && data.usagePercentage <= 1000,
      typeof data.dailyPercentage === 'number' && data.dailyPercentage >= 0,
      typeof data.travelPercentage === 'number' && data.travelPercentage >= 0
    ]

    if (!typeChecks.every(check => check === true)) {
      console.warn('财务数据类型验证失败')
      return false
    }

    // 逻辑一致性验证
    const logicChecks = [
      // 总支出应该等于日常支出+旅行支出（允许0.01的误差）
      Math.abs(data.totalExpense - (data.dailyExpense + data.travelExpense)) < 0.01,

      // 剩余预算应该等于总预算-总支出（允许0.01的误差）
      Math.abs(data.remainingBudget - (data.totalBudget - data.totalExpense)) < 0.01,

      // 使用百分比应该与实际计算一致（允许0.1的误差）
      data.totalBudget > 0 ?
        Math.abs(data.usagePercentage - (data.totalExpense / data.totalBudget * 100)) < 0.1 :
        data.usagePercentage === 0
    ]

    const isLogicValid = logicChecks.every(check => check === true)
    if (!isLogicValid) {
      console.warn('财务数据逻辑一致性验证失败', {
        totalExpense: data.totalExpense,
        dailyExpense: data.dailyExpense,
        travelExpense: data.travelExpense,
        calculatedTotal: data.dailyExpense + data.travelExpense,
        totalBudget: data.totalBudget,
        remainingBudget: data.remainingBudget,
        calculatedRemaining: data.totalBudget - data.totalExpense,
        usagePercentage: data.usagePercentage,
        calculatedUsage: data.totalBudget > 0 ? (data.totalExpense / data.totalBudget * 100) : 0
      })
    }

    return isLogicValid
  }

  /**
   * 验证旅行数据
   */
  validateTravelData(data) {
    if (!data || typeof data !== 'object') return false

    const checks = [
      typeof data.totalPlans === 'number' && data.totalPlans >= 0,
      typeof data.totalExpense === 'number' && data.totalExpense >= 0,
      typeof data.budgetUsage === 'number' && data.budgetUsage >= 0 && data.budgetUsage <= 1000
    ]

    return checks.every(check => check === true)
  }

  // ==================== 数据清理 ====================

  /**
   * 清理旅行规划页面的本地缓存
   */
  clearTravelPageCache() {
    try {
      // 清理本地存储缓存
      const localCacheKeys = [
        'travel_page_data',
        'travel_statistics',
        'travel_plans_cache',
        'ongoing_plans_cache'
      ]

      localCacheKeys.forEach(key => {
        try {
          wx.removeStorageSync(key)
        } catch (error) {
          // 静默处理单个缓存清理失败
        }
      })

      // 清理内存缓存中所有旅行计划相关的缓存
      this.clearCache('travel')

      // 清理所有travel_plans相关的缓存（包括不同选项的缓存）
      const cacheKeysToRemove = []
      for (const key of this.cache.keys()) {
        if (key.startsWith(CACHE_KEYS.TRAVEL_PLANS)) {
          cacheKeysToRemove.push(key)
        }
      }

      cacheKeysToRemove.forEach(key => {
        this.cache.delete(key)
      })

    } catch (error) {
      // 静默处理缓存清理失败
    }
  }

  /**
   * 清理异常数据
   */
  async cleanupData() {
    try {
      // 清除所有缓存
      this.clearCache()

      // 清除本地存储中的异常数据
      const keysToClean = [
        'travel_page_data',
        'travel_statistics',
        'financial_overview',
        'homepage_financial_data'
      ]

      keysToClean.forEach(key => {
        try {
          wx.removeStorageSync(key)
        } catch (error) {
          // 静默处理
        }
      })

      return { success: true, message: '数据清理完成' }
    } catch (error) {
      return { success: false, message: '数据清理失败' }
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

// 创建全局实例
const dataManager = new DataManager()

// 导出常量和实例
export { COLLECTIONS, CACHE_KEYS, CACHE_TTL }
export default dataManager
