/**
 * 统一导航管理器 v2.0
 * 解决导航逻辑不一致问题
 */

import hapticManager from './haptic-manager.js';

class NavigationManager {
  constructor() {
    this.navigationHistory = [];
    this.maxHistoryLength = 10;
  }
  
  // 记录导航历史
  recordNavigation(url, method = 'navigateTo') {
    this.navigationHistory.unshift({
      url,
      method,
      timestamp: Date.now()
    });
    
    // 限制历史记录长度
    if (this.navigationHistory.length > this.maxHistoryLength) {
      this.navigationHistory = this.navigationHistory.slice(0, this.maxHistoryLength);
    }
  }
  
  // 统一的旅行规划导航
  static navigateToTravelPlanning(source = 'index', options = {}) {
    hapticManager.navigation();
    
    let url;
    switch (source) {
      case 'profile':
        // 从个人中心进入，直接到创建页面
        url = '/subpackages/travel-planning/create-plan/index';
        break;
      case 'index':
      default:
        // 从首页进入，到主页面
        url = '/subpackages/travel-planning/index';
        break;
    }
    
    // 添加参数
    if (options.planId) {
      url += `?planId=${options.planId}`;
    }
    
    wx.navigateTo({
      url,
      success: () => {
        this.recordNavigation(url, 'navigateTo');
      },
      fail: (err) => {
        console.error('导航失败:', err);
        hapticManager.error();
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }
  
  // 统一的记账导航
  static navigateToAccounting(mode = 'daily', options = {}) {
    hapticManager.navigation();
    
    let url = `/subpackages/account/travel-expense/index?mode=${mode}`;
    
    // 添加计划ID参数
    if (options.planId) {
      url += `&planId=${options.planId}`;
    }
    
    wx.navigateTo({
      url,
      success: () => {
        this.recordNavigation(url, 'navigateTo');
      },
      fail: (err) => {
        console.error('导航失败:', err);
        hapticManager.error();
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }
  
  // 统一的社交导航
  static navigateToSocial(page = 'discover', options = {}) {
    hapticManager.navigation();
    
    const pageMap = {
      'discover': '/subpackages/social/discover/index',
      'friends': '/subpackages/social/friend-list/index',
      'chat': '/subpackages/social/chat/index',
      'share': '/subpackages/social/travel-share/index'
    };
    
    const url = pageMap[page] || pageMap.discover;
    
    wx.navigateTo({
      url,
      success: () => {
        this.recordNavigation(url, 'navigateTo');
      },
      fail: (err) => {
        console.error('导航失败:', err);
        hapticManager.error();
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }
  
  // 统一的设置导航
  static navigateToSettings(page = 'user-settings', options = {}) {
    hapticManager.navigation();
    
    const pageMap = {
      'user-settings': '/subpackages/settings/user-settings/index',
      'budget-setting': '/subpackages/settings/budget-setting/index',
      'about': '/subpackages/settings/about/index',
      'help': '/subpackages/settings/help/index',
      'privacy': '/subpackages/settings/privacy/index',
      'user-agreement': '/subpackages/settings/user-agreement/index'
    };
    
    const url = pageMap[page] || pageMap['user-settings'];
    
    wx.navigateTo({
      url,
      success: () => {
        this.recordNavigation(url, 'navigateTo');
      },
      fail: (err) => {
        console.error('导航失败:', err);
        hapticManager.error();
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }
  
  // 智能返回
  static smartBack() {
    hapticManager.navigation();
    
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  }
  
  // 安全导航（带错误处理）
  static safeNavigate(url, method = 'navigateTo', options = {}) {
    hapticManager.navigation();
    
    const navigateMethod = wx[method];
    if (!navigateMethod) {
      console.error('不支持的导航方法:', method);
      hapticManager.error();
      return;
    }
    
    navigateMethod({
      url,
      ...options,
      success: (res) => {
        this.recordNavigation(url, method);
        if (options.success) {
          options.success(res);
        }
      },
      fail: (err) => {
        console.error('导航失败:', err);
        hapticManager.error();
        
        // 尝试降级处理
        if (method === 'switchTab' && err.errMsg.includes('not found')) {
          // Tab页面不存在，尝试普通导航
          this.safeNavigate(url, 'navigateTo', options);
          return;
        }
        
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none',
          duration: 2000
        });
        
        if (options.fail) {
          options.fail(err);
        }
      }
    });
  }
  
  // 获取导航历史
  getNavigationHistory() {
    return this.navigationHistory;
  }
  
  // 清除导航历史
  clearNavigationHistory() {
    this.navigationHistory = [];
  }
  
  // 检查是否可以返回
  canGoBack() {
    const pages = getCurrentPages();
    return pages.length > 1;
  }
  
  // 获取当前页面路径
  getCurrentPagePath() {
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      return currentPage.route;
    }
    return null;
  }
  
  // 检查是否为指定页面
  isCurrentPage(pagePath) {
    const currentPath = this.getCurrentPagePath();
    return currentPath === pagePath;
  }
}

// 导出单例实例
export default new NavigationManager();
