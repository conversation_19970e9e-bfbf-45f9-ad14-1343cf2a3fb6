/**
 * API工具类
 * 简洁的云开发API封装
 */

import { API_STATUS } from './constants.js'

class API {
  constructor() {
    // 初始化云开发
    if (wx.cloud) {
      wx.cloud.init({
        traceUser: true
      })
      this.db = wx.cloud.database()
    }
  }

  // 微信登录
  async wxLogin() {
    try {
      const loginRes = await this.promisify(wx.login)()
      
      if (!loginRes.code) {
        throw new Error('获取登录凭证失败')
      }

      // 调用云函数获取openid - 添加超时控制
      const cloudRes = await Promise.race([
        wx.cloud.callFunction({
          name: 'login',
          data: { code: loginRes.code }
        }),
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('云函数调用超时')), 10000)
        })
      ])

      return {
        success: true,
        data: cloudRes.result
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      return {
        success: false,
        message: error.message || '登录失败'
      }
    }
  }

  // 获取用户信息
  async getUserProfile() {
    try {
      const userInfo = await this.promisify(wx.getUserProfile)({
        desc: '用于完善用户资料'
      })
      
      return {
        success: true,
        data: userInfo.userInfo
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return {
        success: false,
        message: '获取用户信息失败'
      }
    }
  }

  // 数据库查询
  async query(collection, where = {}, orderBy = null, limit = 20) {
    try {
      let query = this.db.collection(collection).where(where)
      
      if (orderBy) {
        query = query.orderBy(orderBy.field, orderBy.order || 'desc')
      }
      
      const res = await Promise.race([
        query.limit(limit).get(),
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('数据库查询超时')), 10000)
        })
      ])
      
      return {
        success: true,
        data: res.data
      }
    } catch (error) {
      console.error('查询失败:', error)
      return {
        success: false,
        message: '查询失败'
      }
    }
  }

  // 数据库添加
  async add(collection, data) {
    try {
      const res = await Promise.race([
        this.db.collection(collection).add({
          data: {
            ...data,
            createTime: new Date(),
            updateTime: new Date()
          }
        }),
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('数据库添加超时')), 10000)
        })
      ])
      
      return {
        success: true,
        data: { _id: res._id }
      }
    } catch (error) {
      console.error('添加失败:', error)
      return {
        success: false,
        message: '添加失败'
      }
    }
  }

  // 数据库更新
  async update(collection, id, data) {
    try {
      const res = await Promise.race([
        this.db.collection(collection).doc(id).update({
          data: {
            ...data,
            updateTime: new Date()
          }
        }),
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('数据库更新超时')), 10000)
        })
      ])
      
      return {
        success: true,
        data: res
      }
    } catch (error) {
      console.error('更新失败:', error)
      return {
        success: false,
        message: '更新失败'
      }
    }
  }

  // 数据库删除
  async remove(collection, id) {
    try {
      const res = await Promise.race([
        this.db.collection(collection).doc(id).remove(),
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('数据库删除超时')), 10000)
        })
      ])
      
      return {
        success: true,
        data: res
      }
    } catch (error) {
      console.error('删除失败:', error)
      return {
        success: false,
        message: '删除失败'
      }
    }
  }

  // Promise化微信API
  promisify(fn) {
    return (options = {}) => {
      return new Promise((resolve, reject) => {
        fn({
          ...options,
          success: resolve,
          fail: reject
        })
      })
    }
  }
}

export default new API()
