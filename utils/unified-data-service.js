/**
 * 统一数据服务 - 整合所有数据管理功能
 * 解决重复代码问题，提供统一的数据访问接口
 */

// 导入现有模块（逐步迁移）
import cacheManager, { CACHE_KEYS } from './cache-manager.js'
import auth from './auth.js'

// 数据库集合定义
const COLLECTIONS = {
  USERS: 'users',
  RECORDS: 'expense_records',
  TRAVEL_PLANS: 'travel_plans',
  CATEGORIES: 'categories',
  USER_BUDGETS: 'user_budgets'
}

// 缓存TTL定义（毫秒）
const CACHE_TTL = {
  SHORT: 2 * 60 * 1000,    // 2分钟
  MEDIUM: 10 * 60 * 1000,  // 10分钟
  LONG: 30 * 60 * 1000     // 30分钟
}

class UnifiedDataService {
  constructor() {
    // 使用统一的缓存管理器
    this.cacheManager = cacheManager
    
    // 同步状态管理
    this.syncStatus = {
      isOnline: true,
      lastSyncTime: 0,
      syncInProgress: false
    }
    
    // 数据版本管理
    this.dataVersions = new Map()
    
    // 初始化
    this.init()
  }

  /**
   * 初始化服务
   */
  async init() {
    // 检查网络状态
    this.checkNetworkStatus()
    
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      this.syncStatus.isOnline = res.isConnected
    })
  }

  /**
   * 检查网络状态
   */
  checkNetworkStatus() {
    wx.getNetworkType({
      success: (res) => {
        this.syncStatus.isOnline = res.networkType !== 'none'
      }
    })
  }

  // ==================== 统一云函数调用 ====================

  /**
   * 统一云函数调用（带超时和重试机制）
   * @param {string} name 云函数名称
   * @param {string} action 操作类型
   * @param {Object} data 数据
   * @param {Object} options 选项
   */
  async callFunction(name, action, data = {}, options = {}) {
    const {
      useCache = true,
      cacheKey,
      cacheTTL = CACHE_TTL.MEDIUM,
      forceRefresh = false,
      timeout = 5000,
      retries = 2
    } = options

    try {
      // 检查缓存
      if (useCache && cacheKey && !forceRefresh) {
        const cached = this.cacheManager.get(cacheKey)
        if (cached) {
          return { success: true, data: cached, fromCache: true }
        }
      }

      // 执行云函数调用（带超时和重试）
      const result = await this.executeWithRetry(
        () => this.callCloudFunction(name, action, data, timeout),
        retries
      )

      if (result.result && result.result.success) {
        // 缓存结果
        if (useCache && cacheKey) {
          this.cacheManager.set(cacheKey, result.result.data, cacheTTL)
        }

        return result.result
      } else {
        throw new Error(result.result?.message || '云函数调用失败')
      }
    } catch (error) {
      console.error(`云函数调用失败 [${name}/${action}]:`, error)
      return {
        success: false,
        message: error.message || '数据获取失败'
      }
    }
  }

  /**
   * 执行云函数调用（带超时）
   */
  async callCloudFunction(name, action, data, timeout) {
    return Promise.race([
      wx.cloud.callFunction({
        name,
        data: { action, data }
      }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('云函数调用超时')), timeout)
      )
    ])
  }

  /**
   * 带重试机制的执行
   */
  async executeWithRetry(fn, retries) {
    let lastError
    
    for (let i = 0; i <= retries; i++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        if (i < retries) {
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
        }
      }
    }
    
    throw lastError
  }

  // ==================== 统一缓存管理 ====================

  /**
   * 设置缓存
   */
  setCache(key, data, ttl = CACHE_TTL.MEDIUM) {
    return this.cacheManager.set(key, data, ttl)
  }

  /**
   * 获取缓存
   */
  getCache(key) {
    return this.cacheManager.get(key)
  }

  /**
   * 清除缓存（统一入口）
   */
  clearCache(pattern = null) {
    if (pattern) {
      this.cacheManager.clearByPattern(pattern)
    } else {
      this.cacheManager.clear()
    }

    // 同步清理本地存储
    this.clearLocalStorage(pattern)
  }

  /**
   * 清理本地存储
   */
  clearLocalStorage(pattern = null) {
    if (pattern) {
      const keysToClean = [
        `${pattern}_data`,
        `${pattern}_statistics`,
        `${pattern}_overview`,
        `homepage_${pattern}_data`
      ]
      keysToClean.forEach(key => {
        try {
          wx.removeStorageSync(key)
        } catch (error) {
          // 静默处理
        }
      })
    } else {
      // 清理所有相关存储
      const allKeys = [
        'travel_page_data',
        'travel_statistics',
        'financial_overview',
        'homepage_financial_data',
        'homepage_travel_data'
      ]
      allKeys.forEach(key => {
        try {
          wx.removeStorageSync(key)
        } catch (error) {
          // 静默处理
        }
      })
    }
  }

  // ==================== 数据验证 ====================

  /**
   * 验证财务数据
   */
  validateFinancialData(data) {
    if (!data || typeof data !== 'object') return false
    
    const requiredFields = ['totalExpense', 'monthlyExpense', 'budgetUsage']
    return requiredFields.every(field => 
      data.hasOwnProperty(field) && typeof data[field] === 'number'
    )
  }

  /**
   * 验证旅行数据
   */
  validateTravelData(data) {
    if (!data || typeof data !== 'object') return false
    
    const requiredFields = ['totalPlans', 'ongoingPlans', 'totalExpense']
    return requiredFields.every(field => 
      data.hasOwnProperty(field) && typeof data[field] === 'number'
    )
  }

  // ==================== 业务数据接口 ====================

  /**
   * 获取财务概览
   */
  async getFinancialOverview(forceRefresh = false) {
    return await this.callFunction('expense', 'getFinancialOverview', {}, {
      cacheKey: CACHE_KEYS.FINANCIAL_OVERVIEW,
      cacheTTL: CACHE_TTL.MEDIUM,
      forceRefresh
    })
  }

  /**
   * 获取旅行统计
   */
  async getTravelStatistics(forceRefresh = false) {
    return await this.callFunction('travel', 'getTravelStatistics', {
      includeCollaboration: true // 确保包含协作数据
    }, {
      cacheKey: CACHE_KEYS.TRAVEL_STATISTICS,
      cacheTTL: CACHE_TTL.MEDIUM,
      forceRefresh
    })
  }

  /**
   * 获取旅行计划列表
   */
  async getTravelPlans(options = {}) {
    const { status, limit, forceRefresh = false } = options
    
    return await this.callFunction('travel', 'getTravelPlans', {
      status,
      limit
    }, {
      cacheKey: `${CACHE_KEYS.TRAVEL_PLANS}_${status || 'all'}`,
      cacheTTL: CACHE_TTL.SHORT,
      forceRefresh
    })
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      syncStatus: this.syncStatus,
      cacheStats: this.cacheManager.getStats(),
      dataVersions: Object.fromEntries(this.dataVersions)
    }
  }

  /**
   * 清理异常数据
   */
  async cleanupData() {
    try {
      // 清除所有缓存
      this.clearCache()

      return { success: true, message: '数据清理完成' }
    } catch (error) {
      return { success: false, message: '数据清理失败' }
    }
  }

  /**
   * 数据预热 - 预加载常用数据
   */
  async warmupData() {
    try {
      console.log('开始数据预热...')

      // 并行预热关键数据
      const warmupTasks = [
        this.getFinancialOverview().catch(e => console.warn('财务数据预热失败:', e)),
        this.getTravelStatistics().catch(e => console.warn('旅行统计预热失败:', e)),
        this.getTravelPlans({ limit: 5 }).catch(e => console.warn('旅行计划预热失败:', e))
      ]

      await Promise.allSettled(warmupTasks)
      console.log('数据预热完成')

      return { success: true, message: '数据预热完成' }
    } catch (error) {
      console.error('数据预热失败:', error)
      return { success: false, message: '数据预热失败' }
    }
  }

  /**
   * 智能数据同步 - 根据数据变化智能更新缓存
   */
  async smartSync(dataType, newData) {
    try {
      // 检查数据是否真的发生了变化
      const cacheKey = this.getCacheKeyForDataType(dataType)
      const cachedData = this.getCache(cacheKey)

      if (cachedData && JSON.stringify(cachedData) === JSON.stringify(newData)) {
        // 数据没有变化，无需更新
        return { success: true, message: '数据无变化，跳过同步' }
      }

      // 更新缓存
      this.setCache(cacheKey, newData)

      // 更新数据版本
      this.dataVersions.set(dataType, Date.now())

      return { success: true, message: '数据同步完成' }
    } catch (error) {
      return { success: false, message: '数据同步失败' }
    }
  }

  /**
   * 根据数据类型获取缓存键
   */
  getCacheKeyForDataType(dataType) {
    const keyMap = {
      'financial': CACHE_KEYS.FINANCIAL_OVERVIEW,
      'travel': CACHE_KEYS.TRAVEL_STATISTICS,
      'plans': CACHE_KEYS.TRAVEL_PLANS,
      'user': CACHE_KEYS.USER_INFO
    }
    return keyMap[dataType] || dataType
  }
}

// 创建全局实例
const unifiedDataService = new UnifiedDataService()

// 导出常量和实例
export { COLLECTIONS, CACHE_KEYS, CACHE_TTL }
export default unifiedDataService
