/**
 * 常量定义
 * 保持简洁，只定义必要的常量
 */

// 应用信息
export const APP_INFO = {
  NAME: '爱巢小记',
  VERSION: '1.0.0',
  RELEASE_DATE: '2025-01-24',
  CONTACT_EMAIL: '<EMAIL>',
  CONTACT_PHONE: '***********'
}

// 页面路径
export const PAGES = {
  INDEX: '/pages/index/index',
  LOGIN: '/pages/login/index',
  PROFILE: '/pages/profile/index'
}

// 分包路径
export const SUBPACKAGES = {
  ACCOUNT: '/subpackages/account/',
  TRAVEL: '/subpackages/travel/',
  SOCIAL: '/subpackages/social/',
  SETTINGS: '/subpackages/settings/'
}

// 存储键名
export const STORAGE_KEYS = {
  USER_INFO: 'userInfo',
  THEME: 'theme',
  SETTINGS: 'settings'
}

// 主题配置
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
}

// API状态码
export const API_STATUS = {
  SUCCESS: 200,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500
}
