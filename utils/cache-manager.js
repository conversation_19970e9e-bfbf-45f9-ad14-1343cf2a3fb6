
class CacheManager {
  constructor() {
    // 内存缓存存储
    this.cache = new Map()

    // 定时器引用
    this.cleanupTimer = null

    // 缓存配置
    this.config = {
      // 默认TTL: 5分钟
      defaultTTL: 5 * 60 * 1000,
      // 最大缓存条目数
      maxSize: 100,
      // 清理间隔: 10分钟
      cleanupInterval: 10 * 60 * 1000
    }

    // 启动定期清理
    this.startCleanupTimer()
  }

  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {any} data 缓存数据
   * @param {number} ttl 生存时间(毫秒)，默认使用配置值
   * @param {string} version 数据版本，用于缓存失效
   */
  set(key, data, ttl = this.config.defaultTTL, version = '1.0') {
    try {
      // 检查缓存大小限制
      if (this.cache.size >= this.config.maxSize) {
        this.evictOldest()
      }

      const cacheItem = {
        data: data,
        timestamp: Date.now(),
        ttl: ttl,
        version: version,
        accessCount: 0,
        lastAccess: Date.now()
      }

      this.cache.set(key, cacheItem)
      return true
    } catch (error) {
      console.error('缓存设置失败:', error)
      return false
    }
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @param {string} version 期望的数据版本
   * @returns {any|null} 缓存数据或null
   */
  get(key, version = null) {
    try {
      const cacheItem = this.cache.get(key)
      
      if (!cacheItem) {
        return null
      }

      // 检查TTL
      if (this.isExpired(cacheItem)) {
        this.cache.delete(key)
        return null
      }

      // 检查版本
      if (version && cacheItem.version !== version) {
        this.cache.delete(key)
        return null
      }

      // 更新访问统计
      cacheItem.accessCount++
      cacheItem.lastAccess = Date.now()

      return cacheItem.data
    } catch (error) {
      console.error('缓存获取失败:', error)
      return null
    }
  }

  /**
   * 检查缓存是否存在且有效
   * @param {string} key 缓存键
   * @param {string} version 期望的数据版本
   * @returns {boolean}
   */
  has(key, version = null) {
    const cacheItem = this.cache.get(key)
    
    if (!cacheItem || this.isExpired(cacheItem)) {
      return false
    }

    if (version && cacheItem.version !== version) {
      return false
    }

    return true
  }

  /**
   * 删除指定缓存
   * @param {string} key 缓存键
   */
  delete(key) {
    return this.cache.delete(key)
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear()
  }

  /**
   * 按模式清除缓存
   * @param {string} pattern 匹配模式
   */
  clearByPattern(pattern) {
    const keysToDelete = []

    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key))

    if (keysToDelete.length > 0) {
      console.log(`按模式清除缓存: ${pattern}, 清除了 ${keysToDelete.length} 个缓存项`)
    }

    return keysToDelete.length
  }

  /**
   * 使指定前缀的缓存失效
   * @param {string} prefix 缓存键前缀
   */
  invalidateByPrefix(prefix) {
    const keysToDelete = []
    
    for (const key of this.cache.keys()) {
      if (key.startsWith(prefix)) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key))
    return keysToDelete.length
  }

  /**
   * 检查缓存项是否过期
   * @param {object} cacheItem 缓存项
   * @returns {boolean}
   */
  isExpired(cacheItem) {
    return Date.now() - cacheItem.timestamp > cacheItem.ttl
  }

  /**
   * 移除最旧的缓存项
   */
  evictOldest() {
    let oldestKey = null
    let oldestTime = Date.now()

    for (const [key, item] of this.cache.entries()) {
      if (item.lastAccess < oldestTime) {
        oldestTime = item.lastAccess
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }

  /**
   * 启动定期清理定时器（修复内存泄漏）
   */
  startCleanupTimer() {
    // 保存定时器引用以便清理
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const keysToDelete = []
    
    for (const [key, item] of this.cache.entries()) {
      if (this.isExpired(item)) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key))
    
    if (keysToDelete.length > 0) {
      console.log(`缓存清理完成，移除 ${keysToDelete.length} 个过期项`)
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {object} 统计信息
   */
  getStats() {
    let totalSize = 0
    let expiredCount = 0
    
    for (const [key, item] of this.cache.entries()) {
      totalSize++
      if (this.isExpired(item)) {
        expiredCount++
      }
    }

    return {
      totalSize,
      expiredCount,
      hitRate: this.calculateHitRate(),
      maxSize: this.config.maxSize
    }
  }

  /**
   * 计算缓存命中率（简化版）
   * @returns {number} 命中率百分比
   */
  calculateHitRate() {
    // 这里可以实现更复杂的命中率计算逻辑
    return 0
  }

  /**
   * 预热缓存 - 批量设置常用数据
   * @param {object} dataMap 数据映射 {key: {data, ttl, version}}
   */
  warmup(dataMap) {
    for (const [key, config] of Object.entries(dataMap)) {
      this.set(key, config.data, config.ttl, config.version)
    }
  }

  /**
   * 停止清理定时器（修复内存泄漏）
   */
  stopCleanupTimer() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
  }

  /**
   * 销毁缓存管理器（修复内存泄漏）
   */
  destroy() {
    // 停止清理定时器
    this.stopCleanupTimer()

    // 清空缓存
    this.cache.clear()

    // 重置配置
    this.config = null

    console.log('缓存管理器已销毁，资源已清理')
  }

  /**
   * 安全清理 - 页面卸载时调用
   */
  safeCleanup() {
    try {
      // 清理过期缓存
      this.cleanup()

      // 如果缓存过大，清理最旧的项目
      if (this.cache.size > this.config.maxSize * 0.8) {
        const keysToDelete = []
        let count = 0
        const maxDelete = Math.floor(this.config.maxSize * 0.2)

        for (const [key, item] of this.cache.entries()) {
          if (count >= maxDelete) break
          keysToDelete.push(key)
          count++
        }

        keysToDelete.forEach(key => this.cache.delete(key))
        console.log(`安全清理：删除了 ${keysToDelete.length} 个缓存项`)
      }
    } catch (error) {
      console.error('安全清理失败:', error)
    }
  }
}

// 创建全局缓存管理器实例
const cacheManager = new CacheManager()

// 预定义的缓存键
export const CACHE_KEYS = {
  // 财务数据
  FINANCIAL_OVERVIEW: 'financial_overview',
  USER_BUDGET: 'user_budget',
  EXPENSE_RECORDS: 'expense_records',
  
  // 旅行数据
  TRAVEL_STATISTICS: 'travel_statistics',
  TRAVEL_PLANS: 'travel_plans',
  CURRENT_PLAN: 'current_plan',
  ONGOING_PLANS: 'ongoing_plans',
  
  // 用户数据
  USER_INFO: 'user_info',
  USER_STATS: 'user_stats',
  
  // 其他
  DESTINATIONS: 'destinations',
  CATEGORIES: 'categories'
}

// 缓存TTL配置（毫秒）
export const CACHE_TTL = {
  SHORT: 2 * 60 * 1000,      // 2分钟 - 用于频繁变化的数据
  MEDIUM: 5 * 60 * 1000,     // 5分钟 - 用于一般数据
  LONG: 15 * 60 * 1000,      // 15分钟 - 用于相对稳定的数据
  VERY_LONG: 60 * 60 * 1000  // 1小时 - 用于很少变化的数据
}

export default cacheManager
