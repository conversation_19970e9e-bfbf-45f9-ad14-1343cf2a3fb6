/**
 * 登录状态管理工具
 * 统一处理登录状态检查和重定向
 */

import storage from './storage.js'
import { PAGES } from './constants.js'

class Auth {
  /**
   * 检查用户是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    // {{ AURA-X: Modify - 移除游客判断，只检查openid. Approval: 寸止(ID:1738056000). }}
    const userInfo = storage.getUserInfo()
    return !!(userInfo && userInfo.openid)
  }

  /**
   * 获取当前用户信息
   * @returns {Object|null} 用户信息或null
   */
  getCurrentUser() {
    return storage.getUserInfo()
  }

  /**
   * 检查登录状态，未登录则重定向到登录页
   * @param {Object} options 配置选项
   * @param {boolean} options.showToast 是否显示提示信息
   * @param {string} options.redirectType 重定向类型：'reLaunch'|'redirectTo'|'navigateTo'
   * @returns {boolean} 是否已登录
   */
  requireLogin(options = {}) {
    const {
      showToast = true,
      redirectType = 'reLaunch'
    } = options

    if (this.isLoggedIn()) {
      return true
    }

    // 显示提示信息
    if (showToast) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 1500
      })
    }

    // 延迟跳转，让用户看到提示信息
    if (showToast) {
      setTimeout(() => {
        this.redirectToLogin(redirectType)
      }, 1500)
    } else {
      // 不显示提示时立即跳转
      this.redirectToLogin(redirectType)
    }

    return false
  }

  /**
   * 重定向到登录页面
   * @param {string} type 重定向类型
   */
  redirectToLogin(type = 'reLaunch') {
    const url = PAGES.LOGIN

    switch (type) {
      case 'reLaunch':
        wx.reLaunch({ url })
        break
      case 'redirectTo':
        wx.redirectTo({ url })
        break
      case 'navigateTo':
        wx.navigateTo({ url })
        break
      default:
        wx.reLaunch({ url })
    }
  }

  /**
   * 页面级别的登录检查混入
   * 在页面的onLoad或onShow中调用
   * @param {Object} pageInstance 页面实例
   * @param {Object} options 配置选项
   */
  checkPageAuth(pageInstance, options = {}) {
    if (!this.requireLogin(options)) {
      // 未登录，停止页面后续初始化
      return false
    }

    // 已登录，更新页面用户信息
    const userInfo = this.getCurrentUser()
    if (pageInstance.setData && userInfo) {
      pageInstance.setData({
        userInfo: userInfo,
        hasUserInfo: true,
        isGuest: userInfo.isGuest || false
      })
    }

    return true
  }

  /**
   * 登出
   */
  logout() {
    // 清除用户信息
    storage.remove('userInfo')
    
    // 清除全局用户信息
    const app = getApp()
    if (app.globalData) {
      app.globalData.userInfo = null
    }

    // 跳转到登录页
    this.redirectToLogin('reLaunch')
  }

  /**
   * 设置用户信息
   * @param {Object} userInfo 用户信息
   */
  setUserInfo(userInfo) {
    // 保存到本地存储
    storage.setUserInfo(userInfo)
    
    // 更新全局数据
    const app = getApp()
    if (app.globalData) {
      app.globalData.userInfo = userInfo
    }
  }
}

export default new Auth()
