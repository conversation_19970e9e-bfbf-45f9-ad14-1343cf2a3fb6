/**
 * 用户管理器 - 完整版本
 */

import storage from './storage.js'
import unifiedPerformanceManager from './unified-performance-manager.js'

class UserManager {
  constructor() {
    this.listeners = new Map()
    this.userInfo = null

    // 初始化时加载用户信息
    this.loadUserInfo()
  }

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      const app = getApp()

      // 强制清理可能的错误缓存
      this.clearUserCache()

      if (app.globalData.openid) {
        const cloudUserInfo = await this.loadUserInfoFromCloud()

        if (cloudUserInfo) {
          // 验证用户身份一致性
          if (cloudUserInfo.openid === app.globalData.openid) {
            this.userInfo = cloudUserInfo
            storage.setUserInfo(this.userInfo)
          } else {
            this.clearUserCache()
            this.loadUserInfoFromLocal()
          }
        } else {
          this.loadUserInfoFromLocal()
        }
      } else {
        this.loadUserInfoFromLocal()
      }

      this.updateGlobalData()

    } catch (error) {
      this.clearUserCache()
      this.loadUserInfoFromLocal()
    }
  }

  /**
   * 从云端加载用户信息
   */
  async loadUserInfoFromCloud() {
    try {
      const app = getApp()
      const openid = app.globalData.openid
      
      if (!openid) {
        return null
      }

      const db = wx.cloud.database()
      const result = await db.collection('users').where({
        _openid: openid
      }).get()

      if (result.data && result.data.length > 0) {
        const userData = result.data[0]
        return {
          // 基本信息
          avatarUrl: userData.avatarUrl || '/images/user.svg',
          nickName: userData.nickName || '微信用户',
          gender: userData.gender || 0,

          // 地区信息
          city: userData.city || '',
          province: userData.province || '',
          country: userData.country || '',
          location: userData.location || '',

          // 联系信息
          phone: userData.phone || '',
          email: userData.email || '',

          // 个人偏好
          language: userData.language || 'zh_CN',
          theme: userData.theme || 'light',

          // 状态信息
          setupCompleted: userData.setupCompleted || false,
          useWechatInfo: userData.useWechatInfo || false,

          // 系统信息
          openid: openid,
          _id: userData._id,
          createTime: userData.createTime,
          updateTime: userData.updateTime,
          lastLoginTime: userData.lastLoginTime
        }
      }
      return null
    } catch (error) {
      return null
    }
  }

  /**
   * 从本地存储加载用户信息
   */
  loadUserInfoFromLocal() {
    try {
      const storedUserInfo = storage.getUserInfo()

      if (storedUserInfo) {
        this.userInfo = storedUserInfo
      } else {
        this.userInfo = this.getDefaultGuestInfo()
      }
    } catch (error) {
      this.userInfo = this.getDefaultGuestInfo()
    }
  }

  /**
   * 清理用户缓存
   */
  clearUserCache() {
    try {
      storage.remove('userInfo')
      this.userInfo = null
    } catch (error) {
      // 静默处理清理失败
    }
  }

  /**
   * 获取默认用户信息
   */
  getDefaultGuestInfo() {
    return {
      // 基本信息
      avatarUrl: '/images/user.svg',
      nickName: '微信用户',
      gender: 0,

      // 地区信息
      city: '',
      province: '',
      country: '',
      location: '',

      // 联系信息
      phone: '',
      email: '',

      // 个人偏好
      language: 'zh_CN',
      theme: 'light',

      // 状态信息
      setupCompleted: false,
      useWechatInfo: false,

      // 时间戳
      createTime: new Date(),
      updateTime: new Date()
    }
  }

  /**
   * 更新全局数据
   */
  updateGlobalData() {
    const app = getApp()
    if (app && app.globalData) {
      app.globalData.userInfo = this.userInfo
    }
  }

  /**
   * 获取当前用户信息（带身份验证）
   */
  getUserInfo() {
    // 验证用户信息的有效性
    if (this.userInfo) {
      const app = getApp()

      // 如果有openid，验证一致性
      if (app.globalData.openid && this.userInfo.openid) {
        if (app.globalData.openid !== this.userInfo.openid) {
          this.clearUserCache()
          return this.getDefaultGuestInfo()
        }
      }
    }

    return this.userInfo || this.getDefaultGuestInfo()
  }

  /**
   * 设置用户登录状态
   */
  setUserLogin(userInfo) {
    this.userInfo = userInfo

    storage.setUserInfo(this.userInfo)
    this.updateGlobalData()
    this.notifyListeners('loginStatusChanged', {
      userInfo: this.userInfo
    })
    this.notifyListeners('userInfoChanged', this.userInfo)


  }

  /**
   * 强制刷新用户信息
   */
  async forceRefreshUserInfo() {
    try {

      // 清理所有缓存
      this.clearUserCache()

      // 重新加载
      await this.loadUserInfo()

      this.notifyListeners('userInfoChanged', this.userInfo)
      this.notifyListeners('loginStatusChanged', {
        userInfo: this.userInfo
      })
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 更新用户信息
   */
  async updateUserInfo(updateData) {
    try {
      // 验证必要字段
      if (!updateData) {
        throw new Error('更新数据不能为空')
      }

      // 合并用户信息
      this.userInfo = {
        ...this.userInfo,
        ...updateData,
        updateTime: new Date().toISOString()
      }

      storage.setUserInfo(this.userInfo)
      this.updateGlobalData()

      // 同步到云端
      if (this.userInfo.openid) {
        await this.syncUserInfoToCloud()
      }

      this.notifyListeners('userInfoChanged', this.userInfo)

      // 强制刷新所有页面的用户信息显示
      this.refreshAllPages()

      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 刷新所有页面的用户信息显示
   */
  refreshAllPages() {
    try {
      // 获取当前页面栈
      const pages = getCurrentPages()

      // 刷新所有页面的用户信息
      pages.forEach(page => {
        if (page && page.setData && typeof page.setData === 'function') {
          page.setData({
            userInfo: this.userInfo,
            hasUserInfo: !!this.userInfo
          })
        }
      })

      // 已刷新所有页面的用户信息
    } catch (error) {
    }
  }

  /**
   * 同步用户信息到云端
   */
  async syncUserInfoToCloud() {
    try {
      if (!this.userInfo.openid) {

        return false
      }

      const db = wx.cloud.database()

      // 先查询用户是否存在
      const userQuery = await db.collection('users').where({
        _openid: this.userInfo.openid
      }).get()

      const syncData = {
        // 基本信息
        nickName: this.userInfo.nickName || '',
        avatarUrl: this.userInfo.avatarUrl || '',
        gender: this.userInfo.gender || 0,

        // 地区信息
        city: this.userInfo.city || '',
        province: this.userInfo.province || '',
        country: this.userInfo.country || '',
        location: this.userInfo.location || '',

        // 联系信息
        phone: this.userInfo.phone || '',
        email: this.userInfo.email || '',

        // 个人偏好
        language: this.userInfo.language || 'zh_CN',
        theme: this.userInfo.theme || 'light',

        // 状态信息
        setupCompleted: this.userInfo.setupCompleted || false,
        useWechatInfo: this.userInfo.useWechatInfo || false,

        // 时间戳
        createTime: this.userInfo.createTime || new Date(),
        updateTime: new Date(),
        lastLoginTime: new Date()
      }

      if (userQuery.data.length > 0) {
        // 用户存在，更新信息
        await db.collection('users').where({
          _openid: this.userInfo.openid
        }).update({
          data: syncData
        })
      } else {
        // 用户不存在，创建新记录
        await db.collection('users').add({
          data: {
            ...syncData,
            openid: this.userInfo.openid,
            unionid: this.userInfo.unionid,
            createTime: new Date()
          }
        })
      }


      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 从云端拉取最新用户信息
   */
  async pullUserInfoFromCloud() {
    try {
      if (!this.userInfo?.openid) {

        return false
      }

      const cloudUserInfo = await this.loadUserInfoFromCloud()

      if (cloudUserInfo) {
        // 合并云端信息和本地信息
        const mergedInfo = {
          ...this.userInfo,
          ...cloudUserInfo
        }

        this.userInfo = mergedInfo
        storage.setUserInfo(this.userInfo)
        this.updateGlobalData()
        this.notifyListeners('userInfoChanged', this.userInfo)


        return true
      }

      return false
    } catch (error) {
      return false
    }
  }

  /**
   * 智能同步：根据时间戳决定同步方向
   */
  async smartSync() {
    try {
      if (!this.userInfo?.openid) {
        return false
      }

      const cloudUserInfo = await this.loadUserInfoFromCloud()

      if (!cloudUserInfo) {
        // 云端没有数据，上传本地数据
        return await this.syncUserInfoToCloud()
      }

      const localUpdateTime = new Date(this.userInfo.updateTime || 0)
      const cloudUpdateTime = new Date(cloudUserInfo.updateTime || 0)

      if (cloudUpdateTime > localUpdateTime) {
        // 云端数据更新，拉取云端数据

        return await this.pullUserInfoFromCloud()
      } else if (localUpdateTime > cloudUpdateTime) {
        // 本地数据更新，推送到云端

        return await this.syncUserInfoToCloud()
      } else {
        // 数据一致，无需同步

        return true
      }
    } catch (error) {
      return false
    }
  }

  /**
   * 页面混入方法 - 优化版本，支持快速初始化
   */
  async mixinPage(pageInstance) {
    if (!pageInstance) return

    // 立即设置当前用户信息，不等待加载
    if (pageInstance.setData) {
      unifiedPerformanceManager.smartSetData(pageInstance, {
        userInfo: this.userInfo || this.getDefaultGuestInfo(),
        hasUserInfo: !!this.userInfo
      })
    }

    // 如果没有用户信息，快速加载
    if (!this.userInfo) {
      try {
        // 设置1秒超时，避免阻塞页面
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('用户信息加载超时')), 1000)
        })

        await Promise.race([
          this.loadUserInfo(),
          timeoutPromise
        ])

        // 更新页面数据
        if (pageInstance.setData) {
          unifiedPerformanceManager.smartSetData(pageInstance, {
            userInfo: this.userInfo,
            hasUserInfo: !!this.userInfo
          })
        }
      } catch (error) {
        // 静默处理加载失败，使用默认信息
      }
    }

    // 避免重复添加事件监听器
    const pageId = unifiedPerformanceManager.getPageId(pageInstance)
    if (!this.listeners.has(`userInfoChanged_${pageId}`)) {
      this.addEventListener('userInfoChanged', function(newUserInfo) {
        if (this.setData) {
          unifiedPerformanceManager.smartSetData(this, {
            userInfo: newUserInfo,
            hasUserInfo: !!newUserInfo
          })
        }
      }, pageInstance)
    }

    if (!this.listeners.has(`loginStatusChanged_${pageId}`)) {
      this.addEventListener('loginStatusChanged', function(data) {
        if (this.setData) {
          unifiedPerformanceManager.smartSetData(this, {
            userInfo: data.userInfo,
            hasUserInfo: !!data.userInfo
          })
        }
      }, pageInstance)
    }
  }

  /**
   * 添加事件监听器
   */
  addEventListener(event, callback, context = null) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }

    this.listeners.get(event).push({
      callback,
      context
    })
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(event, context) {
    if (!this.listeners.has(event)) {
      return
    }

    const listeners = this.listeners.get(event)
    const filteredListeners = listeners.filter(listener => listener.context !== context)
    this.listeners.set(event, filteredListeners)
  }

  /**
   * 通知监听器
   */
  notifyListeners(event, data) {
    if (!this.listeners.has(event)) {
      return
    }

    const listeners = this.listeners.get(event)
    listeners.forEach(listener => {
      try {
        listener.callback.call(listener.context, data)
      } catch (error) {
      }
    })
  }

  /**
   * 清理页面监听器
   */
  cleanupPage(pageInstance) {
    this.removeEventListener('userInfoChanged', pageInstance)
    this.removeEventListener('loginStatusChanged', pageInstance)
  }
}

// 创建全局单例
const userManager = new UserManager()

export default userManager
