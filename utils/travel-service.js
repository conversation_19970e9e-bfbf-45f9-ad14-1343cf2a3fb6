// utils/travel-service.js
// 旅行规划相关的数据服务

import userManager from './userManager.js'

class TravelService {
  constructor() {
    this.db = wx.cloud.database()
    this.collection = this.db.collection('travel_plans')
  }

  /**
   * 创建旅行计划
   * @param {Object} planData 旅行计划数据
   * @returns {Promise} 创建结果
   */
  async createTravelPlan(planData) {
    try {
      // 获取当前用户信息
      const userInfo = userManager.getUserInfo()
      if (!userInfo || !userInfo.openid) {
        throw new Error('用户未登录')
      }

      // 构建完整的旅行计划数据
      const now = new Date()
      const travelPlan = {
        title: planData.title,
        destination: planData.destination, // 兼容旧格式
        startDate: planData.startDate, // 兼容旧格式
        endDate: planData.endDate, // 兼容旧格式
        duration: this.calculateDuration(planData.startDate, planData.endDate), // 兼容旧格式
        budget: planData.budget || 0, // 兼容旧格式
        participantCount: planData.participantCount || 1, // 兼容旧格式
        notes: planData.notes || '', // 兼容旧格式

        // 新格式的详细数据结构
        destinationDetail: {
          name: planData.destination,
          province: planData.province || '',
          city: planData.city || '',
          coordinates: planData.coordinates || null
        },
        dates: {
          startDate: planData.startDate,
          endDate: planData.endDate,
          duration: this.calculateDuration(planData.startDate, planData.endDate)
        },
        budgetDetail: {
          total: planData.budget || 0,
          spent: 0,
          items: planData.budgetItems || []
        },
        participants: {
          count: planData.participantCount || 1,
          members: []
        },
        status: this.determineInitialStatus(planData.startDate), // 智能状态判断
        isPublic: false,
        itinerary: [], // 行程安排
        records: [], // 旅行记录
        expenses: [], // 兼容旧格式
        createdAt: now, // 兼容旧格式
        updatedAt: now, // 兼容旧格式
        createTime: now.toISOString(),
        updateTime: now.toISOString()
      }

      // 保存到云数据库
      const result = await this.collection.add({
        data: travelPlan
      })

      return {
        success: true,
        data: {
          _id: result._id,
          ...travelPlan
        }
      }
    } catch (error) {
      console.error('创建旅行计划失败:', error)
      return {
        success: false,
        message: error.message || '创建失败，请重试'
      }
    }
  }

  /**
   * {{ AURA-X: Add - 智能状态判断逻辑. Approval: 寸止(ID:1738056000). }}
   * 根据开始日期智能判断初始状态
   * @param {string} startDate 开始日期
   * @returns {string} 状态值
   */
  determineInitialStatus(startDate) {
    if (!startDate) return 'planning'

    const today = new Date()
    const start = new Date(startDate)

    // 今天或之前开始 → ongoing，未来开始 → planning
    return start <= today ? 'ongoing' : 'planning'
  }

  /**
   * 获取用户的旅行计划列表
   * @param {Object} options 查询选项
   * @returns {Promise} 查询结果
   */
  async getTravelPlans(options = {}) {
    try {
      const userInfo = userManager.getUserInfo()
      if (!userInfo || !userInfo.openid) {
        throw new Error('用户未登录')
      }

      const {
        status = null,
        limit = 20,
        page = 1
      } = options

      let query = this.collection.where({
        _openid: userInfo.openid
      })

      if (status) {
        query = query.where({
          status: status
        })
      }

      const result = await query
        .orderBy('createTime', 'desc')
        .skip((page - 1) * limit)
        .limit(limit)
        .get()

      return {
        success: true,
        data: result.data || [],
        total: result.data?.length || 0
      }
    } catch (error) {
      console.error('获取旅行计划失败:', error)
      return {
        success: false,
        message: error.message || '获取失败，请重试'
      }
    }
  }

  /**
   * {{ AURA-X: Modify - 使用云函数支持协作者权限. Approval: 寸止(ID:1738056000). }}
   * 获取单个旅行计划详情
   * @param {string} planId 计划ID
   * @returns {Promise} 查询结果
   */
  async getTravelPlan(planId) {
    try {
      const userInfo = userManager.getUserInfo()
      if (!userInfo || !userInfo.openid) {
        throw new Error('用户未登录')
      }

      // 使用云函数获取计划详情，支持协作者权限验证
      const result = await wx.cloud.callFunction({
        name: 'travel',
        data: {
          action: 'getTravelPlan',
          data: { planId }
        }
      })

      if (result.result && result.result.success) {
        return {
          success: true,
          data: result.result.data
        }
      } else {
        throw new Error(result.result?.message || '获取计划详情失败')
      }
    } catch (error) {
      console.error('获取旅行计划详情失败:', error)
      return {
        success: false,
        message: error.message || '获取失败，请重试'
      }
    }
  }

  /**
   * 更新旅行计划
   * @param {string} planId 计划ID
   * @param {Object} updateData 更新数据
   * @returns {Promise} 更新结果
   */
  async updateTravelPlan(planId, updateData) {
    try {
      const userInfo = userManager.getUserInfo()
      if (!userInfo || !userInfo.openid) {
        throw new Error('用户未登录')
      }

      // 验证所有权
      const planResult = await this.getTravelPlan(planId)
      if (!planResult.success) {
        throw new Error(planResult.message)
      }

      // 更新数据
      const updateFields = {
        ...updateData,
        updateTime: new Date().toISOString()
      }

      await this.collection.doc(planId).update({
        data: updateFields
      })

      return {
        success: true,
        message: '更新成功'
      }
    } catch (error) {
      console.error('更新旅行计划失败:', error)
      return {
        success: false,
        message: error.message || '更新失败，请重试'
      }
    }
  }

  /**
   * 删除旅行计划
   * @param {string} planId 计划ID
   * @returns {Promise} 删除结果
   */
  async deleteTravelPlan(planId) {
    try {
      const userInfo = userManager.getUserInfo()
      if (!userInfo || !userInfo.openid) {
        throw new Error('用户未登录')
      }

      // 验证所有权
      const planResult = await this.getTravelPlan(planId)
      if (!planResult.success) {
        throw new Error(planResult.message)
      }

      // 删除相关的费用记录 - 直接操作数据库
      try {
        const db = wx.cloud.database()
        const deleteResult = await db.collection('records')
          .where({
            planId: planId,
            _openid: userInfo.openid
          })
          .remove()

      } catch (expenseError) {
        console.error('费用记录删除失败:', expenseError)
        // 继续删除计划，但记录错误
      }

      // 删除旅行计划
      await this.collection.doc(planId).remove()

      // 使用数据管理器清理所有相关缓存
      try {
        const dataManager = require('./data-manager.js').default
        dataManager.clearCache()


        // 通知数据变更，触发首页和旅行规划页面的数据刷新
        const app = getApp()
        if (app.globalData && app.globalData.dataChangeNotifier) {
          app.globalData.dataChangeNotifier.notify('travelPlanDeleted', {
            planId: planId,
            timestamp: Date.now()
          })
        }
      } catch (notifyError) {
      }

      return {
        success: true,
        message: '删除成功'
      }
    } catch (error) {
      console.error('删除旅行计划失败:', error)
      return {
        success: false,
        message: error.message || '删除失败，请重试'
      }
    }
  }

  /**
   * 计算旅行天数
   * @param {string} startDate 开始日期
   * @param {string} endDate 结束日期
   * @returns {number} 天数
   */
  calculateDuration(startDate, endDate) {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end - start)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays + 1 // 包含开始和结束日期
  }

  /**
   * 自动更新计划状态
   * @param {string} planId 计划ID
   * @returns {Promise} 更新结果
   */
  async autoUpdatePlanStatus(planId) {
    try {
      const planResult = await this.getTravelPlan(planId)
      if (!planResult.success) {
        return planResult
      }

      const plan = planResult.data
      const today = new Date()
      const startDate = new Date(plan.startDate)
      const endDate = new Date(plan.endDate)

      let newStatus = plan.status

      // 自动状态转换逻辑
      if (plan.status === 'planning' && today >= startDate) {
        newStatus = 'ongoing'
      } else if (plan.status === 'ongoing' && today > endDate) {
        newStatus = 'completed'
      }

      // 如果状态需要更新
      if (newStatus !== plan.status) {
        return await this.updateTravelPlan(planId, { status: newStatus })
      }

      return { success: true, message: '状态无需更新' }
    } catch (error) {
      console.error('自动更新计划状态失败:', error)
      return {
        success: false,
        message: error.message || '状态更新失败'
      }
    }
  }

  /**
   * 批量更新所有计划状态
   * @returns {Promise} 更新结果
   */
  async batchUpdatePlanStatus() {
    try {
      const plansResult = await this.getTravelPlans()
      if (!plansResult.success) {
        return plansResult
      }

      const plans = plansResult.data
      const updatePromises = plans.map(plan => this.autoUpdatePlanStatus(plan._id))

      await Promise.all(updatePromises)

      return { success: true, message: '批量状态更新完成' }
    } catch (error) {
      console.error('批量更新计划状态失败:', error)
      return {
        success: false,
        message: error.message || '批量更新失败'
      }
    }
  }

  /**
   * 验证旅行计划数据
   * @param {Object} planData 计划数据
   * @returns {Object} 验证结果
   */
  validatePlanData(planData) {
    const errors = []

    // 必填字段验证
    if (!planData.title || planData.title.trim() === '') {
      errors.push('请输入旅行标题')
    }

    if (!planData.destination || planData.destination.trim() === '') {
      errors.push('请输入目的地')
    }

    if (!planData.startDate) {
      errors.push('请选择开始日期')
    }

    if (!planData.endDate) {
      errors.push('请选择结束日期')
    }

    // 日期逻辑验证
    if (planData.startDate && planData.endDate) {
      const startDate = new Date(planData.startDate)
      const endDate = new Date(planData.endDate)
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      if (startDate < today) {
        errors.push('开始日期不能早于今天')
      }

      if (endDate < startDate) {
        errors.push('结束日期不能早于开始日期')
      }

      // 限制旅行天数不超过30天
      const duration = this.calculateDuration(planData.startDate, planData.endDate)
      if (duration > 30) {
        errors.push('旅行天数不能超过30天')
      }
    }

    // 预算验证
    if (planData.budget && (planData.budget < 0 || planData.budget > 1000000)) {
      errors.push('预算金额应在0-100万之间')
    }

    // 人数验证
    if (planData.participantCount && (planData.participantCount < 1 || planData.participantCount > 50)) {
      errors.push('参与人数应在1-50人之间')
    }

    // 预算项目验证
    if (planData.budgetItems && planData.budgetItems.length > 0) {
      planData.budgetItems.forEach((item, index) => {
        if (!item.name || item.name.trim() === '') {
          errors.push(`预算项目${index + 1}的名称不能为空`)
        }
        if (item.amount < 0) {
          errors.push(`预算项目"${item.name}"的金额不能为负数`)
        }
      })
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }
}

// 导出单例
const travelService = new TravelService()
export default travelService
