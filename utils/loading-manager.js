/**
 * 统一加载状态管理器 v2.0
 * 解决加载状态不统一问题
 */

import hapticManager from './haptic-manager.js';

class LoadingManager {
  constructor() {
    this.loadingStates = new Map();
    this.loadingQueue = [];
    this.isGlobalLoading = false;
  }
  
  // 显示加载状态
  static show(options = {}) {
    const {
      title = '加载中...',
      mask = true,
      duration = 0,
      key = 'default'
    } = options;
    
    // 记录加载状态
    this.loadingStates.set(key, {
      title,
      mask,
      startTime: Date.now()
    });
    
    // 显示加载
    wx.showLoading({
      title,
      mask
    });
    
    // 自动隐藏
    if (duration > 0) {
      setTimeout(() => {
        this.hide(key);
      }, duration);
    }
    
    this.isGlobalLoading = true;
  }
  
  // 隐藏加载状态
  static hide(key = 'default') {
    if (this.loadingStates.has(key)) {
      this.loadingStates.delete(key);
    }
    
    // 如果没有其他加载状态，隐藏全局加载
    if (this.loadingStates.size === 0) {
      wx.hideLoading();
      this.isGlobalLoading = false;
    }
  }
  
  // 显示成功提示
  static showSuccess(title, duration = 2000) {
    hapticManager.success();
    
    wx.showToast({
      title,
      icon: 'success',
      duration
    });
  }
  
  // 显示错误提示
  static showError(title, duration = 3000) {
    hapticManager.error();
    
    wx.showToast({
      title,
      icon: 'error',
      duration
    });
  }
  
  // 显示警告提示
  static showWarning(title, duration = 2500) {
    hapticManager.warning();
    
    wx.showToast({
      title,
      icon: 'none',
      duration
    });
  }
  
  // 显示信息提示
  static showInfo(title, duration = 2000) {
    wx.showToast({
      title,
      icon: 'none',
      duration
    });
  }
  
  // 显示模态对话框
  static showModal(options = {}) {
    const {
      title = '提示',
      content = '',
      showCancel = true,
      cancelText = '取消',
      confirmText = '确定',
      cancelColor = '#000000',
      confirmColor = '#FF6B6B'
    } = options;
    
    return new Promise((resolve) => {
      wx.showModal({
        title,
        content,
        showCancel,
        cancelText,
        confirmText,
        cancelColor,
        confirmColor,
        success: (res) => {
          if (res.confirm) {
            hapticManager.selection();
          }
          resolve(res);
        },
        fail: () => {
          resolve({ confirm: false, cancel: true });
        }
      });
    });
  }
  
  // 显示操作菜单
  static showActionSheet(options = {}) {
    const {
      itemList = [],
      itemColor = '#000000'
    } = options;
    
    return new Promise((resolve) => {
      wx.showActionSheet({
        itemList,
        itemColor,
        success: (res) => {
          hapticManager.selection();
          resolve(res);
        },
        fail: () => {
          resolve({ cancel: true });
        }
      });
    });
  }
  
  // 显示带进度的加载
  static showProgress(options = {}) {
    const {
      title = '处理中...',
      progress = 0,
      key = 'progress'
    } = options;
    
    // 记录进度加载状态
    this.loadingStates.set(key, {
      title,
      progress,
      startTime: Date.now(),
      type: 'progress'
    });
    
    // 微信小程序不支持进度条，使用普通加载
    wx.showLoading({
      title: `${title} ${Math.round(progress)}%`,
      mask: true
    });
    
    this.isGlobalLoading = true;
  }
  
  // 更新进度
  static updateProgress(progress, key = 'progress') {
    const loadingState = this.loadingStates.get(key);
    if (loadingState && loadingState.type === 'progress') {
      loadingState.progress = progress;
      
      wx.showLoading({
        title: `${loadingState.title} ${Math.round(progress)}%`,
        mask: true
      });
      
      // 进度完成时自动隐藏
      if (progress >= 100) {
        setTimeout(() => {
          this.hide(key);
        }, 500);
      }
    }
  }
  
  // 显示自定义加载动画
  static showCustomLoading(options = {}) {
    const {
      title = '加载中...',
      animation = 'spinner',
      key = 'custom'
    } = options;
    
    // 记录自定义加载状态
    this.loadingStates.set(key, {
      title,
      animation,
      startTime: Date.now(),
      type: 'custom'
    });
    
    // 使用普通加载（微信小程序限制）
    wx.showLoading({
      title,
      mask: true
    });
    
    this.isGlobalLoading = true;
  }
  
  // 获取当前加载状态
  static getLoadingStates() {
    return Array.from(this.loadingStates.entries());
  }
  
  // 清除所有加载状态
  static clearAll() {
    this.loadingStates.clear();
    wx.hideLoading();
    this.isGlobalLoading = false;
  }
  
  // 检查是否正在加载
  static isLoading(key = null) {
    if (key) {
      return this.loadingStates.has(key);
    }
    return this.isGlobalLoading;
  }
  
  // 获取加载时长
  static getLoadingDuration(key = 'default') {
    const loadingState = this.loadingStates.get(key);
    if (loadingState) {
      return Date.now() - loadingState.startTime;
    }
    return 0;
  }
  
  // 批量操作加载状态
  static batch(operations) {
    operations.forEach(operation => {
      const { type, options } = operation;
      if (typeof this[type] === 'function') {
        this[type](options);
      }
    });
  }
}

// 导出单例实例
export default LoadingManager;
