/**
 * 智能同步监听器 - 监听数据变化并智能更新缓存
 * 进一步优化项目性能和数据一致性
 */

import unifiedDataService from './unified-data-service.js'
import unifiedPerformanceManager from './unified-performance-manager.js'

class SmartSyncListener {
  constructor() {
    // 数据变化监听器
    this.listeners = new Map()
    
    // 同步队列
    this.syncQueue = new Set()
    
    // 同步状态
    this.isSyncing = false
    
    // 配置
    this.config = {
      batchSyncDelay: 1000,     // 批量同步延迟1秒
      maxRetries: 3,            // 最大重试次数
      syncTimeout: 5000         // 同步超时5秒
    }
    
    this.init()
  }

  /**
   * 初始化监听器
   */
  init() {
    // 监听页面数据变化
    this.setupPageDataListeners()
    
    // 监听云函数调用结果
    this.setupCloudFunctionListeners()
    
    // 启动批量同步处理
    this.startBatchSync()
  }

  /**
   * 设置页面数据监听器
   */
  setupPageDataListeners() {
    // 监听财务数据变化
    this.addListener('financial_data_changed', async (newData) => {
      await this.handleDataChange('financial', newData)
    })

    // 监听旅行数据变化
    this.addListener('travel_data_changed', async (newData) => {
      await this.handleDataChange('travel', newData)
    })

    // 监听用户数据变化
    this.addListener('user_data_changed', async (newData) => {
      await this.handleDataChange('user', newData)
    })
  }

  /**
   * 设置云函数调用监听器
   */
  setupCloudFunctionListeners() {
    // 拦截云函数调用，监听数据变化
    const originalCallFunction = wx.cloud.callFunction
    
    wx.cloud.callFunction = async (options) => {
      try {
        const result = await originalCallFunction.call(wx.cloud, options)
        
        // 分析返回结果，检测数据变化
        this.analyzeCloudFunctionResult(options, result)
        
        return result
      } catch (error) {
        throw error
      }
    }
  }

  /**
   * 分析云函数调用结果
   */
  analyzeCloudFunctionResult(options, result) {
    const { name, data } = options
    const { action } = data || {}

    // 根据云函数和操作类型判断数据变化
    const dataChangeMap = {
      'expense': {
        'addRecord': 'financial',
        'updateRecord': 'financial',
        'deleteRecord': 'financial',
        'getFinancialOverview': 'financial'
      },
      'travel': {
        'addPlan': 'travel',
        'updatePlan': 'travel',
        'deletePlan': 'travel',
        'getTravelStatistics': 'travel'
      },
      'user': {
        'updateProfile': 'user',
        'updateAvatar': 'user'
      }
    }

    const dataType = dataChangeMap[name]?.[action]
    if (dataType && result.result?.success) {
      this.queueSync(dataType, result.result.data)
    }
  }

  /**
   * 添加监听器
   */
  addListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event).add(callback)
  }

  /**
   * 移除监听器
   */
  removeListener(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback)
    }
  }

  /**
   * 触发事件
   */
  async emit(event, data) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const promises = Array.from(callbacks).map(callback => 
        callback(data).catch(error => 
          console.error(`监听器回调失败 [${event}]:`, error)
        )
      )
      await Promise.allSettled(promises)
    }
  }

  /**
   * 处理数据变化
   */
  async handleDataChange(dataType, newData) {
    try {
      // 使用统一数据服务的智能同步
      await unifiedDataService.smartSync(dataType, newData)
      
      // 触发相关页面更新
      this.notifyPageUpdates(dataType, newData)
      
    } catch (error) {
      console.error(`处理数据变化失败 [${dataType}]:`, error)
    }
  }

  /**
   * 通知页面更新
   */
  notifyPageUpdates(dataType, newData) {
    // 获取当前页面栈
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]

    if (!currentPage) return

    // 根据数据类型更新相应页面
    const updateMap = {
      'financial': () => {
        if (currentPage.route === 'pages/index/index') {
          unifiedPerformanceManager.smartSetData(currentPage, {
            financialOverview: newData,
            _lastUpdate: Date.now()
          })
        }
      },
      'travel': () => {
        if (currentPage.route === 'pages/index/index') {
          unifiedPerformanceManager.smartSetData(currentPage, {
            travelData: newData,
            _lastUpdate: Date.now()
          })
        }
      },
      'user': () => {
        // 更新所有页面的用户信息
        pages.forEach(page => {
          if (page.setData) {
            unifiedPerformanceManager.smartSetData(page, {
              userInfo: newData,
              _lastUpdate: Date.now()
            })
          }
        })
      }
    }

    const updateFn = updateMap[dataType]
    if (updateFn) {
      updateFn()
    }
  }

  /**
   * 队列同步
   */
  queueSync(dataType, data) {
    this.syncQueue.add({ dataType, data, timestamp: Date.now() })
  }

  /**
   * 启动批量同步处理
   */
  startBatchSync() {
    setInterval(() => {
      if (this.syncQueue.size > 0 && !this.isSyncing) {
        this.processSyncQueue()
      }
    }, this.config.batchSyncDelay)
  }

  /**
   * 处理同步队列
   */
  async processSyncQueue() {
    if (this.isSyncing) return

    this.isSyncing = true
    const items = Array.from(this.syncQueue)
    this.syncQueue.clear()

    try {
      // 按数据类型分组
      const groupedItems = items.reduce((groups, item) => {
        if (!groups[item.dataType]) {
          groups[item.dataType] = []
        }
        groups[item.dataType].push(item)
        return groups
      }, {})

      // 并行处理各数据类型
      const syncPromises = Object.entries(groupedItems).map(([dataType, typeItems]) => {
        // 取最新的数据
        const latestItem = typeItems.sort((a, b) => b.timestamp - a.timestamp)[0]
        return this.handleDataChange(dataType, latestItem.data)
      })

      await Promise.allSettled(syncPromises)
      
    } catch (error) {
      console.error('批量同步处理失败:', error)
    } finally {
      this.isSyncing = false
    }
  }

  /**
   * 获取同步状态
   */
  getStatus() {
    return {
      isSyncing: this.isSyncing,
      queueSize: this.syncQueue.size,
      listenersCount: Array.from(this.listeners.values())
        .reduce((total, set) => total + set.size, 0)
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.listeners.clear()
    this.syncQueue.clear()
    this.isSyncing = false
  }
}

// 创建全局实例
const smartSyncListener = new SmartSyncListener()

export default smartSyncListener
