// utils/data-initializer.js
// 数据初始化工具

import dataManager from './data-manager.js'

function DataInitializer() {
  this.initialized = false
}

// 初始化应用数据
DataInitializer.prototype.initializeAppData = function(callback) {
  const self = this

  // 检查是否已经初始化过
  const hasInitialized = wx.getStorageSync('app_data_initialized')
  if (hasInitialized) {
    if (callback) callback(null)
    return
  }

  // 标记为已初始化（不创建示例数据）
  wx.setStorageSync('app_data_initialized', true)
  this.initialized = true

  if (callback) callback(null)
}

// 初始化基础数据结构（不包含示例数据）
DataInitializer.prototype.initializeDataStructure = function(callback) {
  // 确保必要的数据库集合存在
  // 这里可以添加数据库结构初始化逻辑
  if (callback) callback(null)
}

// 重置应用数据
DataInitializer.prototype.resetAppData = function() {
  const self = this
  try {
    wx.showModal({
      title: '确认重置',
      content: '这将清除所有旅行计划数据，确定要继续吗？',
      success: function(res) {
        if (res.confirm) {
          // 清除所有相关数据
          wx.removeStorageSync('travel_plans')
          wx.removeStorageSync('popular_destinations')
          wx.removeStorageSync('recent_destinations')
          wx.removeStorageSync('user_travel_preferences')
          wx.removeStorageSync('app_data_initialized')

          // 重新初始化
          self.initializeAppData().then(function() {
            wx.showToast({
              title: '重置完成',
              icon: 'success'
            })
          }).catch(function(error) {
            wx.showToast({
              title: '重置失败',
              icon: 'none'
            })
          })
        }
      }
    })
  } catch (error) {
    wx.showToast({
      title: '重置失败',
      icon: 'none'
    })
  }
}

// 检查数据完整性
DataInitializer.prototype.checkDataIntegrity = function(callback) {
  const self = this

  // 检查云数据库中的旅行计划数据完整性
  dataManager.getTravelPlans().then(result => {
    if (!result.success) {
      if (callback) callback(new Error('获取旅行计划失败'))
      return
    }

    const plans = result.data || []
    let hasIssues = false

    if (plans.length === 0) {
      if (callback) callback(null, true)
      return
    }

    // 简化数据完整性检查
    for (let i = 0; i < plans.length; i++) {
      const plan = plans[i]

      // 检查必要字段
      if (!plan.status || !plan.createdAt) {
        hasIssues = true
        break
      }
    }

    if (callback) callback(null, !hasIssues)
  }).catch(error => {
    if (callback) callback(error)
  })
}

// 导出数据
DataInitializer.prototype.exportData = function() {
  try {
    const data = {
      plans: wx.getStorageSync('travel_plans') || [],
      recentDestinations: wx.getStorageSync('recent_destinations') || [],
      preferences: wx.getStorageSync('user_travel_preferences') || {},
      exportTime: new Date().toISOString(),
      version: '1.0.0'
    }

    return Promise.resolve(JSON.stringify(data, null, 2))
  } catch (error) {
    console.error('导出数据失败:', error)
    return Promise.reject(error)
  }
}

// 导入数据
DataInitializer.prototype.importData = function(jsonData) {
  try {
    const data = JSON.parse(jsonData)

    if (data.plans) {
      wx.setStorageSync('travel_plans', data.plans)
    }

    if (data.recentDestinations) {
      wx.setStorageSync('recent_destinations', data.recentDestinations)
    }

    if (data.preferences) {
      wx.setStorageSync('user_travel_preferences', data.preferences)
    }

    wx.showToast({
      title: '导入成功',
      icon: 'success'
    })

    return Promise.resolve(true)
  } catch (error) {
    console.error('导入数据失败:', error)
    wx.showToast({
      title: '导入失败',
      icon: 'none'
    })
    return Promise.resolve(false)
  }
}

// 工具方法
DataInitializer.prototype.generateId = function() {
  return 'plan_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 获取数据统计
DataInitializer.prototype.getDataStatistics = function(callback) {
  // 使用数据管理器获取旅行计划
  dataManager.getTravelPlans().then(result => {
    if (!result.success) {
      const defaultStats = {
        totalPlans: 0,
        planningPlans: 0,
        ongoingPlans: 0,
        completedPlans: 0,
        totalExpenses: 0
      }
      if (callback) callback(new Error('获取数据失败'), defaultStats)
      return
    }

    const plans = result.data || []
    const stats = {
      totalPlans: plans.length,
      planningPlans: plans.filter(function(p) { return p.status === 'planned' }).length,
      ongoingPlans: plans.filter(function(p) { return p.status === 'ongoing' }).length,
      completedPlans: plans.filter(function(p) { return p.status === 'completed' }).length,
      totalExpenses: 0 // 简化统计
    }

    if (callback) callback(null, stats)
  }).catch(error => {
    const defaultStats = {
      totalPlans: 0,
      planningPlans: 0,
      ongoingPlans: 0,
      completedPlans: 0,
      totalExpenses: 0
    }
    if (callback) callback(error, defaultStats)
  })
}

// 创建单例实例
const dataInitializer = new DataInitializer()

export default dataInitializer
