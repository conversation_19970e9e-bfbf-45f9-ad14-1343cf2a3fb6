/**
 * 数据库初始化工具
 * 用于检查和创建必要的数据库集合
 */

class DBInitializer {
  constructor() {
    this.db = null
    this.initialized = false
    this.collections = [
      'users',
      'records',
      'travel_plans',
      'categories',
      'destinations'
    ]
  }

  /**
   * 获取数据库实例（延迟初始化）
   */
  getDB() {
    if (!this.db) {
      this.db = wx.cloud.database()
    }
    return this.db
  }

  /**
   * 初始化数据库
   */
  async initialize() {
    if (this.initialized) return true

    try {
      // 由于数据库集合已存在，只需要检查默认数据
      await this.initializeDefaultData()

      this.initialized = true
      return true
    } catch (error) {
      // 即使失败也返回 true，不阻塞应用启动
      this.initialized = true
      return true
    }
  }

  /**
   * 获取已存在的集合
   */
  async getExistingCollections() {
    try {
      // 使用云函数获取集合列表
      const result = await wx.cloud.callFunction({
        name: 'system',
        data: {
          action: 'getCollections'
        }
      })
      
      if (result.result && result.result.collections) {
        return result.result.collections.map(col => col.name)
      }
      
      return []
    } catch (error) {
      console.error('获取集合列表失败:', error)
      return []
    }
  }

  /**
   * 创建缺失的集合
   */
  async createMissingCollections(collections) {
    try {
      // 使用云函数创建集合
      const result = await wx.cloud.callFunction({
        name: 'system',
        data: {
          action: 'createCollections',
          collections: collections
        }
      })
      
      return true
    } catch (error) {
      throw error
    }
  }

  /**
   * 初始化默认数据
   */
  async initializeDefaultData() {
    try {
      const db = this.getDB()

      // 检查分类数据是否存在
      const categoriesResult = await db.collection('categories').count()

      if (categoriesResult.total === 0) {
        await this.initializeCategories()
      }

      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 初始化默认分类数据
   */
  async initializeCategories() {
    try {
      const db = this.getDB()

      const defaultCategories = [
        {
          name: '餐饮',
          icon: '🍽️',
          type: 'expense',
          color: '#FF6B6B',
          isDefault: true,
          createTime: new Date()
        },
        {
          name: '交通',
          icon: '🚗',
          type: 'expense',
          color: '#4ECDC4',
          isDefault: true,
          createTime: new Date()
        },
        {
          name: '购物',
          icon: '🛍️',
          type: 'expense',
          color: '#45B7D1',
          isDefault: true,
          createTime: new Date()
        },
        {
          name: '娱乐',
          icon: '🎮',
          type: 'expense',
          color: '#96CEB4',
          isDefault: true,
          createTime: new Date()
        },
        {
          name: '工资',
          icon: '💰',
          type: 'income',
          color: '#FFEAA7',
          isDefault: true,
          createTime: new Date()
        },
        {
          name: '奖金',
          icon: '🎁',
          type: 'income',
          color: '#DDA0DD',
          isDefault: true,
          createTime: new Date()
        }
      ]

      // 批量添加默认分类
      for (const category of defaultCategories) {
        await db.collection('categories').add({
          data: category
        })
      }

      return true
    } catch (error) {
      return false
    }
  }
}

// 创建单例实例
const dbInitializer = new DBInitializer()

export default dbInitializer
