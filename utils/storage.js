/**
 * 存储管理工具
 * 简化微信小程序存储操作
 */

import { STORAGE_KEYS } from './constants.js'

class Storage {
  // 设置数据
  set(key, value) {
    try {
      wx.setStorageSync(key, value)
      return true
    } catch (error) {
      console.error('存储失败:', error)
      return false
    }
  }

  // 获取数据
  get(key, defaultValue = null) {
    try {
      const value = wx.getStorageSync(key)
      return value || defaultValue
    } catch (error) {
      console.error('读取失败:', error)
      return defaultValue
    }
  }

  // 删除数据
  remove(key) {
    try {
      wx.removeStorageSync(key)
      return true
    } catch (error) {
      console.error('删除失败:', error)
      return false
    }
  }

  // 清空所有数据
  clear() {
    try {
      wx.clearStorageSync()
      return true
    } catch (error) {
      console.error('清空失败:', error)
      return false
    }
  }

  // 用户信息相关
  setUserInfo(userInfo) {
    return this.set(STORAGE_KEYS.USER_INFO, userInfo)
  }

  getUserInfo() {
    return this.get(STORAGE_KEYS.USER_INFO)
  }

  // 主题相关
  setTheme(theme) {
    return this.set(STORAGE_KEYS.THEME, theme)
  }

  getTheme() {
    return this.get(STORAGE_KEYS.THEME, 'light')
  }
}

export default new Storage()
