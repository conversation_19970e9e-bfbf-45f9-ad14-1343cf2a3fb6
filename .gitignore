# 微信小程序项目 .gitignore

# 开发工具生成的文件
.idea/
.vscode/
*.swp
*.swo
*~

# 微信开发者工具生成的文件
project.private.config.json
.cloudbase/

# 依赖包
node_modules/
miniprogram_npm/

# 云开发相关
cloud/functions/*/node_modules/
cloud/functions/*/package-lock.json

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov

# nyc测试覆盖率
.nyc_output

# 依赖目录
jspm_packages/

# 可选的npm缓存目录
.npm

# 可选的REPL历史
.node_repl_history

# 输出的二进制文件
out
dist

# 依赖锁定文件（保留package-lock.json）
# package-lock.json

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 编辑器目录和文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp

# 构建输出
build/
dist/

# 测试覆盖率
coverage/

# 本地配置文件
config.local.js
settings.local.json 